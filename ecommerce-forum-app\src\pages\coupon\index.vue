<template>
  <view class="coupon-container">
    <!-- 优惠券状态筛选 -->
    <view class="coupon-tabs">
      <view 
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        v-for="tab in couponTabs"
        :key="tab.key"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="tab-badge" v-if="tab.count > 0">
          <text class="badge-text">{{ tab.count }}</text>
        </view>
      </view>
    </view>

    <!-- 优惠券列表 -->
    <scroll-view 
      class="coupon-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="refreshCoupons"
      :refresher-triggered="isRefreshing"
    >
      <view class="coupon-list">
        <view 
          class="coupon-item" 
          :class="{ expired: coupon.status === 'expired', used: coupon.status === 'used' }"
          v-for="coupon in filteredCoupons" 
          :key="coupon.id"
        >
          <!-- 优惠券左侧 -->
          <view class="coupon-left">
            <view class="coupon-amount">
              <text class="amount-symbol">¥</text>
              <text class="amount-value">{{ coupon.amount }}</text>
            </view>
            <text class="coupon-condition">{{ coupon.condition }}</text>
          </view>

          <!-- 优惠券右侧 -->
          <view class="coupon-right">
            <view class="coupon-info">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-desc" v-if="coupon.description">{{ coupon.description }}</text>
              <view class="coupon-validity">
                <text class="validity-text">有效期：{{ formatDate(coupon.startTime) }} - {{ formatDate(coupon.endTime) }}</text>
              </view>
              <view class="coupon-scope" v-if="coupon.scope">
                <text class="scope-text">适用范围：{{ coupon.scope }}</text>
              </view>
            </view>

            <!-- 优惠券操作 -->
            <view class="coupon-action">
              <view 
                class="action-btn"
                :class="getActionClass(coupon.status)"
                @click="handleCouponAction(coupon)"
              >
                <text class="btn-text">{{ getActionText(coupon.status) }}</text>
              </view>
            </view>
          </view>

          <!-- 优惠券状态标签 -->
          <view class="coupon-status" v-if="coupon.status !== 'available'">
            <text class="status-text">{{ getStatusText(coupon.status) }}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载更多...</text>
      </view>

      <!-- 无优惠券 -->
      <view class="no-coupons" v-if="filteredCoupons.length === 0 && !isLoading">
        <text class="no-coupons-icon">🎫</text>
        <text class="no-coupons-text">暂无相关优惠券</text>
        <text class="no-coupons-tip">去商城逛逛，领取更多优惠券</text>
        <view class="go-shopping-btn" @click="goShopping">
          <text class="btn-text">去逛逛</text>
        </view>
      </view>
    </scroll-view>

    <!-- 领取优惠券弹窗 -->
    <view class="receive-modal" v-if="showReceiveModal" @click="showReceiveModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">领取优惠券</text>
          <text class="close-btn" @click="showReceiveModal = false">✕</text>
        </view>
        
        <view class="available-coupons">
          <view 
            class="available-item"
            v-for="coupon in availableCoupons"
            :key="coupon.id"
            @click="receiveCoupon(coupon)"
          >
            <view class="coupon-preview">
              <view class="preview-left">
                <text class="preview-amount">¥{{ coupon.amount }}</text>
                <text class="preview-condition">{{ coupon.condition }}</text>
              </view>
              <view class="preview-right">
                <text class="preview-name">{{ coupon.name }}</text>
                <text class="preview-validity">{{ formatDate(coupon.endTime) }}到期</text>
              </view>
            </view>
            <view class="receive-btn">
              <text class="btn-text">立即领取</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface Coupon {
  id: number
  name: string
  description?: string
  amount: number
  condition: string
  startTime: string
  endTime: string
  scope?: string
  status: 'available' | 'used' | 'expired'
  type: 'discount' | 'cash' | 'shipping'
}

// 响应式数据
const activeTab = ref('available')
const isRefreshing = ref(false)
const isLoading = ref(false)
const hasMore = ref(true)
const showReceiveModal = ref(false)

const couponTabs = ref([
  { key: 'available', name: '可使用', count: 3 },
  { key: 'used', name: '已使用', count: 5 },
  { key: 'expired', name: '已过期', count: 2 }
])

const coupons = ref<Coupon[]>([
  {
    id: 1,
    name: '新用户专享券',
    description: '首次购买专享优惠',
    amount: 20,
    condition: '满100元可用',
    startTime: '2024-01-01',
    endTime: '2024-12-31',
    scope: '全场商品',
    status: 'available',
    type: 'cash'
  },
  {
    id: 2,
    name: '数码产品优惠券',
    amount: 50,
    condition: '满300元可用',
    startTime: '2024-01-01',
    endTime: '2024-06-30',
    scope: '数码分类',
    status: 'available',
    type: 'cash'
  },
  {
    id: 3,
    name: '免邮券',
    description: '全场包邮',
    amount: 10,
    condition: '无门槛',
    startTime: '2024-01-01',
    endTime: '2024-03-31',
    scope: '全场商品',
    status: 'available',
    type: 'shipping'
  },
  {
    id: 4,
    name: '生活用品券',
    amount: 30,
    condition: '满200元可用',
    startTime: '2023-12-01',
    endTime: '2023-12-31',
    scope: '生活用品',
    status: 'expired',
    type: 'cash'
  },
  {
    id: 5,
    name: '满减券',
    amount: 15,
    condition: '满80元可用',
    startTime: '2023-11-01',
    endTime: '2023-11-30',
    scope: '全场商品',
    status: 'used',
    type: 'cash'
  }
])

const availableCoupons = ref<Coupon[]>([
  {
    id: 101,
    name: '限时特惠券',
    amount: 25,
    condition: '满150元可用',
    startTime: '2024-01-01',
    endTime: '2024-02-29',
    scope: '全场商品',
    status: 'available',
    type: 'cash'
  },
  {
    id: 102,
    name: '品类专享券',
    amount: 40,
    condition: '满250元可用',
    startTime: '2024-01-01',
    endTime: '2024-03-31',
    scope: '服装鞋包',
    status: 'available',
    type: 'cash'
  }
])

// 计算属性
const filteredCoupons = computed(() => {
  return coupons.value.filter(coupon => coupon.status === activeTab.value)
})

// 方法
const switchTab = (tabKey: string) => {
  activeTab.value = tabKey
}

const refreshCoupons = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1000)
}

const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    used: '已使用',
    expired: '已过期'
  }
  return statusMap[status] || ''
}

const getActionText = (status: string) => {
  const actionMap: Record<string, string> = {
    available: '立即使用',
    used: '已使用',
    expired: '已过期'
  }
  return actionMap[status] || ''
}

const getActionClass = (status: string) => {
  if (status === 'available') return 'primary'
  return 'disabled'
}

const handleCouponAction = (coupon: Coupon) => {
  if (coupon.status === 'available') {
    uni.navigateTo({
      url: `/pages/product/list?couponId=${coupon.id}`
    })
  }
}

const goShopping = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  })
}

const receiveCoupon = (coupon: Coupon) => {
  // 添加到用户优惠券列表
  const newCoupon = { ...coupon, id: Date.now() }
  coupons.value.unshift(newCoupon)
  
  // 更新标签页计数
  couponTabs.value[0].count++
  
  uni.showToast({
    title: '领取成功',
    icon: 'success'
  })
  
  showReceiveModal.value = false
}

onLoad((options) => {
  if (options?.showReceive === 'true') {
    showReceiveModal.value = true
  }
})
</script>

<style lang="scss" scoped>
.coupon-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.coupon-tabs {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  
  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
    position: relative;
    
    &.active {
      border-bottom: 4rpx solid #ff6b35;
      
      .tab-text {
        color: #ff6b35;
        font-weight: 600;
      }
    }
    
    .tab-text {
      font-size: 26rpx;
      color: #666666;
    }
    
    .tab-badge {
      position: absolute;
      top: 16rpx;
      right: 20rpx;
      min-width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ff6b35;
      border-radius: 50%;
      
      .badge-text {
        font-size: 20rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}

.coupon-scroll {
  height: calc(100vh - 120rpx);
}

.coupon-list {
  padding: 20rpx;
  
  .coupon-item {
    display: flex;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    position: relative;
    
    &.expired, &.used {
      opacity: 0.6;
      
      .coupon-left {
        background-color: #d9d9d9;
      }
    }
    
    .coupon-left {
      width: 200rpx;
      background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30rpx 20rpx;
      
      .coupon-amount {
        display: flex;
        align-items: flex-end;
        margin-bottom: 8rpx;
        
        .amount-symbol {
          font-size: 24rpx;
          color: #ffffff;
          margin-right: 4rpx;
        }
        
        .amount-value {
          font-size: 48rpx;
          font-weight: 600;
          color: #ffffff;
        }
      }
      
      .coupon-condition {
        font-size: 20rpx;
        color: rgba(255, 255, 255, 0.9);
        text-align: center;
      }
    }
    
    .coupon-right {
      flex: 1;
      display: flex;
      padding: 30rpx;
      
      .coupon-info {
        flex: 1;
        
        .coupon-name {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }
        
        .coupon-desc {
          display: block;
          font-size: 22rpx;
          color: #666666;
          margin-bottom: 12rpx;
        }
        
        .coupon-validity {
          margin-bottom: 8rpx;
          
          .validity-text {
            font-size: 20rpx;
            color: #999999;
          }
        }
        
        .coupon-scope {
          .scope-text {
            font-size: 20rpx;
            color: #999999;
          }
        }
      }
      
      .coupon-action {
        display: flex;
        align-items: center;
        
        .action-btn {
          padding: 16rpx 24rpx;
          border-radius: 20rpx;
          
          &.primary {
            background-color: #ff6b35;
            
            .btn-text {
              color: #ffffff;
            }
          }
          
          &.disabled {
            background-color: #f8f8f8;
            
            .btn-text {
              color: #999999;
            }
          }
          
          .btn-text {
            font-size: 22rpx;
            font-weight: 600;
          }
        }
      }
    }
    
    .coupon-status {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      padding: 6rpx 12rpx;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 8rpx;
      
      .status-text {
        font-size: 20rpx;
        color: #ffffff;
      }
    }
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .load-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.no-coupons {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  
  .no-coupons-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }
  
  .no-coupons-text {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }
  
  .no-coupons-tip {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .go-shopping-btn {
    padding: 20rpx 40rpx;
    background-color: #ff6b35;
    border-radius: 30rpx;
    
    .btn-text {
      font-size: 26rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

.receive-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  
  .modal-content {
    width: 100%;
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    max-height: 80vh;
    overflow-y: auto;
    
    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;
      
      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }
      
      .close-btn {
        font-size: 32rpx;
        color: #999999;
      }
    }
    
    .available-coupons {
      .available-item {
        display: flex;
        align-items: center;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-bottom: 16rpx;
        
        .coupon-preview {
          flex: 1;
          display: flex;
          align-items: center;
          
          .preview-left {
            margin-right: 20rpx;
            
            .preview-amount {
              display: block;
              font-size: 32rpx;
              font-weight: 600;
              color: #ff6b35;
              margin-bottom: 4rpx;
            }
            
            .preview-condition {
              font-size: 20rpx;
              color: #666666;
            }
          }
          
          .preview-right {
            flex: 1;
            
            .preview-name {
              display: block;
              font-size: 26rpx;
              color: #333333;
              margin-bottom: 8rpx;
            }
            
            .preview-validity {
              font-size: 22rpx;
              color: #999999;
            }
          }
        }
        
        .receive-btn {
          padding: 16rpx 24rpx;
          background-color: #ff6b35;
          border-radius: 20rpx;
          
          .btn-text {
            font-size: 22rpx;
            color: #ffffff;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
