<template>
  <view class="points-mall">
    <!-- 积分余额卡片 -->
    <view class="points-balance-card">
      <view class="balance-info">
        <text class="balance-label">我的积分</text>
        <text class="balance-amount">{{ userPoints }}</text>
      </view>
      <view class="balance-actions">
        <view class="action-btn" @click="goToPointsCenter">
          <text class="btn-text">积分明细</text>
        </view>
        <view class="action-btn primary" @click="goToEarnPoints">
          <text class="btn-text">赚积分</text>
        </view>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-nav">
      <scroll-view class="nav-scroll" scroll-x>
        <view class="nav-list">
          <view 
            class="nav-item"
            :class="{ active: activeCategory === category.key }"
            v-for="category in categories"
            :key="category.key"
            @click="switchCategory(category.key)"
          >
            <text class="nav-icon">{{ category.icon }}</text>
            <text class="nav-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 商品列表 -->
    <scroll-view 
      class="products-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="refreshProducts"
      :refresher-triggered="isRefreshing"
    >
      <view class="products-grid">
        <view 
          class="product-item" 
          v-for="product in filteredProducts" 
          :key="product.id"
          @click="goToProduct(product)"
        >
          <view class="product-image-wrapper">
            <image :src="product.image" class="product-image" mode="aspectFill" />
            <view class="stock-badge" v-if="product.stock <= 10">
              <text class="badge-text">仅剩{{ product.stock }}件</text>
            </view>
          </view>
          
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc" v-if="product.description">{{ product.description }}</text>
            
            <view class="product-price">
              <view class="points-price">
                <text class="points-icon">💎</text>
                <text class="points-amount">{{ product.pointsPrice }}</text>
                <text class="points-unit">积分</text>
              </view>
              <text class="original-price" v-if="product.originalPrice">
                原价¥{{ product.originalPrice }}
              </text>
            </view>
            
            <view class="product-stats">
              <text class="exchange-count">已兑换{{ product.exchangeCount }}</text>
              <view class="rating" v-if="product.rating">
                <text class="rating-text">{{ product.rating }}分</text>
              </view>
            </view>
          </view>
          
          <view class="exchange-btn" :class="{ disabled: userPoints < product.pointsPrice }">
            <text class="btn-text">
              {{ userPoints >= product.pointsPrice ? '立即兑换' : '积分不足' }}
            </text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载更多...</text>
      </view>

      <!-- 无商品 -->
      <view class="no-products" v-if="filteredProducts.length === 0 && !isLoading">
        <text class="no-products-icon">🎁</text>
        <text class="no-products-text">暂无兑换商品</text>
        <text class="no-products-tip">敬请期待更多好礼</text>
      </view>
    </scroll-view>

    <!-- 兑换确认弹窗 -->
    <view class="exchange-modal" v-if="showExchangeModal" @click="showExchangeModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">确认兑换</text>
          <text class="close-btn" @click="showExchangeModal = false">✕</text>
        </view>
        
        <view class="product-preview" v-if="selectedProduct">
          <image :src="selectedProduct.image" class="preview-image" mode="aspectFill" />
          <view class="preview-info">
            <text class="preview-name">{{ selectedProduct.name }}</text>
            <view class="preview-price">
              <text class="points-icon">💎</text>
              <text class="points-amount">{{ selectedProduct.pointsPrice }}</text>
              <text class="points-unit">积分</text>
            </view>
          </view>
        </view>
        
        <view class="exchange-info">
          <view class="info-row">
            <text class="info-label">当前积分：</text>
            <text class="info-value">{{ userPoints }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">兑换消耗：</text>
            <text class="info-value points">{{ selectedProduct?.pointsPrice || 0 }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">剩余积分：</text>
            <text class="info-value">{{ userPoints - (selectedProduct?.pointsPrice || 0) }}</text>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="cancel-btn" @click="showExchangeModal = false">
            <text class="btn-text">取消</text>
          </view>
          <view class="confirm-btn" @click="confirmExchange">
            <text class="btn-text">确认兑换</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface Product {
  id: number
  name: string
  description?: string
  image: string
  pointsPrice: number
  originalPrice?: number
  stock: number
  exchangeCount: number
  rating?: number
  category: string
}

interface Category {
  key: string
  name: string
  icon: string
}

// 响应式数据
const userPoints = ref(2580)
const activeCategory = ref('all')
const isRefreshing = ref(false)
const isLoading = ref(false)
const hasMore = ref(true)
const showExchangeModal = ref(false)
const selectedProduct = ref<Product | null>(null)

const categories = ref<Category[]>([
  { key: 'all', name: '全部', icon: '🎁' },
  { key: 'digital', name: '数码', icon: '📱' },
  { key: 'life', name: '生活', icon: '🏠' },
  { key: 'food', name: '美食', icon: '🍔' },
  { key: 'coupon', name: '优惠券', icon: '🎫' },
  { key: 'virtual', name: '虚拟', icon: '💎' }
])

const products = ref<Product[]>([
  {
    id: 1,
    name: '蓝牙耳机',
    description: '高音质无线耳机',
    image: 'https://via.placeholder.com/300x300/ff6b35/ffffff?text=耳机',
    pointsPrice: 1500,
    originalPrice: 199,
    stock: 50,
    exchangeCount: 128,
    rating: 4.8,
    category: 'digital'
  },
  {
    id: 2,
    name: '保温杯',
    description: '316不锈钢保温杯',
    image: 'https://via.placeholder.com/300x300/1890ff/ffffff?text=保温杯',
    pointsPrice: 800,
    originalPrice: 89,
    stock: 8,
    exchangeCount: 256,
    rating: 4.6,
    category: 'life'
  },
  {
    id: 3,
    name: '星巴克咖啡券',
    description: '中杯任意饮品券',
    image: 'https://via.placeholder.com/300x300/52c41a/ffffff?text=咖啡券',
    pointsPrice: 300,
    originalPrice: 35,
    stock: 100,
    exchangeCount: 512,
    category: 'coupon'
  },
  {
    id: 4,
    name: '手机支架',
    description: '桌面手机支架',
    image: 'https://via.placeholder.com/300x300/faad14/ffffff?text=支架',
    pointsPrice: 200,
    originalPrice: 29,
    stock: 30,
    exchangeCount: 89,
    rating: 4.5,
    category: 'digital'
  },
  {
    id: 5,
    name: '论坛VIP月卡',
    description: '享受专属特权',
    image: 'https://via.placeholder.com/300x300/722ed1/ffffff?text=VIP',
    pointsPrice: 500,
    stock: 999,
    exchangeCount: 1024,
    category: 'virtual'
  },
  {
    id: 6,
    name: '零食大礼包',
    description: '精选进口零食',
    image: 'https://via.placeholder.com/300x300/eb2f96/ffffff?text=零食',
    pointsPrice: 1200,
    originalPrice: 158,
    stock: 25,
    exchangeCount: 67,
    rating: 4.7,
    category: 'food'
  }
])

// 计算属性
const filteredProducts = computed(() => {
  if (activeCategory.value === 'all') {
    return products.value
  }
  return products.value.filter(product => product.category === activeCategory.value)
})

// 方法
const goToPointsCenter = () => {
  uni.navigateTo({
    url: '/pages/points/index'
  })
}

const goToEarnPoints = () => {
  uni.navigateTo({
    url: '/pages/points/earn'
  })
}

const switchCategory = (categoryKey: string) => {
  activeCategory.value = categoryKey
}

const refreshProducts = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1000)
}

const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
}

const goToProduct = (product: Product) => {
  if (userPoints.value < product.pointsPrice) {
    uni.showToast({
      title: '积分不足',
      icon: 'none'
    })
    return
  }
  
  selectedProduct.value = product
  showExchangeModal.value = true
}

const confirmExchange = () => {
  if (!selectedProduct.value) return
  
  if (userPoints.value < selectedProduct.value.pointsPrice) {
    uni.showToast({
      title: '积分不足',
      icon: 'none'
    })
    return
  }
  
  // 扣除积分
  userPoints.value -= selectedProduct.value.pointsPrice
  
  // 增加兑换次数
  selectedProduct.value.exchangeCount++
  
  // 减少库存
  selectedProduct.value.stock--
  
  showExchangeModal.value = false
  selectedProduct.value = null
  
  uni.showToast({
    title: '兑换成功',
    icon: 'success'
  })
  
  // 跳转到兑换记录
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/points/records?type=exchange'
    })
  }, 1500)
}

onLoad(() => {
  // 页面加载时的初始化逻辑
})
</script>

<style lang="scss" scoped>
.points-mall {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.points-balance-card {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .balance-info {
    .balance-label {
      display: block;
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8rpx;
    }

    .balance-amount {
      font-size: 48rpx;
      font-weight: 600;
      color: #ffffff;
    }
  }

  .balance-actions {
    display: flex;
    gap: 16rpx;

    .action-btn {
      padding: 16rpx 24rpx;
      border-radius: 20rpx;
      border: 1rpx solid rgba(255, 255, 255, 0.3);

      &.primary {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .btn-text {
        font-size: 24rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}

.category-nav {
  background-color: #ffffff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;

  .nav-scroll {
    white-space: nowrap;
  }

  .nav-list {
    display: flex;
    padding: 0 30rpx;
    gap: 40rpx;

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 80rpx;

      &.active {
        .nav-icon {
          background-color: #fff7f0;
        }

        .nav-text {
          color: #ff6b35;
          font-weight: 600;
        }
      }

      .nav-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        background-color: #f8f8f8;
        border-radius: 50%;
        margin-bottom: 12rpx;
      }

      .nav-text {
        font-size: 22rpx;
        color: #666666;
      }
    }
  }
}

.products-scroll {
  height: calc(100vh - 320rpx);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 20rpx 20rpx;

  .product-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    overflow: hidden;

    .product-image-wrapper {
      position: relative;

      .product-image {
        width: 100%;
        height: 300rpx;
      }

      .stock-badge {
        position: absolute;
        top: 16rpx;
        right: 16rpx;
        background-color: rgba(255, 107, 53, 0.9);
        border-radius: 12rpx;
        padding: 6rpx 12rpx;

        .badge-text {
          font-size: 20rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }

    .product-info {
      padding: 20rpx;

      .product-name {
        display: block;
        font-size: 26rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .product-desc {
        display: block;
        font-size: 22rpx;
        color: #666666;
        margin-bottom: 16rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .product-price {
        margin-bottom: 12rpx;

        .points-price {
          display: flex;
          align-items: center;
          margin-bottom: 6rpx;

          .points-icon {
            font-size: 24rpx;
            margin-right: 6rpx;
          }

          .points-amount {
            font-size: 28rpx;
            font-weight: 600;
            color: #ff6b35;
            margin-right: 4rpx;
          }

          .points-unit {
            font-size: 22rpx;
            color: #ff6b35;
          }
        }

        .original-price {
          font-size: 20rpx;
          color: #999999;
          text-decoration: line-through;
        }
      }

      .product-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .exchange-count {
          font-size: 20rpx;
          color: #999999;
        }

        .rating {
          .rating-text {
            font-size: 20rpx;
            color: #faad14;
          }
        }
      }
    }

    .exchange-btn {
      margin: 0 20rpx 20rpx;
      padding: 20rpx;
      background-color: #ff6b35;
      border-radius: 8rpx;
      text-align: center;

      &.disabled {
        background-color: #d9d9d9;
      }

      .btn-text {
        font-size: 26rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;

  .load-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.no-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .no-products-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }

  .no-products-text {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }

  .no-products-tip {
    font-size: 24rpx;
    color: #999999;
  }
}

.exchange-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    width: 600rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }

      .close-btn {
        font-size: 32rpx;
        color: #999999;
      }
    }

    .product-preview {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      margin-bottom: 30rpx;

      .preview-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .preview-info {
        flex: 1;

        .preview-name {
          display: block;
          font-size: 26rpx;
          color: #333333;
          margin-bottom: 12rpx;
        }

        .preview-price {
          display: flex;
          align-items: center;

          .points-icon {
            font-size: 24rpx;
            margin-right: 6rpx;
          }

          .points-amount {
            font-size: 28rpx;
            font-weight: 600;
            color: #ff6b35;
            margin-right: 4rpx;
          }

          .points-unit {
            font-size: 22rpx;
            color: #ff6b35;
          }
        }
      }
    }

    .exchange-info {
      margin-bottom: 40rpx;

      .info-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .info-label {
          font-size: 26rpx;
          color: #666666;
        }

        .info-value {
          font-size: 26rpx;
          color: #333333;
          font-weight: 600;

          &.points {
            color: #ff6b35;
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 20rpx;

      .cancel-btn, .confirm-btn {
        flex: 1;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40rpx;

        .btn-text {
          font-size: 28rpx;
          font-weight: 600;
        }
      }

      .cancel-btn {
        background-color: #f8f8f8;
        border: 1rpx solid #d9d9d9;

        .btn-text {
          color: #666666;
        }
      }

      .confirm-btn {
        background-color: #ff6b35;

        .btn-text {
          color: #ffffff;
        }
      }
    }
  }
}
</style>
