{"version": 3, "file": "SourceCode.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/SourceCode.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC7D,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAErC,OAAO,OAAO,UAAU;IACtB;;;;;OAKG;IACH,oBAAoB,CAClB,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,GACpC,OAAO;IACV;;;;OAIG;IACH,gBAAgB,CACd,WAAW,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,GAC1C,QAAQ,CAAC,OAAO,EAAE;IACrB;;;;OAIG;IACH,iBAAiB,CACf,WAAW,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,GAC1C,QAAQ,CAAC,OAAO,EAAE;IACrB;;;;OAIG;IACH,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE;IAC1D;;;;;OAKG;IACH,aAAa,CAAC,CAAC,SAAS,UAAU,CAAC,qBAAqB,EACtD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;;;OAMG;IACH,oBAAoB,CAAC,CAAC,SAAS,UAAU,CAAC,qBAAqB,EAC7D,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;OAIG;IACH,cAAc,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EACxD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;;;OAMG;IACH,qBAAqB,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EAC/D,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;;OAKG;IACH,YAAY,CAAC,CAAC,SAAS,UAAU,CAAC,qBAAqB,EACrD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;;;OAMG;IACH,mBAAmB,CAAC,CAAC,SAAS,UAAU,CAAC,qBAAqB,EAC5D,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;OAIG;IACH,aAAa,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EACvD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;;;OAMG;IACH,oBAAoB,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EAC9D,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;;OAKG;IACH,aAAa,CAAC,CAAC,SAAS,UAAU,CAAC,qBAAqB,EACtD,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;;OAKG;IACH,cAAc,CAAC,CAAC,SAAS,UAAU,CAAC,qBAAqB,EACvD,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;;OAKG;IACH,oBAAoB,CAAC,CAAC,SAAS;QAAE,eAAe,CAAC,EAAE,OAAO,CAAA;KAAE,EAC1D,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,CAAC,GACV,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7C;;;;;;OAMG;IACH,SAAS,CACP,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,WAAW,CAAC,EAAE,MAAM,EACpB,UAAU,CAAC,EAAE,MAAM,GAClB,QAAQ,CAAC,KAAK,EAAE;IACnB;;;;;OAKG;IACH,SAAS,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EACnD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,OAAO,EAAE,CAAC,GACT,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;OAIG;IACH,cAAc,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EACxD,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,OAAO,CAAC,EAAE,CAAC,GAAG,MAAM,GACnB,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;OAIG;IACH,eAAe,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EACzD,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,OAAO,CAAC,EAAE,CAAC,GAAG,MAAM,GACnB,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxC;;;;;;OAMG;IACH,gBAAgB,CAAC,CAAC,SAAS,UAAU,CAAC,sBAAsB,EAC1D,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,OAAO,CAAC,EAAE,CAAC,GAAG,MAAM,GACnB,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;CACzC;AAED,OAAO,OAAO,cAAe,SAAQ,UAAU;IAC7C;;;OAGG;gBACS,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,OAAO;IACjD;;;OAGG;gBACS,MAAM,EAAE,UAAU,CAAC,gBAAgB;IAE/C;;OAEG;IACH,GAAG,EAAE,UAAU,CAAC,OAAO,CAAC;IACxB;;;OAGG;IACH,cAAc,IAAI,QAAQ,CAAC,OAAO,EAAE;IACpC;;;;OAIG;IACH,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,GAAG,MAAM;IACpD;;;OAGG;IACH,QAAQ,IAAI,MAAM,EAAE;IACpB;;;;OAIG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,QAAQ,CAAC,QAAQ;IACjD;;;;OAIG;IACH,mBAAmB,CAAC,KAAK,EAAE,MAAM,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxD;;;;;;OAMG;IACH,OAAO,CACL,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,WAAW,CAAC,EAAE,MAAM,EACpB,UAAU,CAAC,EAAE,MAAM,GAClB,MAAM;IACT;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC;IAChB;;;;;;;OAOG;IACH,cAAc,CACZ,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EACrC,MAAM,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,GACrC,OAAO;IACV;;;;;;;;;;;OAWG;IACH,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,KAAK,GAAG,OAAO;IAC5E;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK;IAC1C;;;;OAIG;IACH,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;IAClD;;;OAGG;IACH,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC,QAAQ,EAAE;IACpE;;;OAGG;IACH,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,OAAO;IAC9D;;;OAGG;IACH,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB;;OAEG;IACH,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B;;OAEG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACzC;;OAEG;IACH,YAAY,EAAE,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;IACxC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,iBAAiB,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;IACpC;;OAEG;IACH,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC;IAMpC;;;;OAIG;IACH,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;CAC1C;AAED,kBAAU,UAAU,CAAC;IACnB,UAAiB,OAAQ,SAAQ,QAAQ,CAAC,OAAO;QAC/C,QAAQ,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC7B,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;KAC1B;IAED,UAAiB,gBAAgB;QAC/B;;WAEG;QACH,GAAG,EAAE,OAAO,CAAC;QACb;;WAEG;QACH,cAAc,EAAE,cAAc,GAAG,IAAI,CAAC;QACtC;;WAEG;QACH,YAAY,EAAE,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;QACxC;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;WAEG;QACH,WAAW,EAAE,WAAW,GAAG,IAAI,CAAC;KACjC;IAED,KAAY,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;IAE7C,KAAY,eAAe,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,KAAK,OAAO,CAAC;IACjE,KAAY,kBAAkB,CAAC,MAAM,EAAE,OAAO,IAG5C,MAAM,SAAS,CAAC,CACd,KAAK,EAAE,QAAQ,CAAC,KAAK,KAClB,KAAK,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,KAAK,CAAC,GACzC,CAAC,GACD,OAAO,CAAC;IACd,KAAY,6BAA6B,CAAC,OAAO,EAAE,OAAO,IACxD,OAAO,SAAS;QAAE,MAAM,CAAC,EAAE,eAAe,CAAA;KAAE,GACxC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,GAC9C,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3C,KAAY,qBAAqB,CAAC,CAAC,IAAI,CAAC,SAAS;QAAE,eAAe,EAAE,IAAI,CAAA;KAAE,GACtE,6BAA6B,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,GAChD,6BAA6B,CAC3B,CAAC,EACD,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC1C,CAAC;IAEN,KAAY,qBAAqB,GAC7B,eAAe,GACf,MAAM,GACN;QACE;;WAEG;QACH,MAAM,CAAC,EAAE,eAAe,CAAC;QACzB;;WAEG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAC1B;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,CAAC;IAEN,KAAY,sBAAsB,GAC9B,eAAe,GACf,MAAM,GACN;QACE;;WAEG;QACH,MAAM,CAAC,EAAE,eAAe,CAAC;QACzB;;WAEG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAC1B;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,CAAC;CACP;;AAED,cAAM,UAAW,SAAQ,eAA2C;CAAG;AAEvE,OAAO,EAAE,UAAU,EAAE,CAAC"}