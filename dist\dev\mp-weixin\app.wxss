view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}
page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
  background-color: #f8f8f8;
}
.flex {
  display: flex;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.m-0 {
  margin: 0;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}
.m-xs {
  margin: 10rpx;
}
.mt-xs {
  margin-top: 10rpx;
}
.mr-xs {
  margin-right: 10rpx;
}
.mb-xs {
  margin-bottom: 10rpx;
}
.ml-xs {
  margin-left: 10rpx;
}
.m-sm {
  margin: 20rpx;
}
.mt-sm {
  margin-top: 20rpx;
}
.mr-sm {
  margin-right: 20rpx;
}
.mb-sm {
  margin-bottom: 20rpx;
}
.ml-sm {
  margin-left: 20rpx;
}
.m-base {
  margin: 30rpx;
}
.mt-base {
  margin-top: 30rpx;
}
.mr-base {
  margin-right: 30rpx;
}
.mb-base {
  margin-bottom: 30rpx;
}
.ml-base {
  margin-left: 30rpx;
}
.p-0 {
  padding: 0;
}
.pt-0 {
  padding-top: 0;
}
.pr-0 {
  padding-right: 0;
}
.pb-0 {
  padding-bottom: 0;
}
.pl-0 {
  padding-left: 0;
}
.p-xs {
  padding: 10rpx;
}
.pt-xs {
  padding-top: 10rpx;
}
.pr-xs {
  padding-right: 10rpx;
}
.pb-xs {
  padding-bottom: 10rpx;
}
.pl-xs {
  padding-left: 10rpx;
}
.p-sm {
  padding: 20rpx;
}
.pt-sm {
  padding-top: 20rpx;
}
.pr-sm {
  padding-right: 20rpx;
}
.pb-sm {
  padding-bottom: 20rpx;
}
.pl-sm {
  padding-left: 20rpx;
}
.p-base {
  padding: 30rpx;
}
.pt-base {
  padding-top: 30rpx;
}
.pr-base {
  padding-right: 30rpx;
}
.pb-base {
  padding-bottom: 30rpx;
}
.pl-base {
  padding-left: 30rpx;
}
.text-primary {
  color: #007aff;
}
.text-secondary {
  color: #ff4757;
}
.text-success {
  color: #2ed573;
}
.text-warning {
  color: #ffa502;
}
.text-error {
  color: #ff4757;
}
.text-info {
  color: #3742fa;
}
.bg-primary {
  background-color: #007aff;
}
.bg-secondary {
  background-color: #ff4757;
}
.bg-success {
  background-color: #2ed573;
}
.bg-warning {
  background-color: #ffa502;
}
.bg-error {
  background-color: #ff4757;
}
.bg-info {
  background-color: #3742fa;
}
.rounded-sm {
  border-radius: 10rpx;
}
.rounded {
  border-radius: 20rpx;
}
.rounded-lg {
  border-radius: 30rpx;
}
.rounded-full {
  border-radius: 50%;
}
.shadow-light {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.shadow {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}
.shadow-dark {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}
.app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 全局样式优化 */
page {
  font-size: 28rpx;
  line-height: 1.6;
  background-color: #f8f9fa;
}

/* 微信小程序 tabBar 样式优化 - 增大尺寸 */
.wx-tab-bar {
  height: 160rpx !important;
  padding: 20rpx 0 !important;
  background: #ffffff !important;
  border-top: 1rpx solid #e5e5e5 !important;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1) !important;
}
.wx-tab-bar-item {
  font-size: 32rpx !important;
  font-weight: 500 !important;
  padding: 16rpx 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

/* tabBar 图标优化 */
.wx-tab-bar-item .wx-tab-bar-item-icon {
  width: 72rpx !important;
  height: 72rpx !important;
  font-size: 72rpx !important;
  margin-bottom: 12rpx !important;
}
.wx-tab-bar-item .wx-tab-bar-item-text {
  font-size: 32rpx !important;
  font-weight: 500 !important;
  line-height: 1.2 !important;
}

/* 选中状态优化 */
.wx-tab-bar-item.wx-tab-bar-item-active .wx-tab-bar-item-icon {
  transform: scale(1.1) !important;
  transition: transform 0.2s ease !important;
}
.wx-tab-bar-item.wx-tab-bar-item-active .wx-tab-bar-item-text {
  font-weight: bold !important;
}

/* 针对页面内容的底部边距，避免被 tabBar 遮挡 */
.page-content {
  padding-bottom: 140rpx;
}

/* uni-app tabBar 全局样式优化 - 仿照腾讯理财通底部栏样式 */
 .uni-tabbar {
  height: 120rpx !important;
  padding: 12rpx 0 !important;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom)) !important;
  background: #ffffff !important;
  border-top: none !important;
  box-shadow: none !important;
  position: relative !important;
}

/* 添加顶部分割线 - 仿理财通样式 */
 .uni-tabbar::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 280rpx !important;
  height: 6rpx !important;
  background: #000000 !important;
  border-radius: 3rpx !important;
}
 .uni-tabbar .uni-tabbar-item {
  height: 96rpx !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8rpx 0 !important;
  position: relative !important;
}
 .uni-tabbar .uni-tabbar-item .uni-tabbar-item-icon {
  width: 44rpx !important;
  height: 44rpx !important;
  font-size: 44rpx !important;
  margin-bottom: 6rpx !important;
  margin-top: 0 !important;
  opacity: 0.6 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 支持emoji图标显示 */
 .uni-tabbar .uni-tabbar-item .uni-tabbar-item-icon::before {
  content: attr(data-icon) !important;
  font-size: 40rpx !important;
  line-height: 1 !important;
}

/* CSS图标样式 - 替代emoji */
 .uni-tabbar .uni-tabbar-item .uni-tabbar-item-icon {
  position: relative !important;
  background: none !important;
}
 .uni-tabbar .uni-tabbar-item .uni-tabbar-item-icon::before {
  content: "" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 32rpx !important;
  height: 32rpx !important;
  background: #999999 !important;
  transition: all 0.2s ease !important;
}

/* 首页图标 - 房子形状 */
 .uni-tabbar .uni-tabbar-item:nth-child(1) .uni-tabbar-item-icon::before {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%) !important;
}

/* 理财图标 - 圆形 */
 .uni-tabbar .uni-tabbar-item:nth-child(2) .uni-tabbar-item-icon::before {
  border-radius: 50% !important;
  border: 4rpx solid #999999 !important;
  background: transparent !important;
}

/* 基金图标 - 上升箭头 */
 .uni-tabbar .uni-tabbar-item:nth-child(3) .uni-tabbar-item-icon::before {
  clip-path: polygon(50% 0%, 0% 50%, 30% 50%, 30% 100%, 70% 100%, 70% 50%, 100% 50%) !important;
}

/* 我的图标 - 人形 */
 .uni-tabbar .uni-tabbar-item:nth-child(4) .uni-tabbar-item-icon::before {
  border-radius: 50% 50% 0 0 !important;
}

/* 选中状态的图标颜色 */
 .uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-icon::before {
  background: #1677ff !important;
}
 .uni-tabbar .uni-tabbar-item.uni-tabbar-item-active:nth-child(2) .uni-tabbar-item-icon::before {
  border-color: #1677ff !important;
}
 .uni-tabbar .uni-tabbar-item .uni-tabbar-item-text {
  font-size: 24rpx !important;
  font-weight: 400 !important;
  margin-top: 0 !important;
  line-height: 1.2 !important;
  color: #999999 !important;
  transition: all 0.2s ease !important;
}

/* 选中状态优化 - 理财通风格 */
 .uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-icon {
  opacity: 1 !important;
  color: #1677ff !important;
  transform: none !important;
}
 .uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-text {
  font-weight: 500 !important;
  color: #1677ff !important;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}