<template>
  <view class="address-edit-container">
    <!-- 地址表单 -->
    <view class="address-form">
      <!-- 联系人信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">联系人信息</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">收货人</text>
          <input 
            type="text" 
            v-model="formData.name" 
            placeholder="请输入收货人姓名"
            class="item-input"
          />
        </view>
        
        <view class="form-item">
          <text class="item-label">手机号</text>
          <input 
            type="number" 
            v-model="formData.phone" 
            placeholder="请输入手机号"
            class="item-input"
            maxlength="11"
          />
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">地址信息</text>
        </view>
        
        <view class="form-item picker-item" @click="showRegionPicker">
          <text class="item-label">所在地区</text>
          <view class="picker-content">
            <text class="picker-text" :class="{ placeholder: !selectedRegion }">
              {{ selectedRegion || '请选择省市区' }}
            </text>
            <text class="picker-arrow">></text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">详细地址</text>
          <textarea 
            v-model="formData.detail" 
            placeholder="请输入详细地址（街道、门牌号等）"
            class="item-textarea"
            maxlength="100"
          />
        </view>
      </view>

      <!-- 地址标签 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">地址标签</text>
        </view>
        
        <view class="tag-list">
          <view 
            class="tag-item"
            :class="{ active: formData.tag === tag }"
            v-for="tag in addressTags"
            :key="tag"
            @click="selectTag(tag)"
          >
            <text class="tag-text">{{ tag }}</text>
          </view>
          <view class="tag-item custom" @click="showCustomTag">
            <text class="tag-text">自定义</text>
          </view>
        </view>
        
        <view class="custom-tag-input" v-if="showCustomInput">
          <input 
            type="text" 
            v-model="customTagValue" 
            placeholder="请输入自定义标签"
            class="custom-input"
            maxlength="10"
            @blur="confirmCustomTag"
          />
        </view>
      </view>

      <!-- 默认地址设置 -->
      <view class="form-section">
        <view class="form-item switch-item">
          <text class="item-label">设为默认地址</text>
          <switch 
            :checked="formData.isDefault" 
            @change="toggleDefault"
            color="#ff6b35"
          />
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn" :class="{ disabled: !canSave }" @click="saveAddress">
      <text class="btn-text">保存地址</text>
    </view>

    <!-- 地区选择器 -->
    <picker-view 
      class="region-picker" 
      v-if="showPicker"
      :value="pickerValue"
      @change="onPickerChange"
    >
      <picker-view-column>
        <view v-for="(province, index) in provinces" :key="index">
          <text>{{ province.name }}</text>
        </view>
      </picker-view-column>
      <picker-view-column>
        <view v-for="(city, index) in cities" :key="index">
          <text>{{ city.name }}</text>
        </view>
      </picker-view-column>
      <picker-view-column>
        <view v-for="(district, index) in districts" :key="index">
          <text>{{ district.name }}</text>
        </view>
      </picker-view-column>
    </picker-view>

    <!-- 地区选择遮罩 -->
    <view class="picker-mask" v-if="showPicker" @click="hidePicker">
      <view class="picker-container" @click.stop>
        <view class="picker-header">
          <text class="cancel-btn" @click="hidePicker">取消</text>
          <text class="picker-title">选择地区</text>
          <text class="confirm-btn" @click="confirmRegion">确定</text>
        </view>
        <picker-view 
          class="picker-content"
          :value="pickerValue"
          @change="onPickerChange"
        >
          <picker-view-column>
            <view v-for="(province, index) in provinces" :key="index" class="picker-item">
              <text>{{ province.name }}</text>
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(city, index) in cities" :key="index" class="picker-item">
              <text>{{ city.name }}</text>
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(district, index) in districts" :key="index" class="picker-item">
              <text>{{ district.name }}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface FormData {
  id?: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  tag: string
  isDefault: boolean
}

interface Region {
  name: string
  code: string
  children?: Region[]
}

// 响应式数据
const isEdit = ref(false)
const showPicker = ref(false)
const showCustomInput = ref(false)
const customTagValue = ref('')
const pickerValue = ref([0, 0, 0])

const formData = ref<FormData>({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  detail: '',
  tag: '',
  isDefault: false
})

const addressTags = ref(['家', '公司', '学校'])

// 模拟地区数据
const provinces = ref<Region[]>([
  {
    name: '北京市',
    code: '110000',
    children: [
      {
        name: '北京市',
        code: '110100',
        children: [
          { name: '东城区', code: '110101' },
          { name: '西城区', code: '110102' },
          { name: '朝阳区', code: '110105' },
          { name: '丰台区', code: '110106' }
        ]
      }
    ]
  },
  {
    name: '上海市',
    code: '310000',
    children: [
      {
        name: '上海市',
        code: '310100',
        children: [
          { name: '黄浦区', code: '310101' },
          { name: '徐汇区', code: '310104' },
          { name: '长宁区', code: '310105' },
          { name: '静安区', code: '310106' }
        ]
      }
    ]
  },
  {
    name: '广东省',
    code: '440000',
    children: [
      {
        name: '广州市',
        code: '440100',
        children: [
          { name: '荔湾区', code: '440103' },
          { name: '越秀区', code: '440104' },
          { name: '海珠区', code: '440105' }
        ]
      },
      {
        name: '深圳市',
        code: '440300',
        children: [
          { name: '罗湖区', code: '440303' },
          { name: '福田区', code: '440304' },
          { name: '南山区', code: '440305' }
        ]
      }
    ]
  }
])

// 计算属性
const cities = computed(() => {
  const province = provinces.value[pickerValue.value[0]]
  return province?.children || []
})

const districts = computed(() => {
  const city = cities.value[pickerValue.value[1]]
  return city?.children || []
})

const selectedRegion = computed(() => {
  if (formData.value.province && formData.value.city && formData.value.district) {
    return `${formData.value.province} ${formData.value.city} ${formData.value.district}`
  }
  return ''
})

const canSave = computed(() => {
  return formData.value.name.trim() && 
         formData.value.phone.trim() && 
         formData.value.province && 
         formData.value.city && 
         formData.value.district && 
         formData.value.detail.trim()
})

// 方法
const showRegionPicker = () => {
  showPicker.value = true
}

const hidePicker = () => {
  showPicker.value = false
}

const onPickerChange = (e: any) => {
  pickerValue.value = e.detail.value
}

const confirmRegion = () => {
  const province = provinces.value[pickerValue.value[0]]
  const city = cities.value[pickerValue.value[1]]
  const district = districts.value[pickerValue.value[2]]
  
  if (province && city && district) {
    formData.value.province = province.name
    formData.value.city = city.name
    formData.value.district = district.name
  }
  
  hidePicker()
}

const selectTag = (tag: string) => {
  formData.value.tag = tag
  showCustomInput.value = false
}

const showCustomTag = () => {
  showCustomInput.value = true
  customTagValue.value = ''
}

const confirmCustomTag = () => {
  if (customTagValue.value.trim()) {
    formData.value.tag = customTagValue.value.trim()
  }
  showCustomInput.value = false
}

const toggleDefault = (e: any) => {
  formData.value.isDefault = e.detail.value
}

const saveAddress = () => {
  if (!canSave.value) {
    uni.showToast({
      title: '请完善地址信息',
      icon: 'none'
    })
    return
  }
  
  // 验证手机号
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(formData.value.phone)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }
  
  // 保存地址逻辑
  uni.showLoading({
    title: isEdit.value ? '保存中...' : '添加中...'
  })
  
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: isEdit.value ? '保存成功' : '添加成功',
      icon: 'success'
    })
    
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 1000)
}

onLoad((options) => {
  if (options?.id) {
    isEdit.value = true
    // 模拟加载地址数据
    formData.value = {
      id: parseInt(options.id),
      name: '张三',
      phone: '13888888888',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      detail: '三里屯街道工体北路8号院1号楼101室',
      tag: '家',
      isDefault: true
    }
  }
})
</script>

<style lang="scss" scoped>
.address-edit-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-form {
  .form-section {
    background-color: #ffffff;
    margin-bottom: 20rpx;
    
    .section-title {
      padding: 30rpx 30rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .title-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
      }
    }
    
    .form-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f8f8f8;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.picker-item {
        .picker-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .picker-text {
            font-size: 28rpx;
            color: #333333;
            
            &.placeholder {
              color: #999999;
            }
          }
          
          .picker-arrow {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
      
      &.switch-item {
        justify-content: space-between;
      }
      
      .item-label {
        width: 160rpx;
        font-size: 28rpx;
        color: #333333;
        margin-right: 20rpx;
      }
      
      .item-input {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      
      .item-textarea {
        flex: 1;
        min-height: 120rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 1.5;
      }
    }
    
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;
      padding: 30rpx;
      
      .tag-item {
        padding: 16rpx 32rpx;
        background-color: #f8f8f8;
        border-radius: 20rpx;
        border: 1rpx solid transparent;
        
        &.active {
          background-color: #fff7f0;
          border-color: #ff6b35;
          
          .tag-text {
            color: #ff6b35;
          }
        }
        
        &.custom {
          border: 1rpx dashed #d9d9d9;
        }
        
        .tag-text {
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
    
    .custom-tag-input {
      padding: 0 30rpx 30rpx;
      
      .custom-input {
        width: 100%;
        height: 60rpx;
        font-size: 26rpx;
        color: #333333;
        background-color: #f8f8f8;
        border-radius: 8rpx;
        padding: 0 16rpx;
      }
    }
  }
}

.save-btn {
  position: fixed;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  height: 80rpx;
  background-color: #ff6b35;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.disabled {
    background-color: #d9d9d9;
  }
  
  .btn-text {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: 600;
  }
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  
  .picker-container {
    width: 100%;
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    
    .picker-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .cancel-btn, .confirm-btn {
        font-size: 28rpx;
        color: #ff6b35;
      }
      
      .picker-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }
    }
    
    .picker-content {
      height: 400rpx;
      
      .picker-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}
</style>
