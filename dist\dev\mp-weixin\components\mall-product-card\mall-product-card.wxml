<mall-card wx:if="{{q}}" u-s="{{['d']}}" class="mall-product-card data-v-b31b7553" bindclick="{{p}}" u-i="b31b7553-0" bind:__l="__l" u-p="{{q}}"><view class="product-image-wrapper data-v-b31b7553"><image src="{{a}}" class="product-image data-v-b31b7553" mode="aspectFill" binderror="{{b}}"/><view wx:if="{{c}}" class="product-tags data-v-b31b7553"><view wx:for="{{d}}" wx:for-item="tag" wx:key="b" class="product-tag data-v-b31b7553">{{tag.a}}</view></view></view><view class="product-info data-v-b31b7553"><view class="product-name ellipsis-2 data-v-b31b7553">{{e}}</view><view class="product-price-wrapper data-v-b31b7553"><view class="product-price data-v-b31b7553"><text class="price-symbol data-v-b31b7553">¥</text><text class="price-integer data-v-b31b7553">{{f}}</text><text class="price-decimal data-v-b31b7553">.{{g}}</text></view><view wx:if="{{h}}" class="original-price data-v-b31b7553"> ¥{{i}}</view></view><view class="product-meta data-v-b31b7553"><view class="product-rating data-v-b31b7553"><view class="rating-stars data-v-b31b7553"><view wx:for="{{j}}" wx:for-item="i" wx:key="a" class="{{['star', 'data-v-b31b7553', i.b && 'star--filled']}}"> ★ </view></view><text class="rating-text data-v-b31b7553">{{k}}</text></view><view class="product-sales data-v-b31b7553">已售{{l}}</view></view></view><view class="product-actions data-v-b31b7553"><mall-button wx:if="{{o}}" class="data-v-b31b7553" u-s="{{['d']}}" catchclick="{{n}}" u-i="b31b7553-1,b31b7553-0" bind:__l="__l" u-p="{{o}}"><image src="{{m}}" class="action-icon data-v-b31b7553" mode="aspectFit"/></mall-button></view></mall-card>