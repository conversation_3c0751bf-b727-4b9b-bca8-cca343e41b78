<template>
  <view class="publish-container">
    <!-- 顶部导航 -->
    <view class="publish-header">
      <text class="cancel-btn" @click="cancelPublish">取消</text>
      <text class="header-title">发布帖子</text>
      <text class="publish-btn" :class="{ disabled: !canPublish }" @click="submitPost">发布</text>
    </view>

    <!-- 发布表单 -->
    <view class="publish-form">
      <!-- 标题输入 -->
      <view class="title-section">
        <textarea 
          class="title-input" 
          v-model="postTitle" 
          placeholder="请输入帖子标题..."
          maxlength="50"
          auto-height
        />
        <text class="title-count">{{ postTitle.length }}/50</text>
      </view>

      <!-- 内容输入 -->
      <view class="content-section">
        <textarea 
          class="content-input" 
          v-model="postContent" 
          placeholder="分享你的想法、经验或问题..."
          maxlength="1000"
          auto-height
        />
        <text class="content-count">{{ postContent.length }}/1000</text>
      </view>

      <!-- 图片上传 -->
      <view class="images-section">
        <view class="images-grid">
          <view 
            class="image-item" 
            v-for="(image, index) in uploadedImages" 
            :key="index"
          >
            <image :src="image" class="uploaded-image" mode="aspectFill" />
            <view class="image-delete" @click="removeImage(index)">
              <text class="delete-icon">✕</text>
            </view>
          </view>
          
          <view class="add-image" v-if="uploadedImages.length < 9" @click="chooseImages">
            <text class="add-icon">📷</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <text class="images-tip">最多可上传9张图片</text>
      </view>

      <!-- 分类选择 -->
      <view class="category-section">
        <view class="section-header">
          <text class="section-title">选择分类</text>
        </view>
        <view class="category-list">
          <view 
            class="category-item"
            :class="{ selected: selectedCategory === category.key }"
            v-for="category in categories"
            :key="category.key"
            @click="selectCategory(category)"
          >
            <text class="category-icon">{{ category.icon }}</text>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </view>

      <!-- 积分设置 -->
      <view class="points-section">
        <view class="section-header">
          <text class="section-title">积分设置</text>
          <view class="points-toggle">
            <switch :checked="requirePoints" @change="togglePoints" color="#ff6b35" />
          </view>
        </view>
        
        <view class="points-config" v-if="requirePoints">
          <view class="points-input-group">
            <text class="input-label">查看需要积分：</text>
            <input 
              type="number" 
              v-model="pointsRequired" 
              placeholder="0"
              class="points-input"
            />
            <text class="input-unit">积分</text>
          </view>
          <text class="points-tip">设置后，其他用户需要消耗积分才能查看完整内容</text>
        </view>
      </view>

      <!-- 标签设置 -->
      <view class="tags-section">
        <view class="section-header">
          <text class="section-title">添加标签</text>
        </view>
        
        <view class="tags-input">
          <view class="selected-tags">
            <view 
              class="tag-item" 
              v-for="(tag, index) in selectedTags" 
              :key="index"
            >
              <text class="tag-text">{{ tag }}</text>
              <text class="tag-remove" @click="removeTag(index)">✕</text>
            </view>
          </view>
          
          <input 
            v-if="selectedTags.length < 5"
            type="text" 
            v-model="currentTag" 
            placeholder="输入标签后按回车添加"
            class="tag-input"
            @confirm="addTag"
          />
        </view>
        
        <view class="hot-tags">
          <text class="hot-tags-title">热门标签：</text>
          <view class="hot-tags-list">
            <text 
              class="hot-tag"
              v-for="tag in hotTags"
              :key="tag"
              @click="addHotTag(tag)"
            >
              {{ tag }}
            </text>
          </view>
        </view>
      </view>

      <!-- 发布设置 -->
      <view class="settings-section">
        <view class="setting-item">
          <text class="setting-label">允许评论</text>
          <switch :checked="allowComments" @change="toggleComments" color="#ff6b35" />
        </view>
        
        <view class="setting-item">
          <text class="setting-label">匿名发布</text>
          <switch :checked="isAnonymous" @change="toggleAnonymous" color="#ff6b35" />
        </view>
      </view>
    </view>

    <!-- 发布奖励提示 -->
    <view class="reward-tip">
      <text class="tip-icon">💎</text>
      <text class="tip-text">发布优质内容可获得积分奖励</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Category {
  key: string
  name: string
  icon: string
}

// 响应式数据
const postTitle = ref('')
const postContent = ref('')
const uploadedImages = ref<string[]>([])
const selectedCategory = ref('')
const requirePoints = ref(false)
const pointsRequired = ref(0)
const selectedTags = ref<string[]>([])
const currentTag = ref('')
const allowComments = ref(true)
const isAnonymous = ref(false)

const categories = ref<Category[]>([
  { key: 'shopping', name: '购物分享', icon: '🛍️' },
  { key: 'experience', name: '经验分享', icon: '💡' },
  { key: 'review', name: '产品评测', icon: '⭐' },
  { key: 'question', name: '问题求助', icon: '❓' },
  { key: 'discussion', name: '讨论交流', icon: '💬' },
  { key: 'news', name: '资讯分享', icon: '📰' },
  { key: 'other', name: '其他', icon: '📝' }
])

const hotTags = ref([
  '好物推荐', '性价比', '新品体验', '使用心得', '购买攻略', 
  '优惠活动', '品牌对比', '质量评价', '售后服务', '物流体验'
])

// 计算属性
const canPublish = computed(() => {
  return postTitle.value.trim().length > 0 && 
         postContent.value.trim().length > 0 && 
         selectedCategory.value !== ''
})

// 方法
const cancelPublish = () => {
  if (postTitle.value || postContent.value || uploadedImages.value.length > 0) {
    uni.showModal({
      title: '确认退出',
      content: '退出后内容将不会保存，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          uni.navigateBack()
        }
      }
    })
  } else {
    uni.navigateBack()
  }
}

const chooseImages = () => {
  const remainingCount = 9 - uploadedImages.value.length
  
  uni.chooseImage({
    count: remainingCount,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      uploadedImages.value.push(...res.tempFilePaths)
    }
  })
}

const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1)
}

const selectCategory = (category: Category) => {
  selectedCategory.value = category.key
}

const togglePoints = (e: any) => {
  requirePoints.value = e.detail.value
  if (!requirePoints.value) {
    pointsRequired.value = 0
  }
}

const addTag = () => {
  const tag = currentTag.value.trim()
  if (tag && !selectedTags.value.includes(tag) && selectedTags.value.length < 5) {
    selectedTags.value.push(tag)
    currentTag.value = ''
  }
}

const addHotTag = (tag: string) => {
  if (!selectedTags.value.includes(tag) && selectedTags.value.length < 5) {
    selectedTags.value.push(tag)
  }
}

const removeTag = (index: number) => {
  selectedTags.value.splice(index, 1)
}

const toggleComments = (e: any) => {
  allowComments.value = e.detail.value
}

const toggleAnonymous = (e: any) => {
  isAnonymous.value = e.detail.value
}

const submitPost = () => {
  if (!canPublish.value) return
  
  const postData = {
    title: postTitle.value.trim(),
    content: postContent.value.trim(),
    images: uploadedImages.value,
    category: selectedCategory.value,
    pointsRequired: requirePoints.value ? pointsRequired.value : 0,
    tags: selectedTags.value,
    allowComments: allowComments.value,
    isAnonymous: isAnonymous.value
  }
  
  uni.showLoading({
    title: '发布中...'
  })
  
  // 模拟发布请求
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '发布成功，获得30积分',
      icon: 'success'
    })
    
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 2000)
}
</script>

<style lang="scss" scoped>
.publish-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.publish-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .cancel-btn, .publish-btn {
    font-size: 28rpx;
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
  }

  .cancel-btn {
    color: #666666;
    background-color: #f8f8f8;
  }

  .publish-btn {
    color: #ffffff;
    background-color: #ff6b35;

    &.disabled {
      background-color: #d9d9d9;
    }
  }

  .header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
}

.publish-form {
  padding: 30rpx;
}

.title-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;

  .title-input {
    width: 100%;
    font-size: 32rpx;
    color: #333333;
    line-height: 1.5;
    min-height: 80rpx;
  }

  .title-count {
    position: absolute;
    bottom: 12rpx;
    right: 20rpx;
    font-size: 22rpx;
    color: #999999;
  }
}

.content-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;

  .content-input {
    width: 100%;
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
    min-height: 200rpx;
  }

  .content-count {
    position: absolute;
    bottom: 12rpx;
    right: 20rpx;
    font-size: 22rpx;
    color: #999999;
  }
}

.images-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .images-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    margin-bottom: 16rpx;

    .image-item {
      position: relative;
      aspect-ratio: 1;

      .uploaded-image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .image-delete {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ff4d4f;
        border-radius: 50%;

        .delete-icon {
          font-size: 20rpx;
          color: #ffffff;
        }
      }
    }

    .add-image {
      aspect-ratio: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f8f8f8;
      border: 2rpx dashed #d9d9d9;
      border-radius: 8rpx;

      .add-icon {
        font-size: 48rpx;
        margin-bottom: 8rpx;
      }

      .add-text {
        font-size: 22rpx;
        color: #999999;
      }
    }
  }

  .images-tip {
    font-size: 22rpx;
    color: #999999;
  }
}

.category-section, .points-section, .tags-section, .settings-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
  }
}

.category-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;

  .category-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border: 2rpx solid #f0f0f0;
    border-radius: 8rpx;

    &.selected {
      border-color: #ff6b35;
      background-color: #fff7f0;
    }

    .category-icon {
      font-size: 32rpx;
      margin-right: 12rpx;
    }

    .category-name {
      font-size: 26rpx;
      color: #333333;
    }
  }
}

.points-config {
  .points-input-group {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .input-label {
      font-size: 26rpx;
      color: #333333;
      margin-right: 16rpx;
    }

    .points-input {
      flex: 1;
      height: 60rpx;
      font-size: 26rpx;
      color: #333333;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 0 16rpx;
      margin-right: 16rpx;
    }

    .input-unit {
      font-size: 26rpx;
      color: #666666;
    }
  }

  .points-tip {
    font-size: 22rpx;
    color: #999999;
    line-height: 1.4;
  }
}

.tags-input {
  margin-bottom: 20rpx;

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 16rpx;

    .tag-item {
      display: flex;
      align-items: center;
      background-color: #fff7f0;
      border: 1rpx solid #ff6b35;
      border-radius: 16rpx;
      padding: 8rpx 16rpx;

      .tag-text {
        font-size: 24rpx;
        color: #ff6b35;
        margin-right: 8rpx;
      }

      .tag-remove {
        font-size: 20rpx;
        color: #ff6b35;
      }
    }
  }

  .tag-input {
    width: 100%;
    height: 60rpx;
    font-size: 26rpx;
    color: #333333;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    padding: 0 16rpx;
  }
}

.hot-tags {
  .hot-tags-title {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }

  .hot-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    .hot-tag {
      font-size: 22rpx;
      color: #666666;
      background-color: #f8f8f8;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
    }
  }
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .setting-label {
    font-size: 26rpx;
    color: #333333;
  }
}

.reward-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff7f0;
  border: 1rpx solid #ff6b35;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 0 30rpx 30rpx;

  .tip-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
  }

  .tip-text {
    font-size: 24rpx;
    color: #ff6b35;
  }
}
</style>
