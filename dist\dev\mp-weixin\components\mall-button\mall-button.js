"use strict";const e=require("../../common/vendor.js"),o=e.defineComponent({__name:"mall-button",props:{type:{default:"primary"},size:{default:"medium"},shape:{default:"square"},disabled:{type:<PERSON>olean,default:!1},loading:{type:Boolean,default:!1},block:{type:Boolean,default:!1},text:{default:""},icon:{},formType:{},openType:{}},emits:["click","getuserinfo","contact","getphonenumber","error","opensetting","launchapp"],setup(o,{emit:t}){const n=o,a=t,l=e.computed(()=>[`mall-button--${n.type}`,`mall-button--${n.size}`,`mall-button--${n.shape}`,{"mall-button--block":n.block,"mall-button--disabled":n.disabled,"mall-button--loading":n.loading}]),d=e=>{n.disabled||n.loading||a("click",e)},i=e=>{a("getuserinfo",e)},p=e=>{a("contact",e)},u=e=>{a("getphonenumber",e)},c=e=>{a("error",e)},r=e=>{a("opensetting",e)},s=e=>{a("launchapp",e)};return(o,t)=>e.e({a:o.loading},(o.loading,{}),{b:o.icon&&!o.loading},o.icon&&!o.loading?{c:o.icon}:{},{d:e.t(o.text),e:e.n(l.value),f:o.disabled||o.loading,g:o.formType,h:o.openType,i:e.o(d),j:e.o(i),k:e.o(p),l:e.o(u),m:e.o(c),n:e.o(r),o:e.o(s)})}}),t=e._export_sfc(o,[["__scopeId","data-v-8f5cd3b1"]]);wx.createComponent(t);
