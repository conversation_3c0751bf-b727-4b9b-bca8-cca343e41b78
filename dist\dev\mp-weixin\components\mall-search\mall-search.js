"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),l=e.defineComponent({__name:"mall-search",props:{modelValue:{default:""},placeholder:{default:"请输入搜索关键词"},disabled:{type:<PERSON>olean,default:!1},maxlength:{default:100},showClear:{type:Boolean,default:!0},showCancel:{type:Boolean,default:!1},autofocus:{type:Boolean,default:!1}},emits:["update:modelValue","input","focus","blur","confirm","clear","cancel"],setup(l,{emit:o}){const t=l,u=o,c=e.ref(t.modelValue),n=e.ref(!1);e.watch(()=>t.modelValue,e=>{c.value=e}),e.watch(c,e=>{u("update:modelValue",e)});const s=e=>{const a=e.detail.value;c.value=a,u("input",a)},d=e=>{n.value=!0,u("focus",e)},r=e=>{n.value=!1,u("blur",e)},m=e=>{const a=e.detail.value;u("confirm",a)},p=()=>{c.value="",u("clear")},i=()=>{c.value="",n.value=!1,u("cancel")};return(l,o)=>e.e({a:a._imports_0$2,b:l.placeholder,c:l.disabled,d:l.maxlength,e:e.o([e=>c.value=e.detail.value,s]),f:e.o(d),g:e.o(r),h:e.o(m),i:c.value,j:l.showClear&&c.value},l.showClear&&c.value?{k:a._imports_1$2,l:e.o(p)}:{},{m:l.showCancel},l.showCancel?{n:e.o(i)}:{},{o:n.value?1:""})}}),o=e._export_sfc(l,[["__scopeId","data-v-ad20d36c"]]);wx.createComponent(o);
