{"version": 3, "file": "getParserServices.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/getParserServices.ts"], "names": [], "mappings": ";;AAyGS,8CAAiB;AApG1B,uEAAoE;AAEpE,MAAM,sCAAsC,GAC1C,gLAAgL,CAAC;AAEnL,MAAM,4BAA4B,GAChC,mKAAmK,CAAC;AA+CtK,SAAS,iBAAiB,CACxB,OAA0D,EAC1D,+BAA+B,GAAG,KAAK;IAEvC,MAAM,MAAM,GACV,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAEnE,mFAAmF;IACnF,EAAE;IACF,mCAAmC;IACnC,yCAAyC;IACzC,yGAAyG;IACzG,sEAAsE;IACtE,0FAA0F;IAC1F,EAAE;IACF,qFAAqF;IACrF,wCAAwC;IACxC,IACE,OAAO,CAAC,UAAU,CAAC,cAAc,EAAE,qBAAqB,IAAI,IAAI;QAChE,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,IAAI,IAAI,EAC/D,CAAC;QACD,UAAU,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED,+EAA+E;IAC/E,uDAAuD;IACvD,IACE,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,IAAI,IAAI;QACjD,CAAC,+BAA+B,EAChC,CAAC;QACD,UAAU,CAAC,MAAM,CAAC,CAAC;IACrB,CAAC;IAED,OAAO,OAAO,CAAC,UAAU,CAAC,cAAgC,CAAC;AAC7D,CAAC;AACD,yDAAyD;AAEzD,SAAS,UAAU,CAAC,MAA0B;IAC5C,MAAM,QAAQ,GAAG;QACf,sCAAsC;QACtC,WAAW,MAAM,IAAI,WAAW,EAAE;QAClC,CAAC,IAAA,iDAAuB,EAAC,MAAM,CAAC,IAAI,4BAA4B;KACjE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAElB,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC"}