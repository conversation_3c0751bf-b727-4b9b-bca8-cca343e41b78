<template>
  <view class="custom-tabbar">
    <!-- 顶部分割线 -->
    <view class="tabbar-divider"></view>

    <view class="tabbar-content">
      <view
        class="tabbar-item"
        v-for="(item, index) in tabList"
        :key="index"
        :class="{ active: currentIndex === index }"
        @click="switchTab(item, index)"
      >
        <view class="tabbar-icon">
          <text class="icon-text">{{ item.icon }}</text>
        </view>
        <text class="tabbar-text">{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface TabItem {
  pagePath: string
  text: string
  icon: string
}

const currentIndex = ref(0)

const tabList: TabItem[] = [
  {
    pagePath: '/pages/home/<USER>',
    text: '今日',
    icon: '🏠'
  },
  {
    pagePath: '/pages/category/index',
    text: '稳健理财',
    icon: '💰'
  },
  {
    pagePath: '/pages/cart/index',
    text: '基金',
    icon: '📈'
  },
  {
    pagePath: '/pages/profile/index',
    text: '我的',
    icon: '👤'
  }
]

const switchTab = (item: TabItem, index: number) => {
  if (currentIndex.value === index) return
  
  currentIndex.value = index
  uni.switchTab({
    url: item.pagePath
  })
}

// 获取当前页面路径并设置对应的 tab 索引
const getCurrentPageIndex = () => {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentRoute = '/' + currentPage.route
    
    const index = tabList.findIndex(item => item.pagePath === currentRoute)
    if (index !== -1) {
      currentIndex.value = index
    }
  }
}

onMounted(() => {
  getCurrentPageIndex()
})

// 监听页面显示
uni.$on('tabbar-update', (index: number) => {
  currentIndex.value = index
})
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-divider {
  width: 280rpx;
  height: 6rpx;
  background: #000000;
  border-radius: 3rpx;
  margin: 0 auto;
  margin-top: 12rpx;
}

.tabbar-content {
  display: flex;
  height: 120rpx;
  align-items: center;
  justify-content: space-around;
  padding: 12rpx 0;
}

.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 96rpx;
  padding: 8rpx 0;
  transition: all 0.2s ease;
}

.tabbar-icon {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  opacity: 0.6;
  transition: all 0.2s ease;
}

.icon-text {
  font-size: 40rpx;
  line-height: 1;
}

.tabbar-text {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
  line-height: 1.2;
  transition: all 0.2s ease;
}

/* 选中状态 */
.tabbar-item.active {
  .tabbar-icon {
    opacity: 1;
  }

  .tabbar-text {
    color: #1677ff;
    font-weight: 500;
  }

  /* 为emoji图标添加蓝色效果 */
  .icon-text {
    filter: hue-rotate(200deg) saturate(1.5);
  }
}
</style>
