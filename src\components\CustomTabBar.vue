<template>
  <view class="custom-tabbar">
    <view 
      class="tabbar-item" 
      v-for="(item, index) in tabList" 
      :key="index"
      :class="{ active: currentIndex === index }"
      @click="switchTab(item, index)"
    >
      <view class="tabbar-icon">{{ item.icon }}</view>
      <text class="tabbar-text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface TabItem {
  pagePath: string
  text: string
  icon: string
}

const currentIndex = ref(0)

const tabList: TabItem[] = [
  {
    pagePath: '/pages/home/<USER>',
    text: '首页',
    icon: '🏠'
  },
  {
    pagePath: '/pages/category/index',
    text: '分类',
    icon: '📂'
  },
  {
    pagePath: '/pages/cart/index',
    text: '购物车',
    icon: '🛒'
  },
  {
    pagePath: '/pages/profile/index',
    text: '我的',
    icon: '👤'
  }
]

const switchTab = (item: TabItem, index: number) => {
  if (currentIndex.value === index) return
  
  currentIndex.value = index
  uni.switchTab({
    url: item.pagePath
  })
}

// 获取当前页面路径并设置对应的 tab 索引
const getCurrentPageIndex = () => {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentRoute = '/' + currentPage.route
    
    const index = tabList.findIndex(item => item.pagePath === currentRoute)
    if (index !== -1) {
      currentIndex.value = index
    }
  }
}

onMounted(() => {
  getCurrentPageIndex()
})

// 监听页面显示
uni.$on('tabbar-update', (index: number) => {
  currentIndex.value = index
})
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 10rpx 0;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx;
  transition: all 0.3s ease;
  
  &.active {
    .tabbar-icon {
      transform: scale(1.1);
    }
    
    .tabbar-text {
      color: #007aff;
      font-weight: bold;
    }
  }
}

.tabbar-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  transition: transform 0.3s ease;
}

.tabbar-text {
  font-size: 24rpx;
  color: #666666;
  transition: color 0.3s ease;
}
</style>
