<!DOCTYPE html>
<html>
<head>
    <title>Create TabBar Icons</title>
    <style>
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            width: 44px;
            height: 44px;
            display: block;
            margin: 0 auto 5px;
        }
        .normal { fill: #999999; }
        .active { fill: #1677ff; }
    </style>
</head>
<body>
    <h2>TabBar Icons for WeChat Mini Program</h2>
    
    <!-- 首页图标 -->
    <div class="icon-container">
        <svg class="icon normal" viewBox="0 0 1024 1024">
            <path d="M946.5 505L560.1 118.8l-25.9-25.9c-12.3-12.2-32.1-12.2-44.4 0L77.5 505c-12.3 12.3-18.9 28.6-18.9 46 0 35.3 28.7 64 64 64h43.4V908c0 17.7 14.3 32 32 32H448V716h112v224h265.9c17.7 0 32-14.3 32-32V615h43.4c17.2 0 33.9-6.7 46-18.9 25.1-25.1 25.1-65.5 0-90.5z"/>
        </svg>
        <div>今日 (normal)</div>
    </div>
    
    <div class="icon-container">
        <svg class="icon active" viewBox="0 0 1024 1024">
            <path d="M946.5 505L560.1 118.8l-25.9-25.9c-12.3-12.2-32.1-12.2-44.4 0L77.5 505c-12.3 12.3-18.9 28.6-18.9 46 0 35.3 28.7 64 64 64h43.4V908c0 17.7 14.3 32 32 32H448V716h112v224h265.9c17.7 0 32-14.3 32-32V615h43.4c17.2 0 33.9-6.7 46-18.9 25.1-25.1 25.1-65.5 0-90.5z"/>
        </svg>
        <div>今日 (active)</div>
    </div>

    <!-- 理财图标 -->
    <div class="icon-container">
        <svg class="icon normal" viewBox="0 0 1024 1024">
            <path d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 161c-5.2 4.4-13.1.7-13.1-6.1v-62.7c0-2.3 1.1-4.6 2.9-6.1L420.7 512l-109.8-92.2c-1.8-1.5-2.9-3.8-2.9-6.1v-62.7c0-6.8 7.9-10.5 13.1-6.1l192 161c3.9 3.2 3.9 9.1 0 12.2zM716 673c0 4.4-3.4 8-7.5 8h-185c-4.1 0-7.5-3.6-7.5-8v-48c0-4.4 3.4-8 7.5-8h185c4.1 0 7.5 3.6 7.5 8v48z"/>
        </svg>
        <div>稳健理财 (normal)</div>
    </div>
    
    <div class="icon-container">
        <svg class="icon active" viewBox="0 0 1024 1024">
            <path d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM513.1 518.1l-192 161c-5.2 4.4-13.1.7-13.1-6.1v-62.7c0-2.3 1.1-4.6 2.9-6.1L420.7 512l-109.8-92.2c-1.8-1.5-2.9-3.8-2.9-6.1v-62.7c0-6.8 7.9-10.5 13.1-6.1l192 161c3.9 3.2 3.9 9.1 0 12.2zM716 673c0 4.4-3.4 8-7.5 8h-185c-4.1 0-7.5-3.6-7.5-8v-48c0-4.4 3.4-8 7.5-8h185c4.1 0 7.5 3.6 7.5 8v48z"/>
        </svg>
        <div>稳健理财 (active)</div>
    </div>

    <!-- 基金图标 -->
    <div class="icon-container">
        <svg class="icon normal" viewBox="0 0 1024 1024">
            <path d="M864 256H736v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.4 557c1.4 31.1 26.8 55 58 55h566.4c31.2 0 56.6-23.9 58-55L927.6 328H968c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM352 192h320v64H352v-64zm504.3 131L831.7 848H192.3L167.7 323h688.6z"/>
            <path d="M424 672c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v216zm200 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v216z"/>
        </svg>
        <div>基金 (normal)</div>
    </div>
    
    <div class="icon-container">
        <svg class="icon active" viewBox="0 0 1024 1024">
            <path d="M864 256H736v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.4 557c1.4 31.1 26.8 55 58 55h566.4c31.2 0 56.6-23.9 58-55L927.6 328H968c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM352 192h320v64H352v-64zm504.3 131L831.7 848H192.3L167.7 323h688.6z"/>
            <path d="M424 672c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v216zm200 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v216z"/>
        </svg>
        <div>基金 (active)</div>
    </div>

    <!-- 我的图标 -->
    <div class="icon-container">
        <svg class="icon normal" viewBox="0 0 1024 1024">
            <path d="M858.5 763.6c-18.9-44.8-46.1-85-80.6-119.5-34.5-34.5-74.7-61.6-119.5-80.6-0.4-0.2-0.8-0.3-1.2-0.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-0.4 0.2-0.8 0.3-1.2 0.5-44.8 18.9-85 46-119.5 80.6-34.5 34.5-61.6 74.7-80.6 119.5C146.9 807.5 137 854 136 901.8c-0.1 4.5 3.5 8.2 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c0.1 4.4 3.6 7.8 8 7.8h60c4.5 0 8.1-3.7 8-8.2-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"/>
        </svg>
        <div>我的 (normal)</div>
    </div>
    
    <div class="icon-container">
        <svg class="icon active" viewBox="0 0 1024 1024">
            <path d="M858.5 763.6c-18.9-44.8-46.1-85-80.6-119.5-34.5-34.5-74.7-61.6-119.5-80.6-0.4-0.2-0.8-0.3-1.2-0.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-0.4 0.2-0.8 0.3-1.2 0.5-44.8 18.9-85 46-119.5 80.6-34.5 34.5-61.6 74.7-80.6 119.5C146.9 807.5 137 854 136 901.8c-0.1 4.5 3.5 8.2 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c0.1 4.4 3.6 7.8 8 7.8h60c4.5 0 8.1-3.7 8-8.2-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"/>
        </svg>
        <div>我的 (active)</div>
    </div>

    <script>
        // 这个HTML文件可以在浏览器中打开，然后右键保存每个图标为PNG格式
        console.log('请在浏览器中打开此文件，然后右键保存每个图标为PNG格式');
        console.log('建议尺寸：44x44px for normal state, 44x44px for active state');
    </script>
</body>
</html>
