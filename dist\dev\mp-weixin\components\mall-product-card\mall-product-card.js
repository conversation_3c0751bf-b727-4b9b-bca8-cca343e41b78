"use strict";const t=require("../../common/vendor.js"),o=require("../../common/assets.js");if(!Array){(t.resolveComponent("mall-button")+t.resolveComponent("mall-card"))()}Math||((()=>"../mall-button/mall-button.js")+(()=>"../mall-card/mall-card.js"))();const r=t.defineComponent({__name:"mall-product-card",props:{product:{}},emits:["click","add-to-cart"],setup(r,{emit:e}){const c=r,a=e,i=t.computed(()=>Math.floor(c.product.price).toString()),p=t.computed(()=>(c.product.price%1).toFixed(2).slice(2)),d=()=>{a("click",c.product)},l=()=>{a("add-to-cart",c.product)},n=()=>{};return(r,e)=>{return t.e({a:r.product.images[0],b:t.o(n),c:r.product.tags.length>0},r.product.tags.length>0?{d:t.f(r.product.tags.slice(0,2),(o,r,e)=>({a:t.t(o),b:o}))}:{},{e:t.t(r.product.name),f:t.t(i.value),g:t.t(p.value),h:r.product.originalPrice&&r.product.originalPrice>r.product.price},r.product.originalPrice&&r.product.originalPrice>r.product.price?{i:t.t(r.product.originalPrice.toFixed(2))}:{},{j:t.f(5,(t,o,e)=>({a:t,b:t<=Math.floor(r.product.rating)?1:""})),k:t.t(r.product.rating.toFixed(1)),l:t.t((c=r.product.sales,c>=1e4?`${(c/1e4).toFixed(1)}万`:c.toString())),m:o._imports_0$3,n:t.o(l),o:t.p({type:"text",size:"small"}),p:t.o(d),q:t.p({clickable:!0})});var c}}}),e=t._export_sfc(r,[["__scopeId","data-v-b31b7553"]]);wx.createComponent(e);
