"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setSrcDescriptor = exports.getSrcDescriptor = exports.getDescriptor = exports.createDescriptor = exports.cache = exports.getResolvedOptions = void 0;
const fs_1 = __importDefault(require("fs"));
const crypto_1 = require("crypto");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const utils_1 = require("../utils");
const script_1 = require("./sfc/script");
function getResolvedOptions() {
    return {
        root: process.env.UNI_INPUT_DIR,
        sourceMap: (process.env.UNI_APP_SOURCEMAP === 'true' ||
            process.env.NODE_ENV === 'development') &&
            process.env.UNI_COMPILE_TARGET !== 'uni_modules',
        // eslint-disable-next-line no-restricted-globals
        compiler: require('@vue/compiler-sfc'),
        targetLanguage: process.env.UNI_UTS_TARGET_LANGUAGE,
        genDefaultAs: script_1.scriptIdentifier,
    };
}
exports.getResolvedOptions = getResolvedOptions;
exports.cache = new Map();
function createDescriptor(filename, source, { root, sourceMap, compiler }) {
    // ensure the path is normalized in a way that is consistent inside
    // project (relative to root) and on different systems.
    const relativeFilename = (0, utils_1.parseUTSRelativeFilename)(filename, root);
    // 传入normalizedPath是为了让sourcemap记录的是相对路径
    const { descriptor, errors } = compiler.parse(source, {
        filename: relativeFilename,
        sourceMap,
    });
    descriptor.relativeFilename = relativeFilename;
    // 重置为绝对路径
    descriptor.filename = filename;
    descriptor.id = getHash(relativeFilename);
    exports.cache.set(filename, descriptor);
    return { descriptor, errors };
}
exports.createDescriptor = createDescriptor;
function getDescriptor(filename, options, createIfNotFound = true) {
    if (exports.cache.has(filename)) {
        return exports.cache.get(filename);
    }
    if (createIfNotFound) {
        const { descriptor, errors } = createDescriptor(filename, (0, uni_cli_shared_1.preUVueJs)((0, uni_cli_shared_1.preUVueHtml)(fs_1.default.readFileSync(filename, 'utf-8'))), options);
        if (errors.length) {
            throw errors[0];
        }
        return descriptor;
    }
}
exports.getDescriptor = getDescriptor;
function getSrcDescriptor(filename) {
    return exports.cache.get(filename);
}
exports.getSrcDescriptor = getSrcDescriptor;
function setSrcDescriptor(filename, entry) {
    exports.cache.set(filename, entry);
}
exports.setSrcDescriptor = setSrcDescriptor;
function getHash(text) {
    return (0, crypto_1.createHash)('sha256').update(text).digest('hex').substring(0, 8);
}
