# 图标文件说明

本项目需要以下PNG格式的图标文件，请准备相应的图标并放置在对应目录中。

## TabBar图标 (src/static/tabbar/)

### 首页图标
- `home.png` - 首页未选中状态 (24x24px)
- `home-active.png` - 首页选中状态 (24x24px)

### 分类图标
- `category.png` - 分类未选中状态 (24x24px)
- `category-active.png` - 分类选中状态 (24x24px)

### 购物车图标
- `cart.png` - 购物车未选中状态 (24x24px)
- `cart-active.png` - 购物车选中状态 (24x24px)

### 个人中心图标
- `user.png` - 个人中心未选中状态 (24x24px)
- `user-active.png` - 个人中心选中状态 (24x24px)

## 通用图标 (src/static/icons/)

### 导航图标
- `arrow-left.png` - 左箭头/返回 (20x20px)
- `arrow-right.png` - 右箭头 (20x20px)
- `arrow-up.png` - 上箭头 (20x20px)
- `arrow-down.png` - 下箭头 (20x20px)

### 功能图标
- `search.png` - 搜索图标 (16x16px)
- `close.png` - 关闭图标 (16x16px)
- `close-circle.png` - 圆形关闭图标 (16x16px)
- `cart-add.png` - 添加到购物车 (20x20px)
- `heart.png` - 收藏/喜欢 (20x20px)
- `heart-filled.png` - 已收藏 (20x20px)
- `share.png` - 分享图标 (20x20px)
- `location.png` - 位置图标 (16x16px)
- `phone.png` - 电话图标 (16x16px)
- `message.png` - 消息图标 (16x16px)

### 状态图标
- `success.png` - 成功状态 (24x24px)
- `error.png` - 错误状态 (24x24px)
- `warning.png` - 警告状态 (24x24px)
- `info.png` - 信息状态 (24x24px)
- `loading.png` - 加载状态 (24x24px)

### 商品相关图标
- `star.png` - 星星评分 (12x12px)
- `star-filled.png` - 实心星星 (12x12px)
- `eye.png` - 查看/浏览 (16x16px)
- `tag.png` - 标签图标 (16x16px)

### 用户相关图标
- `avatar-default.png` - 默认头像 (40x40px)
- `edit.png` - 编辑图标 (16x16px)
- `setting.png` - 设置图标 (20x20px)
- `logout.png` - 退出登录 (16x16px)

## 图标设计要求

1. **格式**: 所有图标必须为PNG格式
2. **背景**: 透明背景
3. **颜色**: 
   - 未选中状态: #999999
   - 选中状态: #ff6b35 (主题色)
   - 通用图标: #333333 或根据使用场景调整
4. **尺寸**: 严格按照上述尺寸要求
5. **风格**: 简洁、现代、统一的设计风格
6. **清晰度**: 支持高分辨率显示，建议提供2x和3x版本

## 临时解决方案

在正式图标准备完成之前，可以：
1. 使用在线图标生成工具创建简单的PNG图标
2. 从免费图标库下载合适的图标
3. 使用设计软件创建简单的几何形状图标

## 推荐图标资源

- Iconfont (阿里巴巴矢量图标库)
- Feather Icons
- Heroicons
- Tabler Icons
- Phosphor Icons

注意：使用第三方图标时请确保符合使用许可要求。
