{"name": "@dcloudio/uni-mp-compiler", "version": "3.0.0-4020920240930001", "description": "uni-mp-compiler", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-mp-compiler"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "dependencies": {"@babel/generator": "^7.20.5", "@babel/parser": "^7.23.9", "@babel/types": "^7.20.7", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/uni-shared": "3.0.0-4020920240930001", "@vue/compiler-core": "3.4.21", "@vue/compiler-dom": "3.4.21", "@vue/shared": "3.4.21", "estree-walker": "^2.0.2"}, "devDependencies": {"@types/babel__generator": "^7.6.4", "@vue/compiler-sfc": "3.4.21", "source-map-js": "^1.0.2"}}