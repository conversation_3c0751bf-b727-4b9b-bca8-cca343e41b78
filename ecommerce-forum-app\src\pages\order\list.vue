<template>
  <view class="order-list-container">
    <!-- 订单状态筛选 -->
    <view class="order-tabs">
      <view 
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        v-for="tab in orderTabs"
        :key="tab.key"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="tab-badge" v-if="tab.count > 0">
          <text class="badge-text">{{ tab.count }}</text>
        </view>
      </view>
    </view>

    <!-- 订单列表 -->
    <scroll-view 
      class="order-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="refreshOrders"
      :refresher-triggered="isRefreshing"
    >
      <view class="order-list">
        <view 
          class="order-item" 
          v-for="order in filteredOrders" 
          :key="order.id"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-info">
              <text class="order-number">订单号：{{ order.orderNumber }}</text>
              <text class="order-time">{{ formatTime(order.createTime) }}</text>
            </view>
            <view class="order-status" :class="getStatusClass(order.status)">
              <text class="status-text">{{ getStatusText(order.status) }}</text>
            </view>
          </view>

          <!-- 商品列表 -->
          <view class="product-list">
            <view 
              class="product-item" 
              v-for="product in order.products" 
              :key="product.id"
              @click="goToProduct(product.id)"
            >
              <image :src="product.image" class="product-image" mode="aspectFill" />
              <view class="product-info">
                <text class="product-name">{{ product.name }}</text>
                <text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
                <view class="product-price">
                  <text class="price-text">¥{{ product.price }}</text>
                  <text class="quantity-text">×{{ product.quantity }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 订单金额 -->
          <view class="order-amount">
            <text class="amount-label">实付款：</text>
            <text class="amount-value">¥{{ order.totalAmount.toFixed(2) }}</text>
          </view>

          <!-- 订单操作 -->
          <view class="order-actions">
            <view class="action-left">
              <text class="contact-service" @click="contactService(order)">联系客服</text>
            </view>
            <view class="action-right">
              <text 
                class="action-btn secondary"
                v-if="order.status === 'pending'"
                @click="cancelOrder(order)"
              >
                取消订单
              </text>
              <text 
                class="action-btn primary"
                v-if="order.status === 'pending'"
                @click="payOrder(order)"
              >
                立即支付
              </text>
              <text 
                class="action-btn secondary"
                v-if="order.status === 'shipped'"
                @click="viewLogistics(order)"
              >
                查看物流
              </text>
              <text 
                class="action-btn primary"
                v-if="order.status === 'shipped'"
                @click="confirmReceive(order)"
              >
                确认收货
              </text>
              <text 
                class="action-btn secondary"
                v-if="order.status === 'completed'"
                @click="evaluateOrder(order)"
              >
                评价
              </text>
              <text 
                class="action-btn primary"
                v-if="order.status === 'completed'"
                @click="buyAgain(order)"
              >
                再次购买
              </text>
              <text 
                class="action-btn secondary"
                v-if="['completed', 'cancelled'].includes(order.status)"
                @click="deleteOrder(order)"
              >
                删除订单
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载更多...</text>
      </view>

      <!-- 无订单 -->
      <view class="no-orders" v-if="filteredOrders.length === 0 && !isLoading">
        <text class="no-orders-icon">📦</text>
        <text class="no-orders-text">暂无相关订单</text>
        <text class="no-orders-tip">去逛逛商城吧</text>
        <view class="go-shopping-btn" @click="goShopping">
          <text class="btn-text">去购物</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad, onShow } from 'vue'

interface Product {
  id: number
  name: string
  image: string
  price: number
  quantity: number
  spec?: string
}

interface Order {
  id: number
  orderNumber: string
  status: string
  createTime: string
  totalAmount: number
  products: Product[]
}

// 响应式数据
const activeTab = ref('all')
const isRefreshing = ref(false)
const isLoading = ref(false)
const hasMore = ref(true)

const orderTabs = ref([
  { key: 'all', name: '全部', count: 0 },
  { key: 'pending', name: '待付款', count: 2 },
  { key: 'paid', name: '待发货', count: 1 },
  { key: 'shipped', name: '待收货', count: 3 },
  { key: 'completed', name: '已完成', count: 5 },
  { key: 'cancelled', name: '已取消', count: 1 }
])

const orders = ref<Order[]>([
  {
    id: 1,
    orderNumber: 'ORD202401020001',
    status: 'pending',
    createTime: '2024-01-02 15:30:00',
    totalAmount: 299.9,
    products: [
      {
        id: 1,
        name: '智能手表 运动版',
        image: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=手表',
        price: 299.9,
        quantity: 1,
        spec: '黑色 42mm'
      }
    ]
  },
  {
    id: 2,
    orderNumber: 'ORD202401020002',
    status: 'shipped',
    createTime: '2024-01-01 10:20:00',
    totalAmount: 599.8,
    products: [
      {
        id: 2,
        name: '蓝牙耳机 降噪版',
        image: 'https://via.placeholder.com/120x120/1890ff/ffffff?text=耳机',
        price: 199.9,
        quantity: 2
      },
      {
        id: 3,
        name: '手机支架',
        image: 'https://via.placeholder.com/120x120/52c41a/ffffff?text=支架',
        price: 99.9,
        quantity: 2
      }
    ]
  },
  {
    id: 3,
    orderNumber: 'ORD202312310001',
    status: 'completed',
    createTime: '2023-12-31 16:45:00',
    totalAmount: 199.9,
    products: [
      {
        id: 4,
        name: '无线充电器',
        image: 'https://via.placeholder.com/120x120/faad14/ffffff?text=充电器',
        price: 199.9,
        quantity: 1,
        spec: '白色 快充版'
      }
    ]
  }
])

// 计算属性
const filteredOrders = computed(() => {
  if (activeTab.value === 'all') {
    return orders.value
  }
  return orders.value.filter(order => order.status === activeTab.value)
})

// 方法
const switchTab = (tabKey: string) => {
  activeTab.value = tabKey
}

const refreshOrders = () => {
  isRefreshing.value = true
  
  // 模拟刷新
  setTimeout(() => {
    isRefreshing.value = false
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1000)
}

const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  
  // 模拟加载更多
  setTimeout(() => {
    isLoading.value = false
    // hasMore.value = false // 没有更多数据时设置为false
  }, 1000)
}

const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'pending',
    paid: 'paid',
    shipped: 'shipped',
    completed: 'completed',
    cancelled: 'cancelled'
  }
  return statusMap[status] || ''
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待付款',
    paid: '待发货',
    shipped: '待收货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知状态'
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  return time.toLocaleString()
}

const goToProduct = (productId: number) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${productId}`
  })
}

const contactService = (order: Order) => {
  uni.showToast({
    title: '正在连接客服...',
    icon: 'loading'
  })
}

const cancelOrder = (order: Order) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        order.status = 'cancelled'
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        })
      }
    }
  })
}

const payOrder = (order: Order) => {
  uni.navigateTo({
    url: `/pages/payment/index?orderId=${order.id}`
  })
}

const viewLogistics = (order: Order) => {
  uni.navigateTo({
    url: `/pages/order/logistics?orderNumber=${order.orderNumber}`
  })
}

const confirmReceive = (order: Order) => {
  uni.showModal({
    title: '确认收货',
    content: '确认已收到商品吗？',
    success: (res) => {
      if (res.confirm) {
        order.status = 'completed'
        uni.showToast({
          title: '确认收货成功',
          icon: 'success'
        })
      }
    }
  })
}

const evaluateOrder = (order: Order) => {
  uni.navigateTo({
    url: `/pages/order/evaluate?orderId=${order.id}`
  })
}

const buyAgain = (order: Order) => {
  uni.showToast({
    title: '已加入购物车',
    icon: 'success'
  })
}

const deleteOrder = (order: Order) => {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        const index = orders.value.findIndex(o => o.id === order.id)
        if (index > -1) {
          orders.value.splice(index, 1)
          uni.showToast({
            title: '订单已删除',
            icon: 'success'
          })
        }
      }
    }
  })
}

const goShopping = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  })
}

onLoad((options) => {
  if (options?.status) {
    activeTab.value = options.status
  }
})

onShow(() => {
  // 页面显示时刷新订单状态
})
</script>

<style lang="scss" scoped>
.order-list-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.order-tabs {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
    position: relative;

    &.active {
      border-bottom: 4rpx solid #ff6b35;

      .tab-text {
        color: #ff6b35;
        font-weight: 600;
      }
    }

    .tab-text {
      font-size: 26rpx;
      color: #666666;
    }

    .tab-badge {
      position: absolute;
      top: 16rpx;
      right: 20rpx;
      min-width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ff6b35;
      border-radius: 50%;

      .badge-text {
        font-size: 20rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}

.order-scroll {
  height: calc(100vh - 120rpx);
}

.order-list {
  padding: 20rpx;

  .order-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;

    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 1rpx solid #f8f8f8;

      .order-info {
        .order-number {
          display: block;
          font-size: 26rpx;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .order-time {
          font-size: 22rpx;
          color: #999999;
        }
      }

      .order-status {
        .status-text {
          font-size: 24rpx;
          font-weight: 600;
        }

        &.pending .status-text {
          color: #faad14;
        }

        &.paid .status-text {
          color: #1890ff;
        }

        &.shipped .status-text {
          color: #52c41a;
        }

        &.completed .status-text {
          color: #666666;
        }

        &.cancelled .status-text {
          color: #ff4d4f;
        }
      }
    }

    .product-list {
      .product-item {
        display: flex;
        align-items: center;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f8f8f8;

        &:last-child {
          border-bottom: none;
        }

        .product-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
          margin-right: 20rpx;
        }

        .product-info {
          flex: 1;

          .product-name {
            display: block;
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .product-spec {
            display: block;
            font-size: 22rpx;
            color: #999999;
            margin-bottom: 12rpx;
          }

          .product-price {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .price-text {
              font-size: 26rpx;
              font-weight: 600;
              color: #ff6b35;
            }

            .quantity-text {
              font-size: 22rpx;
              color: #666666;
            }
          }
        }
      }
    }

    .order-amount {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 20rpx 30rpx;
      border-bottom: 1rpx solid #f8f8f8;

      .amount-label {
        font-size: 24rpx;
        color: #666666;
        margin-right: 8rpx;
      }

      .amount-value {
        font-size: 28rpx;
        font-weight: 600;
        color: #ff6b35;
      }
    }

    .order-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 30rpx;

      .action-left {
        .contact-service {
          font-size: 24rpx;
          color: #666666;
          padding: 12rpx 0;
        }
      }

      .action-right {
        display: flex;
        gap: 16rpx;

        .action-btn {
          font-size: 24rpx;
          padding: 12rpx 24rpx;
          border-radius: 20rpx;

          &.secondary {
            color: #666666;
            background-color: #f8f8f8;
            border: 1rpx solid #d9d9d9;
          }

          &.primary {
            color: #ffffff;
            background-color: #ff6b35;
          }
        }
      }
    }
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;

  .load-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.no-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .no-orders-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }

  .no-orders-text {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }

  .no-orders-tip {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }

  .go-shopping-btn {
    padding: 20rpx 40rpx;
    background-color: #ff6b35;
    border-radius: 30rpx;

    .btn-text {
      font-size: 26rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
</style>
