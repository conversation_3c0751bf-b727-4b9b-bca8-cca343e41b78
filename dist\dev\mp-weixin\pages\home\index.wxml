<view class="home data-v-2c5296db"><view class="header data-v-2c5296db"><text class="title data-v-2c5296db">欢迎使用微信小程序脚手架</text><text class="subtitle data-v-2c5296db">基于 uni-app + Vue 3 + TypeScript</text></view><view class="features data-v-2c5296db"><view wx:for="{{a}}" wx:for-item="feature" wx:key="d" class="feature-item data-v-2c5296db"><view class="feature-icon data-v-2c5296db">{{feature.a}}</view><view class="feature-content data-v-2c5296db"><text class="feature-title data-v-2c5296db">{{feature.b}}</text><text class="feature-desc data-v-2c5296db">{{feature.c}}</text></view></view></view><view class="actions data-v-2c5296db"><button class="btn primary data-v-2c5296db" bindtap="{{b}}">获取用户信息</button><button class="btn secondary data-v-2c5296db" bindtap="{{c}}">显示提示</button></view></view>