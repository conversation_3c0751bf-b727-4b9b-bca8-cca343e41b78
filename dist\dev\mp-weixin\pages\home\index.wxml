<view class="home data-v-2c5296db"><view class="header-section data-v-2c5296db"><view class="user-section data-v-2c5296db"><view class="user-info data-v-2c5296db"><image class="avatar data-v-2c5296db" src="{{a}}" mode="aspectFill"/><view class="user-text data-v-2c5296db"><text class="greeting data-v-2c5296db">Hello, {{b}}</text><text class="subtitle data-v-2c5296db">欢迎使用京东购物小程序</text></view></view><view class="header-actions data-v-2c5296db"><view class="action-btn data-v-2c5296db" bindtap="{{c}}"><text class="action-icon data-v-2c5296db">💬</text></view><view class="action-btn notification data-v-2c5296db" bindtap="{{e}}"><text class="action-icon data-v-2c5296db">🔔</text><view wx:if="{{d}}" class="red-dot data-v-2c5296db"></view></view></view></view><view class="search-container data-v-2c5296db"><view class="search-box data-v-2c5296db" bindtap="{{f}}"><text class="search-icon data-v-2c5296db">🔍</text><text class="search-placeholder data-v-2c5296db">搜索人气好物 / 百亿补贴 / 手机数码</text></view></view></view><view class="main-content data-v-2c5296db"><view class="quick-card primary-card data-v-2c5296db"><view class="card-header data-v-2c5296db"><view class="card-icon data-v-2c5296db">➕</view><view class="card-title data-v-2c5296db"><text class="title data-v-2c5296db">快速建单</text><text class="subtitle data-v-2c5296db">行李托运建单</text></view></view><view class="card-content data-v-2c5296db"><view class="feature-item data-v-2c5296db"><text class="feature-icon data-v-2c5296db">📦</text><text class="feature-text data-v-2c5296db">建单记录 32</text></view><view class="tag-group data-v-2c5296db"><text class="tag data-v-2c5296db">PVG</text><text class="tag data-v-2c5296db">SYU</text><text class="tag data-v-2c5296db">MLJ</text></view></view></view><view class="side-cards data-v-2c5296db"><view class="function-card data-v-2c5296db"><view class="card-icon-wrapper data-v-2c5296db"><text class="card-icon data-v-2c5296db">📊</text></view><view class="card-info data-v-2c5296db"><text class="card-title data-v-2c5296db">行李分拣</text><text class="card-desc data-v-2c5296db">24件待分拣</text></view></view><view class="function-card data-v-2c5296db"><view class="card-icon-wrapper green data-v-2c5296db"><text class="card-icon data-v-2c5296db">🎯</text></view><view class="card-info data-v-2c5296db"><text class="card-title data-v-2c5296db">行李装载</text><text class="card-desc data-v-2c5296db">28件待装载</text></view></view></view></view><view class="bottom-nav data-v-2c5296db"><view wx:for="{{g}}" wx:for-item="nav" wx:key="c" class="nav-item data-v-2c5296db" bindtap="{{nav.d}}"><view class="nav-icon data-v-2c5296db">{{nav.a}}</view><text class="nav-text data-v-2c5296db">{{nav.b}}</text></view></view><view class="flight-section data-v-2c5296db"><view class="section-header data-v-2c5296db"><text class="section-title data-v-2c5296db">当前航线行李</text><text class="more-link data-v-2c5296db" bindtap="{{h}}">查看全部 ></text></view><view class="flight-tabs data-v-2c5296db"><text wx:for="{{i}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', 'data-v-2c5296db', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</text></view><view class="flight-list data-v-2c5296db"><view wx:for="{{j}}" wx:for-item="flight" wx:key="g" class="flight-item data-v-2c5296db"><view class="flight-header data-v-2c5296db"><text class="flight-title data-v-2c5296db">{{flight.a}}</text><view class="flight-time data-v-2c5296db"><text class="time-text data-v-2c5296db">{{flight.b}}</text><text class="arrow data-v-2c5296db">✈️</text><text class="time-text data-v-2c5296db">{{flight.c}}</text></view></view><view class="flight-details data-v-2c5296db"><text class="flight-info data-v-2c5296db">{{flight.d}} | {{flight.e}}</text><view class="action-btn-small data-v-2c5296db" bindtap="{{flight.f}}"><text class="data-v-2c5296db">操作</text></view></view></view></view></view></view>