<template>
  <view class="product-detail">
    <!-- 商品图片轮播 -->
    <swiper class="product-swiper" :indicator-dots="true" :autoplay="false" :circular="true">
      <swiper-item v-for="(image, index) in product.images" :key="index">
        <image :src="image" class="swiper-image" mode="aspectFill" @click="previewImage(index)" />
      </swiper-item>
    </swiper>

    <!-- 商品基本信息 -->
    <view class="product-info">
      <view class="price-section">
        <view class="price-main">
          <text class="current-price">¥{{ product.price }}</text>
          <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
        </view>
        <view class="points-info">
          <text class="points-text">购买可得{{ product.pointsReward }}积分</text>
        </view>
      </view>
      
      <text class="product-title">{{ product.name }}</text>
      <text class="product-subtitle" v-if="product.subtitle">{{ product.subtitle }}</text>
      
      <view class="product-tags">
        <text class="tag" v-for="tag in product.tags" :key="tag">{{ tag }}</text>
      </view>
      
      <view class="product-stats">
        <text class="stat-item">销量{{ product.sales }}</text>
        <text class="stat-item">库存{{ product.stock }}</text>
        <text class="stat-item">{{ product.reviews }}条评价</text>
      </view>
    </view>

    <!-- 规格选择 -->
    <view class="spec-section" @click="showSpecModal = true">
      <view class="spec-header">
        <text class="spec-title">规格</text>
        <view class="spec-selected">
          <text class="selected-text">{{ selectedSpec || '请选择规格' }}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>

    <!-- 服务保障 -->
    <view class="service-section">
      <view class="service-header">
        <text class="service-title">服务</text>
      </view>
      <view class="service-list">
        <view class="service-item" v-for="service in services" :key="service.key">
          <text class="service-icon">{{ service.icon }}</text>
          <text class="service-text">{{ service.text }}</text>
        </view>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="detail-section">
      <view class="detail-tabs">
        <view 
          class="tab-item"
          :class="{ active: activeTab === tab.key }"
          v-for="tab in detailTabs"
          :key="tab.key"
          @click="switchTab(tab.key)"
        >
          <text class="tab-text">{{ tab.name }}</text>
        </view>
      </view>
      
      <view class="tab-content">
        <!-- 商品详情 -->
        <view class="detail-content" v-if="activeTab === 'detail'">
          <view class="detail-images">
            <image 
              v-for="(image, index) in product.detailImages" 
              :key="index"
              :src="image" 
              class="detail-image" 
              mode="widthFix" 
            />
          </view>
        </view>
        
        <!-- 商品评价 -->
        <view class="reviews-content" v-if="activeTab === 'reviews'">
          <view class="reviews-summary">
            <view class="rating-info">
              <text class="rating-score">{{ product.rating }}</text>
              <view class="rating-stars">
                <text class="star" v-for="i in 5" :key="i" :class="{ filled: i <= Math.floor(product.rating) }">★</text>
              </view>
            </view>
            <text class="reviews-count">{{ product.reviews }}条评价</text>
          </view>
          
          <view class="review-list">
            <view class="review-item" v-for="review in productReviews" :key="review.id">
              <view class="review-header">
                <image :src="review.avatar" class="reviewer-avatar" mode="aspectFill" />
                <view class="reviewer-info">
                  <text class="reviewer-name">{{ review.username }}</text>
                  <view class="review-rating">
                    <text class="star" v-for="i in 5" :key="i" :class="{ filled: i <= review.rating }">★</text>
                  </view>
                </view>
                <text class="review-time">{{ formatTime(review.createTime) }}</text>
              </view>
              <text class="review-content">{{ review.content }}</text>
              <view class="review-images" v-if="review.images">
                <image 
                  v-for="(image, index) in review.images" 
                  :key="index"
                  :src="image" 
                  class="review-image" 
                  mode="aspectFill" 
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-btn" @click="toggleFavorite">
          <text class="action-icon" :class="{ favorited: isFavorited }">{{ isFavorited ? '❤️' : '🤍' }}</text>
          <text class="action-text">收藏</text>
        </view>
        <view class="action-btn" @click="goToCart">
          <text class="action-icon">🛒</text>
          <text class="action-text">购物车</text>
          <view class="cart-badge" v-if="cartCount > 0">
            <text class="badge-text">{{ cartCount }}</text>
          </view>
        </view>
        <view class="action-btn" @click="contactService">
          <text class="action-icon">💬</text>
          <text class="action-text">客服</text>
        </view>
      </view>
      <view class="action-right">
        <view class="add-cart-btn" @click="addToCart">
          <text class="btn-text">加入购物车</text>
        </view>
        <view class="buy-now-btn" @click="buyNow">
          <text class="btn-text">立即购买</text>
        </view>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <view class="spec-modal" v-if="showSpecModal" @click="showSpecModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择规格</text>
          <text class="close-btn" @click="showSpecModal = false">✕</text>
        </view>
        <view class="spec-options">
          <view class="spec-group" v-for="group in specGroups" :key="group.name">
            <text class="group-name">{{ group.name }}</text>
            <view class="option-list">
              <view 
                class="option-item"
                :class="{ selected: selectedSpecs[group.name] === option }"
                v-for="option in group.options"
                :key="option"
                @click="selectSpec(group.name, option)"
              >
                <text class="option-text">{{ option }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <view class="quantity-control">
            <text class="quantity-label">数量：</text>
            <view class="quantity-input">
              <view class="quantity-btn" @click="decreaseQuantity">-</view>
              <text class="quantity-value">{{ selectedQuantity }}</text>
              <view class="quantity-btn" @click="increaseQuantity">+</view>
            </view>
          </view>
          <view class="confirm-btn" @click="confirmSpec">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface Product {
  id: number
  name: string
  subtitle?: string
  price: number
  originalPrice?: number
  pointsReward: number
  sales: number
  stock: number
  reviews: number
  rating: number
  tags: string[]
  images: string[]
  detailImages: string[]
}

interface Review {
  id: number
  username: string
  avatar: string
  rating: number
  content: string
  createTime: string
  images?: string[]
}

interface Service {
  key: string
  icon: string
  text: string
}

interface SpecGroup {
  name: string
  options: string[]
}

// 响应式数据
const showSpecModal = ref(false)
const activeTab = ref('detail')
const isFavorited = ref(false)
const cartCount = ref(2)
const selectedQuantity = ref(1)
const selectedSpecs = ref<Record<string, string>>({})

const product = ref<Product>({
  id: 1,
  name: '智能手表 运动版',
  subtitle: '健康监测 | 运动追踪 | 长续航',
  price: 299.9,
  originalPrice: 399.9,
  pointsReward: 30,
  sales: 1234,
  stock: 89,
  reviews: 456,
  rating: 4.8,
  tags: ['热销', '新品', '包邮'],
  images: [
    'https://via.placeholder.com/750x750/ff6b35/ffffff?text=手表1',
    'https://via.placeholder.com/750x750/1890ff/ffffff?text=手表2',
    'https://via.placeholder.com/750x750/52c41a/ffffff?text=手表3'
  ],
  detailImages: [
    'https://via.placeholder.com/750x1000/ff6b35/ffffff?text=详情1',
    'https://via.placeholder.com/750x1000/1890ff/ffffff?text=详情2',
    'https://via.placeholder.com/750x1000/52c41a/ffffff?text=详情3'
  ]
})

const services = ref<Service[]>([
  { key: 'return', icon: '🔄', text: '7天无理由退货' },
  { key: 'warranty', icon: '🛡️', text: '1年质保' },
  { key: 'shipping', icon: '🚚', text: '全国包邮' },
  { key: 'authentic', icon: '✅', text: '正品保证' }
])

const detailTabs = ref([
  { key: 'detail', name: '商品详情' },
  { key: 'reviews', name: '用户评价' }
])

const specGroups = ref<SpecGroup[]>([
  {
    name: '颜色',
    options: ['黑色', '白色', '蓝色']
  },
  {
    name: '尺寸',
    options: ['42mm', '46mm']
  }
])

const productReviews = ref<Review[]>([
  {
    id: 1,
    username: '用户***123',
    avatar: 'https://via.placeholder.com/60x60/ff6b35/ffffff?text=头像',
    rating: 5,
    content: '手表很不错，功能齐全，续航能力强，性价比很高！',
    createTime: '2024-01-02 15:30:00',
    images: [
      'https://via.placeholder.com/200x200/ff6b35/ffffff?text=评价图1',
      'https://via.placeholder.com/200x200/1890ff/ffffff?text=评价图2'
    ]
  },
  {
    id: 2,
    username: '用户***456',
    avatar: 'https://via.placeholder.com/60x60/1890ff/ffffff?text=头像',
    rating: 4,
    content: '外观很漂亮，佩戴舒适，运动监测功能很实用。',
    createTime: '2024-01-01 10:20:00'
  }
])

// 计算属性
const selectedSpec = computed(() => {
  const specs = Object.values(selectedSpecs.value)
  return specs.length > 0 ? specs.join(' ') : ''
})

// 方法
const previewImage = (index: number) => {
  uni.previewImage({
    urls: product.value.images,
    current: index
  })
}

const switchTab = (tabKey: string) => {
  activeTab.value = tabKey
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  uni.showToast({
    title: isFavorited.value ? '已收藏' : '已取消收藏',
    icon: 'success'
  })
}

const goToCart = () => {
  uni.switchTab({
    url: '/pages/cart/index'
  })
}

const contactService = () => {
  uni.showToast({
    title: '正在连接客服...',
    icon: 'loading'
  })
}

const selectSpec = (groupName: string, option: string) => {
  selectedSpecs.value[groupName] = option
}

const increaseQuantity = () => {
  if (selectedQuantity.value < product.value.stock) {
    selectedQuantity.value++
  }
}

const decreaseQuantity = () => {
  if (selectedQuantity.value > 1) {
    selectedQuantity.value--
  }
}

const confirmSpec = () => {
  showSpecModal.value = false
}

const addToCart = () => {
  if (!selectedSpec.value) {
    showSpecModal.value = true
    return
  }
  
  uni.showToast({
    title: '已加入购物车',
    icon: 'success'
  })
  cartCount.value++
}

const buyNow = () => {
  if (!selectedSpec.value) {
    showSpecModal.value = true
    return
  }
  
  uni.navigateTo({
    url: `/pages/order/confirm?productId=${product.value.id}&quantity=${selectedQuantity.value}&spec=${encodeURIComponent(selectedSpec.value)}`
  })
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  return time.toLocaleDateString()
}

onLoad((options) => {
  if (options?.id) {
    // 根据ID加载商品数据
    console.log('Product ID:', options.id)
  }
})
</script>

<style lang="scss" scoped>
.product-detail {
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.product-swiper {
  width: 100%;
  height: 750rpx;
  background-color: #ffffff;

  .swiper-image {
    width: 100%;
    height: 100%;
  }
}

.product-info {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .price-section {
    margin-bottom: 20rpx;

    .price-main {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .current-price {
        font-size: 48rpx;
        font-weight: 700;
        color: #ff6b35;
        margin-right: 20rpx;
      }

      .original-price {
        font-size: 28rpx;
        color: #999999;
        text-decoration: line-through;
      }
    }

    .points-info {
      .points-text {
        font-size: 24rpx;
        color: #52c41a;
        background-color: #f6ffed;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
      }
    }
  }

  .product-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    line-height: 1.4;
    margin-bottom: 12rpx;
  }

  .product-subtitle {
    display: block;
    font-size: 26rpx;
    color: #666666;
    margin-bottom: 20rpx;
  }

  .product-tags {
    display: flex;
    gap: 12rpx;
    margin-bottom: 20rpx;

    .tag {
      font-size: 22rpx;
      color: #ff6b35;
      background-color: #fff7f0;
      padding: 6rpx 12rpx;
      border-radius: 12rpx;
    }
  }

  .product-stats {
    display: flex;
    gap: 30rpx;

    .stat-item {
      font-size: 24rpx;
      color: #999999;
    }
  }
}

.spec-section, .service-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;

  .spec-header, .service-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .spec-title, .service-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }

    .spec-selected {
      display: flex;
      align-items: center;

      .selected-text {
        font-size: 26rpx;
        color: #666666;
        margin-right: 8rpx;
      }

      .arrow-icon {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}

.service-section {
  .service-list {
    padding: 30rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .service-item {
      display: flex;
      align-items: center;

      .service-icon {
        font-size: 28rpx;
        margin-right: 12rpx;
      }

      .service-text {
        font-size: 24rpx;
        color: #333333;
      }
    }
  }
}

.detail-section {
  background-color: #ffffff;

  .detail-tabs {
    display: flex;
    border-bottom: 1rpx solid #f0f0f0;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 30rpx 0;

      &.active {
        border-bottom: 4rpx solid #ff6b35;

        .tab-text {
          color: #ff6b35;
          font-weight: 600;
        }
      }

      .tab-text {
        font-size: 28rpx;
        color: #666666;
      }
    }
  }

  .tab-content {
    min-height: 600rpx;

    .detail-content {
      .detail-images {
        .detail-image {
          width: 100%;
          display: block;
        }
      }
    }

    .reviews-content {
      padding: 30rpx;

      .reviews-summary {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        margin-bottom: 30rpx;

        .rating-info {
          display: flex;
          align-items: center;

          .rating-score {
            font-size: 48rpx;
            font-weight: 700;
            color: #ff6b35;
            margin-right: 20rpx;
          }

          .rating-stars {
            .star {
              font-size: 24rpx;
              color: #d9d9d9;

              &.filled {
                color: #faad14;
              }
            }
          }
        }

        .reviews-count {
          font-size: 26rpx;
          color: #666666;
        }
      }

      .review-list {
        .review-item {
          padding: 30rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            .reviewer-avatar {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
              margin-right: 16rpx;
            }

            .reviewer-info {
              flex: 1;

              .reviewer-name {
                display: block;
                font-size: 26rpx;
                color: #333333;
                margin-bottom: 8rpx;
              }

              .review-rating {
                .star {
                  font-size: 20rpx;
                  color: #d9d9d9;

                  &.filled {
                    color: #faad14;
                  }
                }
              }
            }

            .review-time {
              font-size: 22rpx;
              color: #999999;
            }
          }

          .review-content {
            display: block;
            font-size: 26rpx;
            color: #333333;
            line-height: 1.5;
            margin-bottom: 16rpx;
          }

          .review-images {
            display: flex;
            gap: 12rpx;

            .review-image {
              width: 120rpx;
              height: 120rpx;
              border-radius: 8rpx;
            }
          }
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);

  .action-left {
    display: flex;
    gap: 40rpx;
    margin-right: 30rpx;

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .action-icon {
        font-size: 32rpx;
        margin-bottom: 4rpx;

        &.favorited {
          color: #ff6b35;
        }
      }

      .action-text {
        font-size: 20rpx;
        color: #666666;
      }

      .cart-badge {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        min-width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ff6b35;
        border-radius: 50%;

        .badge-text {
          font-size: 20rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }
  }

  .action-right {
    flex: 1;
    display: flex;
    gap: 20rpx;

    .add-cart-btn, .buy-now-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40rpx;

      .btn-text {
        font-size: 28rpx;
        font-weight: 600;
      }
    }

    .add-cart-btn {
      background-color: #fff7f0;
      border: 2rpx solid #ff6b35;

      .btn-text {
        color: #ff6b35;
      }
    }

    .buy-now-btn {
      background-color: #ff6b35;

      .btn-text {
        color: #ffffff;
      }
    }
  }
}

.spec-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .modal-content {
    width: 100%;
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    max-height: 80vh;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }

      .close-btn {
        font-size: 32rpx;
        color: #999999;
      }
    }

    .spec-options {
      margin-bottom: 40rpx;

      .spec-group {
        margin-bottom: 30rpx;

        .group-name {
          display: block;
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 20rpx;
        }

        .option-list {
          display: flex;
          flex-wrap: wrap;
          gap: 20rpx;

          .option-item {
            padding: 16rpx 24rpx;
            border: 2rpx solid #d9d9d9;
            border-radius: 8rpx;

            &.selected {
              border-color: #ff6b35;
              background-color: #fff7f0;

              .option-text {
                color: #ff6b35;
              }
            }

            .option-text {
              font-size: 26rpx;
              color: #333333;
            }
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .quantity-control {
        display: flex;
        align-items: center;

        .quantity-label {
          font-size: 26rpx;
          color: #333333;
          margin-right: 20rpx;
        }

        .quantity-input {
          display: flex;
          align-items: center;
          border: 2rpx solid #d9d9d9;
          border-radius: 8rpx;

          .quantity-btn {
            width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            color: #666666;
          }

          .quantity-value {
            min-width: 80rpx;
            text-align: center;
            font-size: 26rpx;
            color: #333333;
          }
        }
      }

      .confirm-btn {
        padding: 20rpx 40rpx;
        background-color: #ff6b35;
        border-radius: 30rpx;

        .btn-text {
          font-size: 28rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
