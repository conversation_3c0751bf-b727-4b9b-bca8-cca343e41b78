<template>
  <view class="order-confirm">
    <!-- 收货地址 -->
    <view class="address-section" @click="selectAddress">
      <view class="address-content" v-if="selectedAddress">
        <view class="address-header">
          <text class="receiver-name">{{ selectedAddress.name }}</text>
          <text class="receiver-phone">{{ selectedAddress.phone }}</text>
        </view>
        <text class="address-detail">{{ selectedAddress.fullAddress }}</text>
      </view>
      <view class="no-address" v-else>
        <text class="no-address-text">请选择收货地址</text>
      </view>
      <text class="arrow-icon">></text>
    </view>

    <!-- 商品列表 -->
    <view class="products-section">
      <view class="section-header">
        <text class="shop-name">{{ shopInfo.name }}</text>
        <text class="shop-badge">自营</text>
      </view>
      
      <view class="product-list">
        <view class="product-item" v-for="item in orderItems" :key="item.id">
          <image :src="item.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ item.name }}</text>
            <text class="product-spec" v-if="item.spec">{{ item.spec }}</text>
            <view class="product-price">
              <text class="price-text">¥{{ item.price }}</text>
              <text class="quantity-text">×{{ item.quantity }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 配送方式 -->
      <view class="delivery-section">
        <view class="delivery-header">
          <text class="delivery-title">配送方式</text>
        </view>
        <view class="delivery-options">
          <view 
            class="delivery-option"
            :class="{ selected: selectedDelivery === option.key }"
            v-for="option in deliveryOptions"
            :key="option.key"
            @click="selectDelivery(option)"
          >
            <view class="option-info">
              <text class="option-name">{{ option.name }}</text>
              <text class="option-desc">{{ option.desc }}</text>
            </view>
            <text class="option-price">{{ option.price > 0 ? `¥${option.price}` : '免费' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 优惠券 -->
    <view class="coupon-section" @click="selectCoupon">
      <view class="coupon-content">
        <text class="coupon-title">优惠券</text>
        <view class="coupon-selected">
          <text class="selected-text">{{ selectedCoupon ? `已选择 -¥${selectedCoupon.discount}` : '请选择优惠券' }}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>

    <!-- 积分抵扣 -->
    <view class="points-section">
      <view class="points-header">
        <text class="points-title">积分抵扣</text>
        <view class="points-toggle">
          <switch :checked="usePoints" @change="togglePoints" color="#ff6b35" />
        </view>
      </view>
      <view class="points-info" v-if="usePoints">
        <text class="points-text">使用{{ pointsToUse }}积分，抵扣¥{{ pointsDiscount.toFixed(2) }}</text>
        <text class="points-balance">可用积分：{{ userPoints }}</text>
      </view>
    </view>

    <!-- 订单备注 -->
    <view class="remark-section">
      <view class="remark-header">
        <text class="remark-title">订单备注</text>
      </view>
      <textarea 
        class="remark-input" 
        v-model="orderRemark" 
        placeholder="选填，请输入订单备注"
        maxlength="100"
      />
    </view>

    <!-- 发票信息 -->
    <view class="invoice-section" @click="selectInvoice">
      <view class="invoice-content">
        <text class="invoice-title">发票信息</text>
        <view class="invoice-selected">
          <text class="selected-text">{{ selectedInvoice ? selectedInvoice.title : '不开发票' }}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="cost-section">
      <view class="cost-item">
        <text class="cost-label">商品金额</text>
        <text class="cost-value">¥{{ productTotal.toFixed(2) }}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">运费</text>
        <text class="cost-value">{{ deliveryFee > 0 ? `¥${deliveryFee.toFixed(2)}` : '免费' }}</text>
      </view>
      <view class="cost-item" v-if="selectedCoupon">
        <text class="cost-label">优惠券</text>
        <text class="cost-value discount">-¥{{ selectedCoupon.discount.toFixed(2) }}</text>
      </view>
      <view class="cost-item" v-if="usePoints">
        <text class="cost-label">积分抵扣</text>
        <text class="cost-value discount">-¥{{ pointsDiscount.toFixed(2) }}</text>
      </view>
      <view class="cost-total">
        <text class="total-label">实付款</text>
        <text class="total-value">¥{{ finalTotal.toFixed(2) }}</text>
      </view>
    </view>

    <!-- 底部提交 -->
    <view class="submit-section">
      <view class="submit-info">
        <text class="submit-text">共{{ totalQuantity }}件商品</text>
        <view class="submit-total">
          <text class="total-label">实付：</text>
          <text class="total-price">¥{{ finalTotal.toFixed(2) }}</text>
        </view>
      </view>
      <view class="submit-btn" @click="submitOrder">
        <text class="btn-text">提交订单</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface Address {
  id: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  fullAddress: string
  isDefault: boolean
}

interface OrderItem {
  id: number
  name: string
  image: string
  price: number
  quantity: number
  spec?: string
}

interface DeliveryOption {
  key: string
  name: string
  desc: string
  price: number
}

interface Coupon {
  id: number
  name: string
  discount: number
  minAmount: number
}

interface Invoice {
  type: string
  title: string
  taxNumber?: string
}

// 响应式数据
const selectedAddress = ref<Address | null>({
  id: 1,
  name: '张三',
  phone: '138****8888',
  province: '广东省',
  city: '深圳市',
  district: '南山区',
  detail: '科技园南区深南大道10000号',
  fullAddress: '广东省深圳市南山区科技园南区深南大道10000号',
  isDefault: true
})

const selectedDelivery = ref('standard')
const selectedCoupon = ref<Coupon | null>(null)
const selectedInvoice = ref<Invoice | null>(null)
const usePoints = ref(false)
const orderRemark = ref('')
const userPoints = ref(1580)

const shopInfo = ref({
  name: '官方旗舰店'
})

const orderItems = ref<OrderItem[]>([
  {
    id: 1,
    name: '智能手表 运动版',
    image: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=手表',
    price: 299.9,
    quantity: 1,
    spec: '黑色 42mm'
  },
  {
    id: 2,
    name: '蓝牙耳机',
    image: 'https://via.placeholder.com/120x120/1890ff/ffffff?text=耳机',
    price: 199.9,
    quantity: 2
  }
])

const deliveryOptions = ref<DeliveryOption[]>([
  {
    key: 'standard',
    name: '标准快递',
    desc: '预计3-5天送达',
    price: 0
  },
  {
    key: 'express',
    name: '次日达',
    desc: '次日送达，限部分地区',
    price: 15
  }
])

// 计算属性
const productTotal = computed(() => {
  return orderItems.value.reduce((total, item) => {
    return total + item.price * item.quantity
  }, 0)
})

const totalQuantity = computed(() => {
  return orderItems.value.reduce((total, item) => {
    return total + item.quantity
  }, 0)
})

const deliveryFee = computed(() => {
  const option = deliveryOptions.value.find(opt => opt.key === selectedDelivery.value)
  return option ? option.price : 0
})

const pointsToUse = computed(() => {
  if (!usePoints.value) return 0
  // 每100积分抵扣1元，最多抵扣订单金额的50%
  const maxPoints = Math.floor((productTotal.value + deliveryFee.value) * 0.5 * 100)
  return Math.min(userPoints.value, maxPoints)
})

const pointsDiscount = computed(() => {
  return pointsToUse.value / 100
})

const finalTotal = computed(() => {
  let total = productTotal.value + deliveryFee.value
  
  if (selectedCoupon.value) {
    total -= selectedCoupon.value.discount
  }
  
  if (usePoints.value) {
    total -= pointsDiscount.value
  }
  
  return Math.max(0, total)
})

// 方法
const selectAddress = () => {
  uni.navigateTo({
    url: '/pages/address/list?from=order'
  })
}

const selectDelivery = (option: DeliveryOption) => {
  selectedDelivery.value = option.key
}

const selectCoupon = () => {
  uni.navigateTo({
    url: '/pages/coupon/list?from=order'
  })
}

const selectInvoice = () => {
  uni.navigateTo({
    url: '/pages/order/invoice'
  })
}

const togglePoints = (e: any) => {
  usePoints.value = e.detail.value
}

const submitOrder = () => {
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请选择收货地址',
      icon: 'none'
    })
    return
  }
  
  const orderData = {
    items: orderItems.value,
    address: selectedAddress.value,
    delivery: selectedDelivery.value,
    coupon: selectedCoupon.value,
    invoice: selectedInvoice.value,
    usePoints: usePoints.value,
    pointsUsed: pointsToUse.value,
    remark: orderRemark.value,
    total: finalTotal.value
  }
  
  uni.navigateTo({
    url: `/pages/payment/index?orderData=${encodeURIComponent(JSON.stringify(orderData))}`
  })
}

onLoad((options) => {
  // 处理从购物车或商品详情页传来的参数
  if (options?.cartIds) {
    console.log('Cart IDs:', options.cartIds)
  }
  if (options?.productId) {
    console.log('Product ID:', options.productId)
  }
})
</script>

<style lang="scss" scoped>
.order-confirm {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-section {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .address-content {
    flex: 1;

    .address-header {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      .receiver-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
        margin-right: 20rpx;
      }

      .receiver-phone {
        font-size: 26rpx;
        color: #666666;
      }
    }

    .address-detail {
      font-size: 26rpx;
      color: #666666;
      line-height: 1.4;
    }
  }

  .no-address {
    flex: 1;

    .no-address-text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  .arrow-icon {
    font-size: 24rpx;
    color: #999999;
  }
}

.products-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;

  .section-header {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .shop-name {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-right: 12rpx;
    }

    .shop-badge {
      font-size: 20rpx;
      color: #ff6b35;
      background-color: #fff7f0;
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
    }
  }

  .product-list {
    .product-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f8f8f8;

      &:last-child {
        border-bottom: none;
      }

      .product-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .product-info {
        flex: 1;

        .product-name {
          display: block;
          font-size: 26rpx;
          color: #333333;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-spec {
          display: block;
          font-size: 22rpx;
          color: #999999;
          margin-bottom: 12rpx;
        }

        .product-price {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .price-text {
            font-size: 28rpx;
            font-weight: 600;
            color: #ff6b35;
          }

          .quantity-text {
            font-size: 24rpx;
            color: #666666;
          }
        }
      }
    }
  }

  .delivery-section {
    border-top: 1rpx solid #f0f0f0;

    .delivery-header {
      padding: 30rpx 30rpx 20rpx;

      .delivery-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333333;
      }
    }

    .delivery-options {
      .delivery-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 30rpx;

        &.selected {
          background-color: #fff7f0;

          .option-name {
            color: #ff6b35;
          }
        }

        .option-info {
          .option-name {
            display: block;
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 4rpx;
          }

          .option-desc {
            font-size: 22rpx;
            color: #999999;
          }
        }

        .option-price {
          font-size: 26rpx;
          color: #ff6b35;
          font-weight: 600;
        }
      }
    }
  }
}

.coupon-section, .invoice-section {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .coupon-content, .invoice-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .coupon-title, .invoice-title {
      font-size: 28rpx;
      color: #333333;
    }

    .coupon-selected, .invoice-selected {
      display: flex;
      align-items: center;

      .selected-text {
        font-size: 26rpx;
        color: #666666;
        margin-right: 8rpx;
      }

      .arrow-icon {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}

.points-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .points-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .points-title {
      font-size: 28rpx;
      color: #333333;
    }
  }

  .points-info {
    .points-text {
      display: block;
      font-size: 24rpx;
      color: #52c41a;
      margin-bottom: 8rpx;
    }

    .points-balance {
      font-size: 22rpx;
      color: #999999;
    }
  }
}

.remark-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .remark-header {
    margin-bottom: 20rpx;

    .remark-title {
      font-size: 28rpx;
      color: #333333;
    }
  }

  .remark-input {
    width: 100%;
    min-height: 120rpx;
    font-size: 26rpx;
    color: #333333;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    padding: 20rpx;
    box-sizing: border-box;
  }
}

.cost-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .cost-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .cost-label {
      font-size: 26rpx;
      color: #666666;
    }

    .cost-value {
      font-size: 26rpx;
      color: #333333;

      &.discount {
        color: #52c41a;
      }
    }
  }

  .cost-total {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;

    .total-label {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }

    .total-value {
      font-size: 32rpx;
      font-weight: 700;
      color: #ff6b35;
    }
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);

  .submit-info {
    flex: 1;
    margin-right: 30rpx;

    .submit-text {
      display: block;
      font-size: 24rpx;
      color: #666666;
      margin-bottom: 4rpx;
    }

    .submit-total {
      display: flex;
      align-items: center;

      .total-label {
        font-size: 26rpx;
        color: #333333;
      }

      .total-price {
        font-size: 32rpx;
        font-weight: 700;
        color: #ff6b35;
      }
    }
  }

  .submit-btn {
    padding: 24rpx 40rpx;
    background-color: #ff6b35;
    border-radius: 30rpx;

    .btn-text {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
</style>
