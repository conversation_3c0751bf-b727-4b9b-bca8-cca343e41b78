"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const features = common_vendor.ref([
      {
        id: 1,
        icon: "🚀",
        title: "现代化技术栈",
        description: "Vue 3 + TypeScript + Vite"
      },
      {
        id: 2,
        icon: "📱",
        title: "跨平台支持",
        description: "一套代码多端运行"
      },
      {
        id: 3,
        icon: "🎨",
        title: "组件化开发",
        description: "可复用的UI组件"
      },
      {
        id: 4,
        icon: "⚡",
        title: "快速开发",
        description: "完整的开发工具链"
      }
    ]);
    common_vendor.onMounted(() => {
      console.log("首页加载完成");
    });
    const handleGetUserInfo = () => {
      common_vendor.index.getUserProfile({
        desc: "用于完善用户资料",
        success: (res) => {
          console.log("用户信息：", res.userInfo);
          common_vendor.index.showToast({
            title: "获取成功",
            icon: "success"
          });
        },
        fail: (err) => {
          console.error("获取用户信息失败：", err);
          common_vendor.index.showToast({
            title: "获取失败",
            icon: "error"
          });
        }
      });
    };
    const handleShowToast = () => {
      common_vendor.index.showToast({
        title: "Hello World!",
        icon: "success",
        duration: 2e3
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(features.value, (feature, k0, i0) => {
          return {
            a: common_vendor.t(feature.icon),
            b: common_vendor.t(feature.title),
            c: common_vendor.t(feature.description),
            d: feature.id
          };
        }),
        b: common_vendor.o(handleGetUserInfo),
        c: common_vendor.o(handleShowToast)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2c5296db"]]);
wx.createPage(MiniProgramPage);
