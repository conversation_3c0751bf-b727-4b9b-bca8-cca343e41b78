"use strict";
const common_vendor = require("../../common/vendor.js");
const userAvatar = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzQyODVmNCIvPgogIDxjaXJjbGUgY3g9IjQwIiBjeT0iMzAiIHI9IjE1IiBmaWxsPSIjZmZmZmZmIi8+CiAgPHBhdGggZD0iTTEwIDcwYzAtMTUgMTMuNDMtMjcgMzAtMjdzMzAgMTIgMzAgMjciIGZpbGw9IiNmZmZmZmYiLz4KPC9zdmc+";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userName = common_vendor.ref("东小航");
    const hasNotification = common_vendor.ref(true);
    const bottomNavs = common_vendor.ref([
      { id: 1, icon: "🎯", title: "改签行李" },
      { id: 2, icon: "📧", title: "转运行李" },
      { id: 3, icon: "🎯", title: "中转分拣" },
      { id: 4, icon: "⏰", title: "航班追踪" },
      { id: 5, icon: "📝", title: "交接记录" }
    ]);
    const flightTabs = common_vendor.ref([
      { id: "hongqiao-t2", name: "虹桥T2" },
      { id: "pudong-t1", name: "浦东T1" },
      { id: "shuangliu-t2", name: "双流T2" },
      { id: "baiyun-t3", name: "白云T3" },
      { id: "daxing", name: "大兴" }
    ]);
    const activeTab = common_vendor.ref("hongqiao-t2");
    const flightData = common_vendor.ref({
      "hongqiao-t2": [
        {
          id: 1,
          title: "虹桥T2航站行李@PVG容器",
          departTime: "10:40虹桥T2",
          arriveTime: "12:40首都T2",
          aircraft: "已装载37件",
          seat: "MU5080"
        },
        {
          id: 2,
          title: "虹桥T2航站行李@SVU容器",
          departTime: "14:20虹桥T2",
          arriveTime: "16:30白云T2",
          aircraft: "待装载15件",
          seat: "CZ3847"
        }
      ],
      "pudong-t1": [
        {
          id: 3,
          title: "浦东T1航站行李@SHA容器",
          departTime: "08:30浦东T1",
          arriveTime: "11:20成都T1",
          aircraft: "已装载42件",
          seat: "CA1234"
        }
      ],
      "shuangliu-t2": [
        {
          id: 4,
          title: "双流T2航站行李@CTU容器",
          departTime: "13:15双流T2",
          arriveTime: "15:45深圳T3",
          aircraft: "装载中28件",
          seat: "ZH9876"
        }
      ],
      "baiyun-t3": [
        {
          id: 5,
          title: "白云T3航站行李@CAN容器",
          departTime: "16:40白云T3",
          arriveTime: "19:20杭州T3",
          aircraft: "待分拣33件",
          seat: "MF5432"
        }
      ],
      "daxing": [
        {
          id: 6,
          title: "大兴航站行李@PKX容器",
          departTime: "11:25大兴",
          arriveTime: "14:10西安T3",
          aircraft: "已装载51件",
          seat: "CA9988"
        }
      ]
    });
    const currentFlights = common_vendor.ref(flightData.value[activeTab.value] || []);
    common_vendor.onMounted(() => {
      console.log("航旅纵横风格首页加载完成");
    });
    const switchTab = (tabId) => {
      activeTab.value = tabId;
      currentFlights.value = flightData.value[tabId] || [];
    };
    const handleSearch = () => {
      common_vendor.index.showToast({
        title: "搜索功能",
        icon: "none"
      });
    };
    const handleMessage = () => {
      common_vendor.index.showToast({
        title: "消息中心",
        icon: "none"
      });
    };
    const handleNotification = () => {
      hasNotification.value = false;
      common_vendor.index.showToast({
        title: "通知中心",
        icon: "none"
      });
    };
    const handleNavClick = (nav) => {
      common_vendor.index.showToast({
        title: `${nav.title}功能`,
        icon: "none"
      });
    };
    const handleMoreFlights = () => {
      common_vendor.index.showToast({
        title: "查看全部航班",
        icon: "none"
      });
    };
    const handleFlightAction = (flight) => {
      common_vendor.index.showToast({
        title: `操作${flight.title}`,
        icon: "none"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userAvatar,
        b: common_vendor.t(userName.value),
        c: common_vendor.o(handleMessage),
        d: hasNotification.value
      }, hasNotification.value ? {} : {}, {
        e: common_vendor.o(handleNotification),
        f: common_vendor.o(handleSearch),
        g: common_vendor.f(bottomNavs.value, (nav, k0, i0) => {
          return {
            a: common_vendor.t(nav.icon),
            b: common_vendor.t(nav.title),
            c: nav.id,
            d: common_vendor.o(($event) => handleNavClick(nav), nav.id)
          };
        }),
        h: common_vendor.o(handleMoreFlights),
        i: common_vendor.f(flightTabs.value, (tab, k0, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: tab.id,
            c: activeTab.value === tab.id ? 1 : "",
            d: common_vendor.o(($event) => switchTab(tab.id), tab.id)
          };
        }),
        j: common_vendor.f(currentFlights.value, (flight, k0, i0) => {
          return {
            a: common_vendor.t(flight.title),
            b: common_vendor.t(flight.departTime),
            c: common_vendor.t(flight.arriveTime),
            d: common_vendor.t(flight.aircraft),
            e: common_vendor.t(flight.seat),
            f: common_vendor.o(($event) => handleFlightAction(flight), flight.id),
            g: flight.id
          };
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2c5296db"]]);
wx.createPage(MiniProgramPage);
