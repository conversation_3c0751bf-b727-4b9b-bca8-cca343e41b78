<!DOCTYPE html>
<html>
<head>
    <title>SVG to P<PERSON> Batch Converter</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview { 
            display: inline-block; 
            margin: 10px; 
            text-align: center; 
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            background: white;
        }
        .icon-preview img { 
            border: 1px solid #ccc; 
            margin: 5px; 
            border-radius: 4px;
        }
        button { 
            padding: 10px 20px; 
            margin: 10px; 
            background: #1677ff; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
        }
        button:hover { background: #0056d3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        #progress { 
            margin: 20px 0; 
            padding: 10px;
            background: #e3f2fd;
            border-radius: 4px;
            font-weight: bold;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        h1 { color: #333; text-align: center; }
        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SVG to PNG 批量转换器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"转换所有SVG为PNG"按钮开始转换</li>
                <li>转换完成后，点击"下载所有PNG文件"批量下载</li>
                <li>图标尺寸：88x88px（高清版本，适合各种设备）</li>
                <li>支持透明背景</li>
            </ol>
        </div>
        
        <div style="text-align: center;">
            <button onclick="convertAllIcons()" id="convertBtn">🔄 转换所有SVG为PNG</button>
            <button onclick="downloadAllPNG()" id="downloadBtn" disabled>📥 下载所有PNG文件</button>
        </div>
        
        <div id="progress"></div>
        <div id="output" class="icon-grid"></div>
    </div>
    
    <script>
        // 读取所有SVG文件内容
        const svgFiles = [
            'home.svg', 'home-active.svg',
            'finance.svg', 'finance-active.svg', 
            'fund.svg', 'fund-active.svg',
            'profile.svg', 'profile-active.svg',
            'search.svg', 'message.svg', 'notification.svg',
            'add.svg', 'package.svg', 'chart.svg', 
            'target.svg', 'mail.svg', 'clock.svg', 'document.svg'
        ];
        
        // SVG内容定义
        const svgContents = {
            'home.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 8L8 18V36H16V26H28V36H36V18L22 8Z" fill="#999999"/>
            </svg>`,
            'home-active.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 8L8 18V36H16V26H28V36H36V18L22 8Z" fill="#1677ff"/>
            </svg>`,
            'finance.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="22" r="16" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M22 14V30M18 18H26M18 26H26" stroke="#999999" stroke-width="2"/>
            </svg>`,
            'finance-active.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="22" r="16" stroke="#1677ff" stroke-width="2" fill="none"/>
                <path d="M22 14V30M18 18H26M18 26H26" stroke="#1677ff" stroke-width="2"/>
            </svg>`,
            'fund.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 32L16 24L24 28L36 16" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M30 16H36V22" stroke="#999999" stroke-width="2" fill="none"/>
                <circle cx="16" cy="24" r="2" fill="#999999"/>
                <circle cx="24" cy="28" r="2" fill="#999999"/>
                <circle cx="36" cy="16" r="2" fill="#999999"/>
            </svg>`,
            'fund-active.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 32L16 24L24 28L36 16" stroke="#1677ff" stroke-width="2" fill="none"/>
                <path d="M30 16H36V22" stroke="#1677ff" stroke-width="2" fill="none"/>
                <circle cx="16" cy="24" r="2" fill="#1677ff"/>
                <circle cx="24" cy="28" r="2" fill="#1677ff"/>
                <circle cx="36" cy="16" r="2" fill="#1677ff"/>
            </svg>`,
            'profile.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="16" r="6" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M8 36C8 28 14 22 22 22C30 22 36 28 36 36" stroke="#999999" stroke-width="2" fill="none"/>
            </svg>`,
            'profile-active.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="16" r="6" stroke="#1677ff" stroke-width="2" fill="none"/>
                <path d="M8 36C8 28 14 22 22 22C30 22 36 28 36 36" stroke="#1677ff" stroke-width="2" fill="none"/>
            </svg>`,
            'search.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="12" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="30 30L36 36" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
            </svg>`,
            'message.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M36 12C36 10.9 35.1 10 34 10H10C8.9 10 8 10.9 8 12V28C8 29.1 8.9 30 10 30H30L36 36V12Z" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M16 18H28M16 22H24" stroke="#999999" stroke-width="1"/>
            </svg>`,
            'notification.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 4C19.8 4 18 5.8 18 8V18C18 22.4 14.4 26 10 26V28H34V26C29.6 26 26 22.4 26 18V8C26 5.8 24.2 4 22 4Z" fill="#999999"/>
                <path d="M19 32C19 34.2 20.8 36 23 36C25.2 36 27 34.2 27 32H19Z" fill="#999999"/>
            </svg>`,
            'add.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="22" r="18" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M22 14V30M14 22H30" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
            </svg>`,
            'package.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="8" y="12" width="28" height="24" rx="2" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M8 20H36" stroke="#999999" stroke-width="2"/>
                <path d="M22 12V20" stroke="#999999" stroke-width="2"/>
                <path d="M14 8L22 12L30 8" stroke="#999999" stroke-width="2" fill="none"/>
            </svg>`,
            'chart.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="6" y="6" width="32" height="32" rx="2" stroke="#999999" stroke-width="2" fill="none"/>
                <rect x="10" y="26" width="4" height="8" fill="#999999"/>
                <rect x="16" y="20" width="4" height="14" fill="#999999"/>
                <rect x="22" y="14" width="4" height="20" fill="#999999"/>
                <rect x="28" y="18" width="4" height="16" fill="#999999"/>
            </svg>`,
            'target.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="22" r="16" stroke="#999999" stroke-width="2" fill="none"/>
                <circle cx="22" cy="22" r="10" stroke="#999999" stroke-width="2" fill="none"/>
                <circle cx="22" cy="22" r="4" fill="#999999"/>
            </svg>`,
            'mail.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="6" y="12" width="32" height="20" rx="2" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M6 12L22 24L38 12" stroke="#999999" stroke-width="2" fill="none"/>
            </svg>`,
            'clock.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="22" cy="22" r="16" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M22 10V22L30 30" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
            </svg>`,
            'document.svg': `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 6H28L36 14V36C36 37.1 35.1 38 34 38H12C10.9 38 10 37.1 10 36V8C10 6.9 10.9 6 12 6Z" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M28 6V14H36" stroke="#999999" stroke-width="2" fill="none"/>
                <path d="M16 22H28M16 26H28M16 30H24" stroke="#999999" stroke-width="1"/>
            </svg>`
        };
        
        let pngDataUrls = {};
        
        function svgToPng(svgString, filename) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                // 使用88x88高清尺寸
                canvas.width = 88;
                canvas.height = 88;
                
                img.onload = function() {
                    // 清除画布，保持透明背景
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(img, 0, 0, 88, 88);
                    
                    const dataUrl = canvas.toDataURL('image/png');
                    pngDataUrls[filename] = dataUrl;
                    
                    // 创建预览
                    const preview = document.createElement('div');
                    preview.className = 'icon-preview';
                    preview.innerHTML = `
                        <div><strong>${filename.replace('.svg', '.png')}</strong></div>
                        <img src="${dataUrl}" width="44" height="44" alt="${filename}">
                        <div style="font-size: 12px; color: #666;">88x88px</div>
                    `;
                    document.getElementById('output').appendChild(preview);
                    
                    resolve(dataUrl);
                };
                
                img.onerror = function() {
                    console.error('Failed to load SVG:', filename);
                    resolve(null);
                };
                
                const svgBlob = new Blob([svgString], {type: 'image/svg+xml'});
                const url = URL.createObjectURL(svgBlob);
                img.src = url;
            });
        }
        
        async function convertAllIcons() {
            document.getElementById('output').innerHTML = '';
            document.getElementById('convertBtn').disabled = true;
            document.getElementById('downloadBtn').disabled = true;
            
            const totalFiles = svgFiles.length;
            let converted = 0;
            
            for (const filename of svgFiles) {
                const svgContent = svgContents[filename];
                if (svgContent) {
                    document.getElementById('progress').innerHTML = `🔄 转换中 ${converted + 1}/${totalFiles}: ${filename}`;
                    await svgToPng(svgContent, filename);
                    converted++;
                } else {
                    console.warn('SVG content not found for:', filename);
                }
            }
            
            document.getElementById('progress').innerHTML = `✅ 转换完成！成功转换 ${converted} 个图标`;
            document.getElementById('convertBtn').disabled = false;
            document.getElementById('downloadBtn').disabled = false;
        }
        
        function downloadAllPNG() {
            let downloaded = 0;
            Object.keys(pngDataUrls).forEach(filename => {
                const link = document.createElement('a');
                link.download = filename.replace('.svg', '.png');
                link.href = pngDataUrls[filename];
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                downloaded++;
            });
            
            document.getElementById('progress').innerHTML = `📥 已下载 ${downloaded} 个PNG文件`;
        }
    </script>
</body>
</html>
