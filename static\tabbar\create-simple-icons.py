#!/usr/bin/env python3
"""
创建简单的PNG图标文件
需要安装 Pillow: pip install Pillow
"""

from PIL import Image, ImageDraw
import os

# 确保目录存在
os.makedirs('static/tabbar', exist_ok=True)

def create_icon(name, shape, color, size=44):
    """创建简单的图标"""
    # 创建透明背景的图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    
    if shape == 'home':
        # 房子形状
        points = [
            (center, 8),      # 顶点
            (8, 18),          # 左下
            (8, 36),          # 左底
            (16, 36),         # 左内
            (16, 26),         # 左内上
            (28, 26),         # 右内上
            (28, 36),         # 右内
            (36, 36),         # 右底
            (36, 18),         # 右下
        ]
        draw.polygon(points, fill=color)
        
    elif shape == 'finance':
        # 圆形 + 美元符号
        draw.ellipse([6, 6, 38, 38], outline=color, width=2)
        # 简化的美元符号
        draw.rectangle([20, 14, 24, 30], fill=color)
        draw.rectangle([16, 18, 28, 22], fill=color)
        draw.rectangle([16, 22, 28, 26], fill=color)
        
    elif shape == 'fund':
        # 上升箭头 + 边框
        draw.rectangle([6, 6, 38, 38], outline=color, width=2)
        # 上升线条
        points = [(8, 32), (16, 24), (22, 28), (36, 14)]
        for i in range(len(points)-1):
            draw.line([points[i], points[i+1]], fill=color, width=2)
            
    elif shape == 'profile':
        # 人形图标
        # 头部
        draw.ellipse([16, 10, 28, 22], fill=color)
        # 身体
        draw.ellipse([8, 26, 36, 38], fill=color)
        
    elif shape == 'search':
        # 搜索图标
        draw.ellipse([8, 8, 32, 32], outline=color, width=2)
        draw.line([(28, 28), (36, 36)], fill=color, width=2)
        
    elif shape == 'message':
        # 消息气泡
        points = [
            (8, 8), (36, 8), (36, 28), (14, 28), (8, 36), (8, 8)
        ]
        draw.polygon(points, fill=color)
        
    elif shape == 'notification':
        # 铃铛
        draw.ellipse([14, 4, 30, 20], fill=color)
        draw.rectangle([18, 20, 26, 28], fill=color)
        draw.ellipse([19, 32, 25, 36], fill=color)
        
    elif shape == 'add':
        # 加号
        draw.ellipse([4, 4, 40, 40], outline=color, width=2)
        draw.line([(22, 14), (22, 30)], fill=color, width=2)
        draw.line([(14, 22), (30, 22)], fill=color, width=2)
        
    return img

# 颜色定义
NORMAL_COLOR = (153, 153, 153, 255)  # #999999
ACTIVE_COLOR = (22, 119, 255, 255)   # #1677ff

# 创建TabBar图标
icons = [
    ('home', 'home'),
    ('finance', 'finance'), 
    ('fund', 'fund'),
    ('profile', 'profile')
]

for icon_name, shape in icons:
    # 普通状态
    normal_icon = create_icon(icon_name, shape, NORMAL_COLOR)
    normal_icon.save(f'static/tabbar/{icon_name}.png')
    
    # 选中状态
    active_icon = create_icon(icon_name, shape, ACTIVE_COLOR)
    active_icon.save(f'static/tabbar/{icon_name}-active.png')

# 创建其他图标
other_icons = [
    ('search', 'search'),
    ('message', 'message'),
    ('notification', 'notification'),
    ('add', 'add')
]

for icon_name, shape in other_icons:
    icon = create_icon(icon_name, shape, NORMAL_COLOR)
    icon.save(f'static/tabbar/{icon_name}.png')

print("PNG图标创建完成！")
print("TabBar图标:")
for icon_name, _ in icons:
    print(f"  - {icon_name}.png / {icon_name}-active.png")
print("其他图标:")
for icon_name, _ in other_icons:
    print(f"  - {icon_name}.png")
