<template>
  <view class="cart-container">
    <!-- 顶部导航 -->
    <view class="cart-header">
      <text class="header-title">购物车</text>
      <view class="header-actions">
        <text class="edit-btn" @click="toggleEditMode">{{ isEditMode ? '完成' : '编辑' }}</text>
      </view>
    </view>

    <!-- 购物车列表 -->
    <scroll-view class="cart-list" scroll-y v-if="cartItems.length > 0">
      <view class="cart-item" v-for="item in cartItems" :key="item.id">
        <view class="item-checkbox" @click="toggleItemSelect(item)">
          <text class="checkbox-icon" :class="{ checked: item.selected }">{{ item.selected ? '✓' : '' }}</text>
        </view>
        
        <image :src="item.image" class="item-image" mode="aspectFill" @click="goToProductDetail(item)" />
        
        <view class="item-info">
          <text class="item-name" @click="goToProductDetail(item)">{{ item.name }}</text>
          <text class="item-spec" v-if="item.spec">{{ item.spec }}</text>
          
          <view class="item-price-section">
            <view class="price-info">
              <text class="current-price">¥{{ item.price }}</text>
              <text class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</text>
            </view>
            <view class="points-info" v-if="item.pointsReward">
              <text class="points-text">+{{ item.pointsReward }}积分</text>
            </view>
          </view>
          
          <view class="item-actions">
            <view class="quantity-control" v-if="!isEditMode">
              <view class="quantity-btn" @click="decreaseQuantity(item)">
                <text class="btn-text">-</text>
              </view>
              <text class="quantity-text">{{ item.quantity }}</text>
              <view class="quantity-btn" @click="increaseQuantity(item)">
                <text class="btn-text">+</text>
              </view>
            </view>
            
            <view class="edit-actions" v-if="isEditMode">
              <view class="action-btn favorite-btn" @click="addToFavorite(item)">
                <text class="action-icon">❤️</text>
                <text class="action-text">收藏</text>
              </view>
              <view class="action-btn delete-btn" @click="removeItem(item)">
                <text class="action-icon">🗑️</text>
                <text class="action-text">删除</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 空购物车 -->
    <view class="empty-cart" v-else>
      <text class="empty-icon">🛒</text>
      <text class="empty-text">购物车空空如也</text>
      <text class="empty-desc">快去挑选心仪的商品吧</text>
      <view class="empty-action" @click="goToHome">
        <text class="action-text">去逛逛</text>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section" v-if="cartItems.length > 0">
      <view class="section-header">
        <text class="section-title">为你推荐</text>
      </view>
      <scroll-view class="recommend-list" scroll-x>
        <view class="recommend-item" v-for="product in recommendProducts" :key="product.id" @click="goToProductDetail(product)">
          <image :src="product.image" class="recommend-image" mode="aspectFill" />
          <text class="recommend-name">{{ product.name }}</text>
          <text class="recommend-price">¥{{ product.price }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <view class="cart-footer" v-if="cartItems.length > 0">
      <view class="footer-left">
        <view class="select-all" @click="toggleSelectAll">
          <text class="checkbox-icon" :class="{ checked: isAllSelected }">{{ isAllSelected ? '✓' : '' }}</text>
          <text class="select-text">全选</text>
        </view>
        <view class="total-info" v-if="!isEditMode">
          <text class="total-label">合计：</text>
          <text class="total-price">¥{{ totalPrice.toFixed(2) }}</text>
          <text class="total-points">+{{ totalPoints }}积分</text>
        </view>
      </view>
      
      <view class="footer-right">
        <view class="action-button" v-if="!isEditMode" @click="goToCheckout">
          <text class="button-text">结算({{ selectedCount }})</text>
        </view>
        <view class="action-button delete-button" v-else @click="batchDelete">
          <text class="button-text">删除({{ selectedCount }})</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface CartItem {
  id: number
  name: string
  image: string
  price: number
  originalPrice?: number
  spec?: string
  quantity: number
  pointsReward: number
  selected: boolean
}

interface Product {
  id: number
  name: string
  image: string
  price: number
}

// 响应式数据
const isEditMode = ref(false)

const cartItems = ref<CartItem[]>([
  {
    id: 1,
    name: '智能手表 运动版',
    image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=手表',
    price: 299.9,
    originalPrice: 399.9,
    spec: '黑色 42mm',
    quantity: 1,
    pointsReward: 30,
    selected: true
  },
  {
    id: 2,
    name: '蓝牙耳机 降噪版',
    image: 'https://via.placeholder.com/200x200/1890ff/ffffff?text=耳机',
    price: 199.9,
    spec: '白色',
    quantity: 2,
    pointsReward: 20,
    selected: true
  },
  {
    id: 3,
    name: '无线充电器',
    image: 'https://via.placeholder.com/200x200/52c41a/ffffff?text=充电器',
    price: 89.9,
    originalPrice: 129.9,
    quantity: 1,
    pointsReward: 9,
    selected: false
  }
])

const recommendProducts = ref<Product[]>([
  {
    id: 4,
    name: '数据线',
    image: 'https://via.placeholder.com/150x150/722ed1/ffffff?text=数据线',
    price: 29.9
  },
  {
    id: 5,
    name: '手机壳',
    image: 'https://via.placeholder.com/150x150/fa8c16/ffffff?text=手机壳',
    price: 39.9
  },
  {
    id: 6,
    name: '屏幕保护膜',
    image: 'https://via.placeholder.com/150x150/13c2c2/ffffff?text=保护膜',
    price: 19.9
  }
])

// 计算属性
const selectedItems = computed(() => {
  return cartItems.value.filter(item => item.selected)
})

const selectedCount = computed(() => {
  return selectedItems.value.length
})

const isAllSelected = computed(() => {
  return cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
})

const totalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    return total + item.price * item.quantity
  }, 0)
})

const totalPoints = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    return total + item.pointsReward * item.quantity
  }, 0)
})

// 方法
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value
}

const toggleItemSelect = (item: CartItem) => {
  item.selected = !item.selected
}

const toggleSelectAll = () => {
  const newSelectState = !isAllSelected.value
  cartItems.value.forEach(item => {
    item.selected = newSelectState
  })
}

const increaseQuantity = (item: CartItem) => {
  item.quantity++
}

const decreaseQuantity = (item: CartItem) => {
  if (item.quantity > 1) {
    item.quantity--
  }
}

const removeItem = (item: CartItem) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个商品吗？',
    success: (res) => {
      if (res.confirm) {
        const index = cartItems.value.findIndex(cartItem => cartItem.id === item.id)
        if (index > -1) {
          cartItems.value.splice(index, 1)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    }
  })
}

const batchDelete = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请选择要删除的商品',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认删除',
    content: `确定要删除选中的${selectedCount.value}个商品吗？`,
    success: (res) => {
      if (res.confirm) {
        cartItems.value = cartItems.value.filter(item => !item.selected)
        isEditMode.value = false
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

const addToFavorite = (item: CartItem) => {
  uni.showToast({
    title: '已添加到收藏',
    icon: 'success'
  })
}

const goToProductDetail = (item: CartItem | Product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${item.id}`
  })
}

const goToHome = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  })
}

const goToCheckout = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请选择要结算的商品',
      icon: 'none'
    })
    return
  }
  
  const selectedIds = selectedItems.value.map(item => item.id).join(',')
  uni.navigateTo({
    url: `/pages/order/confirm?cartIds=${selectedIds}`
  })
}
</script>

<style lang="scss" scoped>
.cart-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

  .header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }

  .edit-btn {
    font-size: 28rpx;
    color: #ff6b35;
  }
}

.cart-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.cart-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;

  .item-checkbox {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid #d9d9d9;
    border-radius: 50%;
    margin-right: 20rpx;
    margin-top: 10rpx;

    &.checked {
      background-color: #ff6b35;
      border-color: #ff6b35;
    }

    .checkbox-icon {
      font-size: 20rpx;
      color: #ffffff;
      font-weight: 600;

      &.checked {
        color: #ffffff;
      }
    }
  }

  .item-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 12rpx;
    margin-right: 20rpx;
  }

  .item-info {
    flex: 1;

    .item-name {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      line-height: 1.4;
      margin-bottom: 8rpx;
    }

    .item-spec {
      display: block;
      font-size: 24rpx;
      color: #999999;
      margin-bottom: 12rpx;
    }

    .item-price-section {
      margin-bottom: 20rpx;

      .price-info {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .current-price {
          font-size: 32rpx;
          font-weight: 600;
          color: #ff6b35;
          margin-right: 12rpx;
        }

        .original-price {
          font-size: 24rpx;
          color: #999999;
          text-decoration: line-through;
        }
      }

      .points-info {
        .points-text {
          font-size: 22rpx;
          color: #52c41a;
          background-color: #f6ffed;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }
    }

    .item-actions {
      .quantity-control {
        display: flex;
        align-items: center;

        .quantity-btn {
          width: 60rpx;
          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;

          .btn-text {
            font-size: 28rpx;
            color: #666666;
          }
        }

        .quantity-text {
          min-width: 80rpx;
          text-align: center;
          font-size: 28rpx;
          color: #333333;
        }
      }

      .edit-actions {
        display: flex;
        gap: 20rpx;

        .action-btn {
          display: flex;
          align-items: center;
          gap: 8rpx;
          padding: 12rpx 20rpx;
          border-radius: 20rpx;

          &.favorite-btn {
            background-color: #fff7f0;
          }

          &.delete-btn {
            background-color: #fff2f0;
          }

          .action-icon {
            font-size: 20rpx;
          }

          .action-text {
            font-size: 24rpx;
            color: #666666;
          }
        }
      }
    }
  }
}

.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 12rpx;
  }

  .empty-desc {
    font-size: 26rpx;
    color: #999999;
    margin-bottom: 60rpx;
  }

  .empty-action {
    padding: 24rpx 60rpx;
    background-color: #ff6b35;
    border-radius: 30rpx;

    .action-text {
      font-size: 28rpx;
      color: #ffffff;
    }
  }
}

.recommend-section {
  background-color: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;

  .section-header {
    margin-bottom: 20rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .recommend-list {
    display: flex;
    gap: 20rpx;

    .recommend-item {
      flex-shrink: 0;
      width: 200rpx;

      .recommend-image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
        margin-bottom: 12rpx;
      }

      .recommend-name {
        display: block;
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .recommend-price {
        display: block;
        font-size: 26rpx;
        font-weight: 600;
        color: #ff6b35;
      }
    }
  }
}

.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);

  .footer-left {
    display: flex;
    align-items: center;
    flex: 1;

    .select-all {
      display: flex;
      align-items: center;
      margin-right: 40rpx;

      .checkbox-icon {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx solid #d9d9d9;
        border-radius: 50%;
        margin-right: 12rpx;
        font-size: 20rpx;
        color: #ffffff;
        font-weight: 600;

        &.checked {
          background-color: #ff6b35;
          border-color: #ff6b35;
        }
      }

      .select-text {
        font-size: 26rpx;
        color: #333333;
      }
    }

    .total-info {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8rpx;

      .total-label {
        font-size: 26rpx;
        color: #333333;
      }

      .total-price {
        font-size: 32rpx;
        font-weight: 600;
        color: #ff6b35;
      }

      .total-points {
        font-size: 22rpx;
        color: #52c41a;
      }
    }
  }

  .footer-right {
    .action-button {
      padding: 24rpx 40rpx;
      background-color: #ff6b35;
      border-radius: 30rpx;

      &.delete-button {
        background-color: #f5222d;
      }

      .button-text {
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}
</style>
