<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image :src="userInfo.avatar" class="user-avatar" mode="aspectFill" @click="editAvatar" />
        <view class="user-details">
          <text class="username">{{ userInfo.nickname }}</text>
          <text class="user-level">{{ userInfo.level }}</text>
          <view class="user-stats">
            <view class="stat-item" @click="goToPoints">
              <text class="stat-value">{{ userInfo.points }}</text>
              <text class="stat-label">积分</text>
            </view>
            <view class="stat-item" @click="goToCoupons">
              <text class="stat-value">{{ userInfo.coupons }}</text>
              <text class="stat-label">优惠券</text>
            </view>
            <view class="stat-item" @click="goToFavorites">
              <text class="stat-value">{{ userInfo.favorites }}</text>
              <text class="stat-label">收藏</text>
            </view>
          </view>
        </view>
      </view>
      <view class="user-actions">
        <view class="action-btn" @click="editProfile">
          <text class="action-text">编辑资料</text>
        </view>
      </view>
    </view>

    <!-- 订单管理 -->
    <view class="order-section">
      <view class="section-header" @click="goToOrderList">
        <text class="section-title">我的订单</text>
        <text class="more-text">查看全部 ></text>
      </view>
      <view class="order-types">
        <view class="order-type" v-for="type in orderTypes" :key="type.key" @click="goToOrderList(type.key)">
          <view class="type-icon">{{ type.icon }}</view>
          <text class="type-name">{{ type.name }}</text>
          <view class="type-badge" v-if="type.count > 0">
            <text class="badge-text">{{ type.count }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group" v-for="group in menuGroups" :key="group.title">
        <text class="group-title">{{ group.title }}</text>
        <view class="menu-items">
          <view 
            class="menu-item" 
            v-for="item in group.items" 
            :key="item.key"
            @click="handleMenuClick(item)"
          >
            <view class="item-left">
              <text class="item-icon">{{ item.icon }}</text>
              <text class="item-name">{{ item.name }}</text>
            </view>
            <view class="item-right">
              <text class="item-badge" v-if="item.badge">{{ item.badge }}</text>
              <text class="item-arrow">></text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <view class="logout-btn" @click="logout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface UserInfo {
  id: number
  nickname: string
  avatar: string
  level: string
  points: number
  coupons: number
  favorites: number
}

interface OrderType {
  key: string
  name: string
  icon: string
  count: number
}

interface MenuItem {
  key: string
  name: string
  icon: string
  badge?: string
  action: string
}

interface MenuGroup {
  title: string
  items: MenuItem[]
}

// 响应式数据
const userInfo = ref<UserInfo>({
  id: 1,
  nickname: '购物达人',
  avatar: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=头像',
  level: 'VIP会员',
  points: 1580,
  coupons: 3,
  favorites: 12
})

const orderTypes = ref<OrderType[]>([
  { key: 'pending', name: '待付款', icon: '💰', count: 2 },
  { key: 'shipped', name: '待发货', icon: '📦', count: 1 },
  { key: 'delivering', name: '待收货', icon: '🚚', count: 0 },
  { key: 'completed', name: '已完成', icon: '✅', count: 0 },
  { key: 'refund', name: '退款/售后', icon: '🔄', count: 0 }
])

const menuGroups = ref<MenuGroup[]>([
  {
    title: '交易管理',
    items: [
      { key: 'address', name: '收货地址', icon: '📍', action: 'navigate' },
      { key: 'invoice', name: '发票管理', icon: '🧾', action: 'navigate' },
      { key: 'payment', name: '支付方式', icon: '💳', action: 'navigate' }
    ]
  },
  {
    title: '积分福利',
    items: [
      { key: 'points', name: '积分中心', icon: '💎', badge: '1580', action: 'navigate' },
      { key: 'checkin', name: '每日签到', icon: '📅', action: 'action' },
      { key: 'invite', name: '邀请好友', icon: '👥', action: 'navigate' },
      { key: 'vip', name: 'VIP会员', icon: '👑', action: 'navigate' }
    ]
  },
  {
    title: '社区互动',
    items: [
      { key: 'posts', name: '我的帖子', icon: '📝', action: 'navigate' },
      { key: 'comments', name: '我的评论', icon: '💬', action: 'navigate' },
      { key: 'likes', name: '我的点赞', icon: '👍', action: 'navigate' }
    ]
  },
  {
    title: '设置服务',
    items: [
      { key: 'settings', name: '设置', icon: '⚙️', action: 'navigate' },
      { key: 'feedback', name: '意见反馈', icon: '📢', action: 'navigate' },
      { key: 'help', name: '帮助中心', icon: '❓', action: 'navigate' },
      { key: 'about', name: '关于我们', icon: 'ℹ️', action: 'navigate' }
    ]
  }
])

// 方法
const editAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      userInfo.value.avatar = res.tempFilePaths[0]
      uni.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    }
  })
}

const editProfile = () => {
  uni.navigateTo({
    url: '/pages/profile/edit'
  })
}

const goToPoints = () => {
  uni.navigateTo({
    url: '/pages/points/index'
  })
}

const goToCoupons = () => {
  uni.navigateTo({
    url: '/pages/coupon/index'
  })
}

const goToFavorites = () => {
  uni.navigateTo({
    url: '/pages/product/favorites'
  })
}

const goToOrderList = (status?: string) => {
  const url = status ? `/pages/order/list?status=${status}` : '/pages/order/list'
  uni.navigateTo({
    url
  })
}

const handleMenuClick = (item: MenuItem) => {
  switch (item.action) {
    case 'navigate':
      handleNavigate(item.key)
      break
    case 'action':
      handleAction(item.key)
      break
  }
}

const handleNavigate = (key: string) => {
  const routes: Record<string, string> = {
    address: '/pages/address/list',
    invoice: '/pages/profile/invoice',
    payment: '/pages/profile/payment',
    points: '/pages/points/index',
    invite: '/pages/profile/invite',
    vip: '/pages/profile/vip',
    posts: '/pages/forum/my-posts',
    comments: '/pages/forum/my-comments',
    likes: '/pages/forum/my-likes',
    settings: '/pages/profile/settings',
    feedback: '/pages/profile/feedback',
    help: '/pages/profile/help',
    about: '/pages/profile/about'
  }
  
  if (routes[key]) {
    uni.navigateTo({
      url: routes[key]
    })
  }
}

const handleAction = (key: string) => {
  switch (key) {
    case 'checkin':
      handleCheckin()
      break
  }
}

const handleCheckin = () => {
  uni.showToast({
    title: '签到成功，获得10积分',
    icon: 'success'
  })
  userInfo.value.points += 10
}

const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
        // 清除用户信息，跳转到登录页
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/auth/login'
          })
        }, 1500)
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.user-card {
  background: linear-gradient(135deg, #ff6b35, #ff8f5a);
  padding: 60rpx 30rpx 40rpx;
  margin-bottom: 20rpx;

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      margin-right: 30rpx;
    }

    .user-details {
      flex: 1;

      .username {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8rpx;
      }

      .user-level {
        display: block;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        background-color: rgba(255, 255, 255, 0.2);
        padding: 6rpx 16rpx;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        width: fit-content;
      }

      .user-stats {
        display: flex;
        gap: 40rpx;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .stat-value {
            font-size: 32rpx;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4rpx;
          }

          .stat-label {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }

  .user-actions {
    display: flex;
    justify-content: flex-end;

    .action-btn {
      padding: 16rpx 32rpx;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 30rpx;
      border: 1rpx solid rgba(255, 255, 255, 0.3);

      .action-text {
        font-size: 26rpx;
        color: #ffffff;
      }
    }
  }
}

.order-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }

    .more-text {
      font-size: 26rpx;
      color: #999999;
    }
  }

  .order-types {
    display: flex;
    justify-content: space-between;

    .order-type {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .type-icon {
        font-size: 48rpx;
        margin-bottom: 12rpx;
      }

      .type-name {
        font-size: 24rpx;
        color: #333333;
      }

      .type-badge {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        min-width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ff6b35;
        border-radius: 50%;

        .badge-text {
          font-size: 20rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }
  }
}

.menu-section {
  .menu-group {
    background-color: #ffffff;
    margin-bottom: 20rpx;

    .group-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      padding: 30rpx 30rpx 20rpx;
    }

    .menu-items {
      .menu-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: #f8f8f8;
        }

        .item-left {
          display: flex;
          align-items: center;

          .item-icon {
            font-size: 32rpx;
            margin-right: 20rpx;
          }

          .item-name {
            font-size: 28rpx;
            color: #333333;
          }
        }

        .item-right {
          display: flex;
          align-items: center;

          .item-badge {
            font-size: 24rpx;
            color: #ff6b35;
            background-color: #fff7f0;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            margin-right: 12rpx;
          }

          .item-arrow {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

.logout-section {
  padding: 0 30rpx;

  .logout-btn {
    width: 100%;
    padding: 32rpx 0;
    background-color: #ffffff;
    border-radius: 16rpx;
    text-align: center;
    border: 1rpx solid #f0f0f0;

    &:active {
      background-color: #f8f8f8;
    }

    .logout-text {
      font-size: 28rpx;
      color: #f5222d;
    }
  }
}
</style>
