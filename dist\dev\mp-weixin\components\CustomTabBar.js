"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "CustomTabBar",
  setup(__props) {
    const currentIndex = common_vendor.ref(0);
    const tabList = [
      {
        pagePath: "/pages/home/<USER>",
        text: "首页",
        icon: "🏠"
      },
      {
        pagePath: "/pages/category/index",
        text: "分类",
        icon: "📂"
      },
      {
        pagePath: "/pages/cart/index",
        text: "购物车",
        icon: "🛒"
      },
      {
        pagePath: "/pages/profile/index",
        text: "我的",
        icon: "👤"
      }
    ];
    const switchTab = (item, index) => {
      if (currentIndex.value === index)
        return;
      currentIndex.value = index;
      common_vendor.index.switchTab({
        url: item.pagePath
      });
    };
    const getCurrentPageIndex = () => {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const currentRoute = "/" + currentPage.route;
        const index = tabList.findIndex((item) => item.pagePath === currentRoute);
        if (index !== -1) {
          currentIndex.value = index;
        }
      }
    };
    common_vendor.onMounted(() => {
      getCurrentPageIndex();
    });
    common_vendor.index.$on("tabbar-update", (index) => {
      currentIndex.value = index;
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(tabList, (item, index, i0) => {
          return {
            a: common_vendor.t(item.icon),
            b: common_vendor.t(item.text),
            c: index,
            d: currentIndex.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(item, index), index)
          };
        })
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-55a48eff"]]);
wx.createComponent(Component);
