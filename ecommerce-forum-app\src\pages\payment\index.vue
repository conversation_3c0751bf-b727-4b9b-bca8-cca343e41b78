<template>
  <view class="payment-container">
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="order-header">
        <text class="order-title">订单信息</text>
        <text class="order-number">订单号：{{ orderNumber }}</text>
      </view>
      
      <view class="amount-section">
        <text class="amount-label">支付金额</text>
        <text class="amount-value">¥{{ paymentAmount.toFixed(2) }}</text>
      </view>
      
      <view class="order-details">
        <view class="detail-item">
          <text class="detail-label">商品金额</text>
          <text class="detail-value">¥{{ orderData.productTotal.toFixed(2) }}</text>
        </view>
        <view class="detail-item" v-if="orderData.deliveryFee > 0">
          <text class="detail-label">运费</text>
          <text class="detail-value">¥{{ orderData.deliveryFee.toFixed(2) }}</text>
        </view>
        <view class="detail-item" v-if="orderData.couponDiscount > 0">
          <text class="detail-label">优惠券</text>
          <text class="detail-value discount">-¥{{ orderData.couponDiscount.toFixed(2) }}</text>
        </view>
        <view class="detail-item" v-if="orderData.pointsDiscount > 0">
          <text class="detail-label">积分抵扣</text>
          <text class="detail-value discount">-¥{{ orderData.pointsDiscount.toFixed(2) }}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-methods">
      <view class="methods-header">
        <text class="methods-title">选择支付方式</text>
      </view>
      
      <view class="methods-list">
        <view 
          class="method-item"
          :class="{ selected: selectedMethod === method.key }"
          v-for="method in paymentMethods"
          :key="method.key"
          @click="selectMethod(method)"
        >
          <view class="method-left">
            <image :src="method.icon" class="method-icon" mode="aspectFit" />
            <view class="method-info">
              <text class="method-name">{{ method.name }}</text>
              <text class="method-desc" v-if="method.desc">{{ method.desc }}</text>
            </view>
          </view>
          <view class="method-right">
            <view class="method-badge" v-if="method.badge">
              <text class="badge-text">{{ method.badge }}</text>
            </view>
            <view class="radio-icon" :class="{ checked: selectedMethod === method.key }">
              <text class="radio-dot" v-if="selectedMethod === method.key">●</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分支付 -->
    <view class="points-payment" v-if="canUsePoints">
      <view class="points-header">
        <text class="points-title">积分支付</text>
        <view class="points-toggle">
          <switch :checked="usePointsPay" @change="togglePointsPay" color="#ff6b35" />
        </view>
      </view>
      <view class="points-info" v-if="usePointsPay">
        <text class="points-text">使用{{ pointsForPayment }}积分支付¥{{ pointsPayAmount.toFixed(2) }}</text>
        <text class="points-balance">可用积分：{{ userPoints }}</text>
      </view>
    </view>

    <!-- 支付密码 -->
    <view class="password-section" v-if="needPassword">
      <view class="password-header">
        <text class="password-title">输入支付密码</text>
      </view>
      <view class="password-input">
        <input 
          type="password" 
          v-model="paymentPassword" 
          placeholder="请输入6位支付密码"
          maxlength="6"
          class="password-field"
        />
      </view>
    </view>

    <!-- 支付协议 -->
    <view class="agreement-section">
      <view class="agreement-checkbox" @click="toggleAgreement">
        <text class="checkbox-icon" :class="{ checked: agreedToTerms }">{{ agreedToTerms ? '✓' : '' }}</text>
        <text class="agreement-text">我已阅读并同意</text>
        <text class="agreement-link" @click.stop="viewAgreement">《支付服务协议》</text>
      </view>
    </view>

    <!-- 底部支付按钮 -->
    <view class="payment-footer">
      <view class="footer-info">
        <text class="payment-text">支付金额</text>
        <text class="payment-amount">¥{{ finalPayAmount.toFixed(2) }}</text>
      </view>
      <view class="payment-btn" :class="{ disabled: !canPay }" @click="processPay">
        <text class="btn-text">{{ payButtonText }}</text>
      </view>
    </view>

    <!-- 支付中遮罩 -->
    <view class="payment-loading" v-if="isProcessing">
      <view class="loading-content">
        <view class="loading-icon">💳</view>
        <text class="loading-text">支付处理中...</text>
        <text class="loading-desc">请勿关闭页面</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface PaymentMethod {
  key: string
  name: string
  desc?: string
  icon: string
  badge?: string
  available: boolean
}

interface OrderData {
  orderNumber: string
  productTotal: number
  deliveryFee: number
  couponDiscount: number
  pointsDiscount: number
  total: number
}

// 响应式数据
const selectedMethod = ref('wechat')
const usePointsPay = ref(false)
const paymentPassword = ref('')
const agreedToTerms = ref(false)
const isProcessing = ref(false)
const userPoints = ref(1580)

const orderData = ref<OrderData>({
  orderNumber: '',
  productTotal: 699.8,
  deliveryFee: 0,
  couponDiscount: 50,
  pointsDiscount: 15.8,
  total: 634
})

const paymentMethods = ref<PaymentMethod[]>([
  {
    key: 'wechat',
    name: '微信支付',
    desc: '推荐使用微信支付',
    icon: 'https://via.placeholder.com/60x60/07c160/ffffff?text=微信',
    badge: '推荐',
    available: true
  },
  {
    key: 'alipay',
    name: '支付宝',
    desc: '支持花呗分期付款',
    icon: 'https://via.placeholder.com/60x60/1677ff/ffffff?text=支付宝',
    available: true
  },
  {
    key: 'unionpay',
    name: '银联支付',
    desc: '银行卡快捷支付',
    icon: 'https://via.placeholder.com/60x60/e60012/ffffff?text=银联',
    available: true
  },
  {
    key: 'balance',
    name: '余额支付',
    desc: '当前余额：¥1,234.56',
    icon: 'https://via.placeholder.com/60x60/ff6b35/ffffff?text=余额',
    available: true
  }
])

// 计算属性
const orderNumber = computed(() => {
  return orderData.value.orderNumber || `ORD${Date.now()}`
})

const paymentAmount = computed(() => {
  return orderData.value.total
})

const canUsePoints = computed(() => {
  return userPoints.value >= 100 // 至少100积分才能使用积分支付
})

const pointsForPayment = computed(() => {
  if (!usePointsPay.value) return 0
  // 每100积分可支付1元，最多支付订单金额的30%
  const maxPoints = Math.floor(paymentAmount.value * 0.3 * 100)
  return Math.min(userPoints.value, maxPoints)
})

const pointsPayAmount = computed(() => {
  return pointsForPayment.value / 100
})

const finalPayAmount = computed(() => {
  let amount = paymentAmount.value
  if (usePointsPay.value) {
    amount -= pointsPayAmount.value
  }
  return Math.max(0, amount)
})

const needPassword = computed(() => {
  return ['balance', 'unionpay'].includes(selectedMethod.value)
})

const canPay = computed(() => {
  if (!agreedToTerms.value) return false
  if (needPassword.value && paymentPassword.value.length !== 6) return false
  if (finalPayAmount.value <= 0 && !usePointsPay.value) return false
  return true
})

const payButtonText = computed(() => {
  if (isProcessing.value) return '处理中...'
  if (!agreedToTerms.value) return '请同意支付协议'
  if (needPassword.value && paymentPassword.value.length !== 6) return '请输入支付密码'
  
  const method = paymentMethods.value.find(m => m.key === selectedMethod.value)
  const methodName = method ? method.name : '支付'
  
  if (finalPayAmount.value <= 0) {
    return usePointsPay.value ? '确认积分支付' : '确认支付'
  }
  
  return `${methodName} ¥${finalPayAmount.value.toFixed(2)}`
})

// 方法
const selectMethod = (method: PaymentMethod) => {
  if (method.available) {
    selectedMethod.value = method.key
  }
}

const togglePointsPay = (e: any) => {
  usePointsPay.value = e.detail.value
}

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value
}

const viewAgreement = () => {
  uni.navigateTo({
    url: '/pages/agreement/payment'
  })
}

const processPay = async () => {
  if (!canPay.value) return
  
  isProcessing.value = true
  
  try {
    // 模拟支付处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 根据支付方式调用不同的支付接口
    switch (selectedMethod.value) {
      case 'wechat':
        await processWechatPay()
        break
      case 'alipay':
        await processAlipay()
        break
      case 'unionpay':
        await processUnionPay()
        break
      case 'balance':
        await processBalancePay()
        break
    }
    
    // 支付成功
    uni.showToast({
      title: '支付成功',
      icon: 'success'
    })
    
    setTimeout(() => {
      uni.redirectTo({
        url: `/pages/payment/result?status=success&orderNumber=${orderNumber.value}`
      })
    }, 1500)
    
  } catch (error) {
    uni.showToast({
      title: '支付失败',
      icon: 'error'
    })
    
    setTimeout(() => {
      uni.redirectTo({
        url: `/pages/payment/result?status=failed&orderNumber=${orderNumber.value}`
      })
    }, 1500)
  } finally {
    isProcessing.value = false
  }
}

const processWechatPay = async () => {
  // 微信支付逻辑
  console.log('Processing WeChat Pay...')
}

const processAlipay = async () => {
  // 支付宝支付逻辑
  console.log('Processing Alipay...')
}

const processUnionPay = async () => {
  // 银联支付逻辑
  console.log('Processing UnionPay...')
}

const processBalancePay = async () => {
  // 余额支付逻辑
  console.log('Processing Balance Pay...')
}

onLoad((options) => {
  if (options?.orderData) {
    try {
      const data = JSON.parse(decodeURIComponent(options.orderData))
      orderData.value = {
        orderNumber: `ORD${Date.now()}`,
        productTotal: data.productTotal || 0,
        deliveryFee: data.deliveryFee || 0,
        couponDiscount: data.couponDiscount || 0,
        pointsDiscount: data.pointsDiscount || 0,
        total: data.total || 0
      }
    } catch (error) {
      console.error('Failed to parse order data:', error)
    }
  }
})
</script>

<style lang="scss" scoped>
.payment-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.order-info {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .order-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }

    .order-number {
      font-size: 24rpx;
      color: #999999;
    }
  }

  .amount-section {
    text-align: center;
    margin-bottom: 30rpx;

    .amount-label {
      display: block;
      font-size: 26rpx;
      color: #666666;
      margin-bottom: 12rpx;
    }

    .amount-value {
      font-size: 64rpx;
      font-weight: 700;
      color: #ff6b35;
    }
  }

  .order-details {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .detail-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-label {
        font-size: 24rpx;
        color: #666666;
      }

      .detail-value {
        font-size: 24rpx;
        color: #333333;

        &.discount {
          color: #52c41a;
        }
      }
    }
  }
}

.payment-methods {
  background-color: #ffffff;
  margin-bottom: 20rpx;

  .methods-header {
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .methods-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .methods-list {
    .method-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 1rpx solid #f8f8f8;

      &:last-child {
        border-bottom: none;
      }

      &.selected {
        background-color: #fff7f0;
      }

      .method-left {
        display: flex;
        align-items: center;
        flex: 1;

        .method-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 8rpx;
          margin-right: 20rpx;
        }

        .method-info {
          .method-name {
            display: block;
            font-size: 28rpx;
            color: #333333;
            margin-bottom: 6rpx;
          }

          .method-desc {
            font-size: 22rpx;
            color: #999999;
          }
        }
      }

      .method-right {
        display: flex;
        align-items: center;

        .method-badge {
          margin-right: 16rpx;

          .badge-text {
            font-size: 20rpx;
            color: #ff6b35;
            background-color: #fff7f0;
            padding: 4rpx 8rpx;
            border-radius: 8rpx;
          }
        }

        .radio-icon {
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2rpx solid #d9d9d9;
          border-radius: 50%;

          &.checked {
            border-color: #ff6b35;
            background-color: #ff6b35;
          }

          .radio-dot {
            font-size: 20rpx;
            color: #ffffff;
          }
        }
      }
    }
  }
}

.points-payment {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .points-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .points-title {
      font-size: 28rpx;
      color: #333333;
    }
  }

  .points-info {
    .points-text {
      display: block;
      font-size: 24rpx;
      color: #52c41a;
      margin-bottom: 8rpx;
    }

    .points-balance {
      font-size: 22rpx;
      color: #999999;
    }
  }
}

.password-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .password-header {
    margin-bottom: 20rpx;

    .password-title {
      font-size: 28rpx;
      color: #333333;
    }
  }

  .password-input {
    .password-field {
      width: 100%;
      height: 80rpx;
      font-size: 32rpx;
      color: #333333;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      text-align: center;
      letter-spacing: 8rpx;
    }
  }
}

.agreement-section {
  padding: 30rpx;

  .agreement-checkbox {
    display: flex;
    align-items: center;

    .checkbox-icon {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid #d9d9d9;
      border-radius: 4rpx;
      margin-right: 12rpx;
      font-size: 18rpx;
      color: #ffffff;

      &.checked {
        background-color: #ff6b35;
        border-color: #ff6b35;
      }
    }

    .agreement-text {
      font-size: 24rpx;
      color: #666666;
      margin-right: 8rpx;
    }

    .agreement-link {
      font-size: 24rpx;
      color: #ff6b35;
      text-decoration: underline;
    }
  }
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);

  .footer-info {
    flex: 1;
    margin-right: 30rpx;

    .payment-text {
      display: block;
      font-size: 24rpx;
      color: #666666;
      margin-bottom: 4rpx;
    }

    .payment-amount {
      font-size: 32rpx;
      font-weight: 700;
      color: #ff6b35;
    }
  }

  .payment-btn {
    padding: 24rpx 40rpx;
    background-color: #ff6b35;
    border-radius: 30rpx;
    min-width: 200rpx;
    text-align: center;

    &.disabled {
      background-color: #d9d9d9;
    }

    .btn-text {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

.payment-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #ffffff;
    padding: 60rpx 40rpx;
    border-radius: 20rpx;

    .loading-icon {
      font-size: 80rpx;
      margin-bottom: 20rpx;
      animation: pulse 1.5s ease-in-out infinite;
    }

    .loading-text {
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 12rpx;
    }

    .loading-desc {
      font-size: 22rpx;
      color: #999999;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
