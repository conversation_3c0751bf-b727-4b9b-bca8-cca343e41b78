<template>
  <view class="home-container">
    <!-- 顶部搜索栏 -->
    <view class="header-section">
      <view class="search-bar" @click="goToSearch">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索商品、帖子</text>
      </view>
      <view class="points-info" @click="goToPoints">
        <text class="points-icon">💎</text>
        <text class="points-text">{{ userPoints }}</text>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section">
      <swiper 
        class="banner-swiper" 
        indicator-dots 
        circular 
        autoplay 
        interval="4000"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ff6b35"
      >
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <image 
            :src="banner.image" 
            class="banner-image" 
            mode="aspectFill"
            @click="handleBannerClick(banner)"
          />
          <view class="banner-overlay">
            <text class="banner-title">{{ banner.title }}</text>
            <text class="banner-desc">{{ banner.desc }}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 功能导航 -->
    <view class="nav-section">
      <view class="nav-grid">
        <view 
          class="nav-item" 
          v-for="(nav, index) in navItems" 
          :key="index"
          @click="handleNavClick(nav)"
        >
          <view class="nav-icon">{{ nav.icon }}</view>
          <text class="nav-name">{{ nav.name }}</text>
        </view>
      </view>
    </view>

    <!-- 积分商城入口 -->
    <view class="points-mall-section">
      <view class="section-header" @click="goToPointsMall">
        <view class="section-title">
          <text class="title-icon">💎</text>
          <text class="title-text">积分商城</text>
        </view>
        <text class="more-text">更多好礼 ></text>
      </view>
      <scroll-view class="points-products" scroll-x>
        <view class="points-product-list">
          <view 
            class="points-product-item" 
            v-for="(product, index) in pointsProducts" 
            :key="index"
            @click="goToProductDetail(product, 'points')"
          >
            <image :src="product.image" class="product-image" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <view class="product-points">
                <text class="points-price">{{ product.points }}积分</text>
                <text class="original-price">¥{{ product.price }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 热门商品 -->
    <view class="hot-products-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">🔥</text>
          <text class="title-text">热门商品</text>
        </view>
        <text class="more-text" @click="goToProductList">更多商品 ></text>
      </view>
      <view class="products-grid">
        <view 
          class="product-item" 
          v-for="(product, index) in hotProducts" 
          :key="index"
          @click="goToProductDetail(product)"
        >
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <view class="product-price">
              <text class="current-price">¥{{ product.price }}</text>
              <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-meta">
              <text class="sales-count">已售{{ product.sales }}件</text>
              <text class="points-reward">+{{ product.pointsReward }}积分</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 论坛热帖 -->
    <view class="forum-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">💬</text>
          <text class="title-text">论坛热帖</text>
        </view>
        <text class="more-text" @click="goToForum">进入论坛 ></text>
      </view>
      <view class="forum-posts">
        <view 
          class="forum-post-item" 
          v-for="(post, index) in forumPosts" 
          :key="index"
          @click="goToForumDetail(post)"
        >
          <view class="post-header">
            <image :src="post.avatar" class="user-avatar" mode="aspectFill" />
            <view class="user-info">
              <text class="username">{{ post.username }}</text>
              <text class="post-time">{{ formatTime(post.createTime) }}</text>
            </view>
            <view class="post-points" v-if="post.pointsRequired">
              <text class="points-required">{{ post.pointsRequired }}积分查看</text>
            </view>
          </view>
          <text class="post-title">{{ post.title }}</text>
          <text class="post-content">{{ post.content }}</text>
          <view class="post-stats">
            <text class="stat-item">👍 {{ post.likes }}</text>
            <text class="stat-item">💬 {{ post.comments }}</text>
            <text class="stat-item">👀 {{ post.views }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onPullDownRefresh } from 'vue'

// 接口定义
interface Banner {
  id: number
  title: string
  desc: string
  image: string
  link?: string
  type?: string
}

interface NavItem {
  id: number
  name: string
  icon: string
  link: string
  type: string
}

interface Product {
  id: number
  name: string
  image: string
  price: number
  originalPrice?: number
  points?: number
  pointsReward: number
  sales: number
}

interface ForumPost {
  id: number
  title: string
  content: string
  username: string
  avatar: string
  createTime: string
  likes: number
  comments: number
  views: number
  pointsRequired?: number
}

// 响应式数据
const userPoints = ref(1580)

const banners = ref<Banner[]>([
  {
    id: 1,
    title: '积分商城大促销',
    desc: '购物送积分，积分换好礼',
    image: 'https://via.placeholder.com/750x300/ff6b35/ffffff?text=积分商城'
  },
  {
    id: 2,
    title: '论坛分享赢积分',
    desc: '发帖分享，获得积分奖励',
    image: 'https://via.placeholder.com/750x300/1890ff/ffffff?text=论坛分享'
  },
  {
    id: 3,
    title: '新用户专享',
    desc: '注册即送500积分',
    image: 'https://via.placeholder.com/750x300/52c41a/ffffff?text=新用户福利'
  }
])

const navItems = ref<NavItem[]>([
  { id: 1, name: '积分商城', icon: '💎', link: '/pages/points/mall', type: 'navigate' },
  { id: 2, name: '优惠券', icon: '🎫', link: '/pages/coupon/index', type: 'navigate' },
  { id: 3, name: '签到', icon: '📅', link: '', type: 'action' },
  { id: 4, name: '客服', icon: '💬', link: '', type: 'action' },
  { id: 5, name: '分类', icon: '📂', link: '/pages/category/index', type: 'tab' },
  { id: 6, name: '论坛', icon: '🗣️', link: '/pages/forum/index', type: 'tab' },
  { id: 7, name: '订单', icon: '📋', link: '/pages/order/list', type: 'navigate' },
  { id: 8, name: '地址', icon: '📍', link: '/pages/address/list', type: 'navigate' }
])

const pointsProducts = ref<Product[]>([
  {
    id: 1,
    name: '精美水杯',
    image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=水杯',
    price: 29.9,
    points: 500,
    pointsReward: 0,
    sales: 128
  },
  {
    id: 2,
    name: '蓝牙耳机',
    image: 'https://via.placeholder.com/200x200/1890ff/ffffff?text=耳机',
    price: 199.9,
    points: 1200,
    pointsReward: 0,
    sales: 89
  },
  {
    id: 3,
    name: '手机支架',
    image: 'https://via.placeholder.com/200x200/52c41a/ffffff?text=支架',
    price: 19.9,
    points: 300,
    pointsReward: 0,
    sales: 256
  }
])

const hotProducts = ref<Product[]>([
  {
    id: 1,
    name: '智能手表',
    image: 'https://via.placeholder.com/300x300/ff6b35/ffffff?text=手表',
    price: 299.9,
    originalPrice: 399.9,
    pointsReward: 30,
    sales: 1234
  },
  {
    id: 2,
    name: '无线充电器',
    image: 'https://via.placeholder.com/300x300/1890ff/ffffff?text=充电器',
    price: 89.9,
    originalPrice: 129.9,
    pointsReward: 9,
    sales: 567
  },
  {
    id: 3,
    name: '蓝牙音箱',
    image: 'https://via.placeholder.com/300x300/52c41a/ffffff?text=音箱',
    price: 159.9,
    pointsReward: 16,
    sales: 890
  },
  {
    id: 4,
    name: '数据线',
    image: 'https://via.placeholder.com/300x300/722ed1/ffffff?text=数据线',
    price: 29.9,
    originalPrice: 49.9,
    pointsReward: 3,
    sales: 2345
  }
])

const forumPosts = ref<ForumPost[]>([
  {
    id: 1,
    title: '分享我的购物心得',
    content: '最近在这里买了很多好东西，质量都很不错，积分系统也很给力...',
    username: '购物达人',
    avatar: 'https://via.placeholder.com/80x80/ff6b35/ffffff?text=头像',
    createTime: '2024-01-03 10:30:00',
    likes: 128,
    comments: 45,
    views: 1256,
    pointsRequired: 0
  },
  {
    id: 2,
    title: '积分攻略大全',
    content: '教大家如何快速获得积分，以及积分的最佳使用方法...',
    username: '积分专家',
    avatar: 'https://via.placeholder.com/80x80/1890ff/ffffff?text=专家',
    createTime: '2024-01-03 09:15:00',
    likes: 89,
    comments: 23,
    views: 567,
    pointsRequired: 50
  },
  {
    id: 3,
    title: '新品体验报告',
    content: '最新上架的智能手表使用体验，功能强大，性价比很高...',
    username: '科技爱好者',
    avatar: 'https://via.placeholder.com/80x80/52c41a/ffffff?text=科技',
    createTime: '2024-01-02 20:45:00',
    likes: 156,
    comments: 67,
    views: 890,
    pointsRequired: 0
  }
])

// 方法定义
const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index'
  })
}

const goToPoints = () => {
  uni.navigateTo({
    url: '/pages/points/index'
  })
}

const goToPointsMall = () => {
  uni.navigateTo({
    url: '/pages/points/mall'
  })
}

const goToProductList = () => {
  uni.navigateTo({
    url: '/pages/product/list'
  })
}

const goToForum = () => {
  uni.switchTab({
    url: '/pages/forum/index'
  })
}

const handleBannerClick = (banner: Banner) => {
  if (banner.link) {
    uni.navigateTo({
      url: banner.link
    })
  }
}

const handleNavClick = (nav: NavItem) => {
  if (nav.type === 'navigate' && nav.link) {
    uni.navigateTo({
      url: nav.link
    })
  } else if (nav.type === 'tab' && nav.link) {
    uni.switchTab({
      url: nav.link
    })
  } else if (nav.type === 'action') {
    // 处理特殊操作
    if (nav.name === '签到') {
      handleSignIn()
    } else if (nav.name === '客服') {
      handleCustomerService()
    }
  }
}

const goToProductDetail = (product: Product, type?: string) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}&type=${type || 'normal'}`
  })
}

const goToForumDetail = (post: ForumPost) => {
  uni.navigateTo({
    url: `/pages/forum/detail?id=${post.id}`
  })
}

const handleSignIn = () => {
  uni.showToast({
    title: '签到成功，获得10积分',
    icon: 'success'
  })
  userPoints.value += 10
}

const handleCustomerService = () => {
  uni.showModal({
    title: '客服服务',
    content: '请选择联系方式',
    showCancel: true,
    cancelText: '在线客服',
    confirmText: '电话客服',
    success: (res) => {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: '************'
        })
      } else if (res.cancel) {
        uni.showToast({
          title: '正在连接客服...',
          icon: 'loading'
        })
      }
    }
  })
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return time.toLocaleDateString()
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
  loadUserPoints()
})

onPullDownRefresh(() => {
  // 刷新数据
  setTimeout(() => {
    uni.stopPullDownRefresh()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1000)
})

const loadUserPoints = () => {
  // 模拟加载用户积分
  // 实际项目中这里会调用API
}
</script>

<style lang="scss" scoped>
.home-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部搜索栏 */
.header-section {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

  .search-bar {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: #f5f5f5;
    border-radius: 50rpx;
    margin-right: 20rpx;

    .search-icon {
      font-size: 28rpx;
      margin-right: 12rpx;
      color: #999999;
    }

    .search-placeholder {
      font-size: 28rpx;
      color: #999999;
    }
  }

  .points-info {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    background: linear-gradient(135deg, #ff6b35, #ff8f5a);
    border-radius: 30rpx;

    .points-icon {
      font-size: 24rpx;
      margin-right: 8rpx;
    }

    .points-text {
      font-size: 26rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

/* 轮播图 */
.banner-section {
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .banner-swiper {
    height: 300rpx;
    border-radius: 16rpx;

    .banner-image {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }

    .banner-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 30rpx;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));

      .banner-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8rpx;
      }

      .banner-desc {
        display: block;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

/* 功能导航 */
.nav-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;

  .nav-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30rpx;

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .nav-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        background-color: #f8f9fa;
        border-radius: 20rpx;
        margin-bottom: 12rpx;
      }

      .nav-name {
        font-size: 24rpx;
        color: #333333;
      }
    }
  }
}

/* 区块标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;

  .section-title {
    display: flex;
    align-items: center;

    .title-icon {
      font-size: 32rpx;
      margin-right: 12rpx;
    }

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .more-text {
    font-size: 26rpx;
    color: #999999;
  }
}

/* 积分商城 */
.points-mall-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;

  .points-products {
    padding: 0 30rpx 30rpx;

    .points-product-list {
      display: flex;
      gap: 20rpx;

      .points-product-item {
        flex-shrink: 0;
        width: 200rpx;

        .product-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 12rpx;
          margin-bottom: 12rpx;
        }

        .product-info {
          .product-name {
            display: block;
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .product-points {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .points-price {
              font-size: 28rpx;
              font-weight: 600;
              color: #ff6b35;
            }

            .original-price {
              font-size: 22rpx;
              color: #999999;
              text-decoration: line-through;
            }
          }
        }
      }
    }
  }
}

/* 热门商品 */
.hot-products-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;

  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    padding: 0 30rpx 30rpx;

    .product-item {
      .product-image {
        width: 100%;
        height: 300rpx;
        border-radius: 12rpx;
        margin-bottom: 12rpx;
      }

      .product-info {
        .product-name {
          display: block;
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-price {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .current-price {
            font-size: 32rpx;
            font-weight: 600;
            color: #ff6b35;
            margin-right: 12rpx;
          }

          .original-price {
            font-size: 24rpx;
            color: #999999;
            text-decoration: line-through;
          }
        }

        .product-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .sales-count {
            font-size: 22rpx;
            color: #999999;
          }

          .points-reward {
            font-size: 22rpx;
            color: #52c41a;
            background-color: #f6ffed;
            padding: 4rpx 8rpx;
            border-radius: 8rpx;
          }
        }
      }
    }
  }
}

/* 论坛热帖 */
.forum-section {
  margin: 20rpx 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;

  .forum-posts {
    padding: 0 30rpx 30rpx;

    .forum-post-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .post-header {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .user-avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }

        .user-info {
          flex: 1;

          .username {
            display: block;
            font-size: 26rpx;
            color: #333333;
            font-weight: 500;
            margin-bottom: 4rpx;
          }

          .post-time {
            display: block;
            font-size: 22rpx;
            color: #999999;
          }
        }

        .post-points {
          .points-required {
            font-size: 22rpx;
            color: #ff6b35;
            background-color: #fff7f0;
            padding: 6rpx 12rpx;
            border-radius: 12rpx;
          }
        }
      }

      .post-title {
        display: block;
        font-size: 30rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .post-content {
        display: block;
        font-size: 26rpx;
        color: #666666;
        line-height: 1.5;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .post-stats {
        display: flex;
        align-items: center;
        gap: 30rpx;

        .stat-item {
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
}
</style>
