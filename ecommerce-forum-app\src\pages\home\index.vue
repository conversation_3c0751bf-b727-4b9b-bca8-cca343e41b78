<template>
  <view class="home-container">
    <!-- 顶部搜索栏 -->
    <view class="header-section">
      <view class="search-bar" @click="goToSearch">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索商品、帖子</text>
      </view>
      <view class="points-info" @click="goToPoints">
        <text class="points-icon">💎</text>
        <text class="points-text">{{ userPoints }}</text>
      </view>
    </view>

    <!-- 轮播图 -->
    <view class="banner-section">
      <swiper 
        class="banner-swiper" 
        indicator-dots 
        circular 
        autoplay 
        interval="4000"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ff6b35"
      >
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <image 
            :src="banner.image" 
            class="banner-image" 
            mode="aspectFill"
            @click="handleBannerClick(banner)"
          />
          <view class="banner-overlay">
            <text class="banner-title">{{ banner.title }}</text>
            <text class="banner-desc">{{ banner.desc }}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 功能导航 -->
    <view class="nav-section">
      <view class="nav-grid">
        <view 
          class="nav-item" 
          v-for="(nav, index) in navItems" 
          :key="index"
          @click="handleNavClick(nav)"
        >
          <view class="nav-icon">{{ nav.icon }}</view>
          <text class="nav-name">{{ nav.name }}</text>
        </view>
      </view>
    </view>

    <!-- 积分商城入口 -->
    <view class="points-mall-section">
      <view class="section-header" @click="goToPointsMall">
        <view class="section-title">
          <text class="title-icon">💎</text>
          <text class="title-text">积分商城</text>
        </view>
        <text class="more-text">更多好礼 ></text>
      </view>
      <scroll-view class="points-products" scroll-x>
        <view class="points-product-list">
          <view 
            class="points-product-item" 
            v-for="(product, index) in pointsProducts" 
            :key="index"
            @click="goToProductDetail(product, 'points')"
          >
            <image :src="product.image" class="product-image" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <view class="product-points">
                <text class="points-price">{{ product.points }}积分</text>
                <text class="original-price">¥{{ product.price }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 热门商品 -->
    <view class="hot-products-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">🔥</text>
          <text class="title-text">热门商品</text>
        </view>
        <text class="more-text" @click="goToProductList">更多商品 ></text>
      </view>
      <view class="products-grid">
        <view 
          class="product-item" 
          v-for="(product, index) in hotProducts" 
          :key="index"
          @click="goToProductDetail(product)"
        >
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <view class="product-price">
              <text class="current-price">¥{{ product.price }}</text>
              <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-meta">
              <text class="sales-count">已售{{ product.sales }}件</text>
              <text class="points-reward">+{{ product.pointsReward }}积分</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 论坛热帖 -->
    <view class="forum-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">💬</text>
          <text class="title-text">论坛热帖</text>
        </view>
        <text class="more-text" @click="goToForum">进入论坛 ></text>
      </view>
      <view class="forum-posts">
        <view 
          class="forum-post-item" 
          v-for="(post, index) in forumPosts" 
          :key="index"
          @click="goToForumDetail(post)"
        >
          <view class="post-header">
            <image :src="post.avatar" class="user-avatar" mode="aspectFill" />
            <view class="user-info">
              <text class="username">{{ post.username }}</text>
              <text class="post-time">{{ formatTime(post.createTime) }}</text>
            </view>
            <view class="post-points" v-if="post.pointsRequired">
              <text class="points-required">{{ post.pointsRequired }}积分查看</text>
            </view>
          </view>
          <text class="post-title">{{ post.title }}</text>
          <text class="post-content">{{ post.content }}</text>
          <view class="post-stats">
            <text class="stat-item">👍 {{ post.likes }}</text>
            <text class="stat-item">💬 {{ post.comments }}</text>
            <text class="stat-item">👀 {{ post.views }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onPullDownRefresh } from 'vue'

// 接口定义
interface Banner {
  id: number
  title: string
  desc: string
  image: string
  link?: string
  type?: string
}

interface NavItem {
  id: number
  name: string
  icon: string
  link: string
  type: string
}

interface Product {
  id: number
  name: string
  image: string
  price: number
  originalPrice?: number
  points?: number
  pointsReward: number
  sales: number
}

interface ForumPost {
  id: number
  title: string
  content: string
  username: string
  avatar: string
  createTime: string
  likes: number
  comments: number
  views: number
  pointsRequired?: number
}

// 响应式数据
const userPoints = ref(1580)

const banners = ref<Banner[]>([
  {
    id: 1,
    title: '积分商城大促销',
    desc: '购物送积分，积分换好礼',
    image: 'https://via.placeholder.com/750x300/ff6b35/ffffff?text=积分商城'
  },
  {
    id: 2,
    title: '论坛分享赢积分',
    desc: '发帖分享，获得积分奖励',
    image: 'https://via.placeholder.com/750x300/1890ff/ffffff?text=论坛分享'
  },
  {
    id: 3,
    title: '新用户专享',
    desc: '注册即送500积分',
    image: 'https://via.placeholder.com/750x300/52c41a/ffffff?text=新用户福利'
  }
])

const navItems = ref<NavItem[]>([
  { id: 1, name: '积分商城', icon: '💎', link: '/pages/points/mall', type: 'navigate' },
  { id: 2, name: '优惠券', icon: '🎫', link: '/pages/coupon/index', type: 'navigate' },
  { id: 3, name: '签到', icon: '📅', link: '', type: 'action' },
  { id: 4, name: '客服', icon: '💬', link: '', type: 'action' },
  { id: 5, name: '分类', icon: '📂', link: '/pages/category/index', type: 'tab' },
  { id: 6, name: '论坛', icon: '🗣️', link: '/pages/forum/index', type: 'tab' },
  { id: 7, name: '订单', icon: '📋', link: '/pages/order/list', type: 'navigate' },
  { id: 8, name: '地址', icon: '📍', link: '/pages/address/list', type: 'navigate' }
])
</script>
