"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("./common/vendor.js"),t=require("./store/app.js");Math;const n=e.defineComponent({__name:"App",setup(n){const a=t.useAppStore(),o=e.ref(!1);e.onLaunch(()=>{r()}),e.onShow(()=>{}),e.onHide(()=>{});const r=()=>{return t=this,n=null,r=function*(){try{o.value=!0;const t=yield e.index.getSystemInfo();a.setSystemInfo(t),p()}catch(t){}finally{o.value=!1}},new Promise((e,a)=>{var o=e=>{try{s(r.next(e))}catch(t){a(t)}},p=e=>{try{s(r.throw(e))}catch(t){a(t)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(o,p);s((r=r.apply(t,n)).next())});var t,n,r},p=()=>{if(e.index.canIUse("getUpdateManager")){const t=e.index.getUpdateManager();t.onCheckForUpdate(n=>{n.hasUpdate&&(t.onUpdateReady(()=>{e.index.showModal({title:"更新提示",content:"新版本已经准备好，是否重启应用？",success:e=>{e.confirm&&t.applyUpdate()}})}),t.onUpdateFailed(()=>{e.index.showModal({title:"更新失败",content:"新版本下载失败，请检查网络后重试",showCancel:!1})}))})}};return(t,n)=>e.e({a:o.value},(o.value,{}))}});function a(){const t=e.createSSRApp(n),a=e.createPinia();return t.use(a),{app:t,pinia:a}}a().app.mount("#app"),exports.createApp=a;
