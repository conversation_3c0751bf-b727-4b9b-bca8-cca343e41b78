"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveUnionType = exports.inferRuntimeType = exports.recordImports = exports.fileToScope = exports.invalidateTypeCache = exports.resolveTypeElements = exports.TypeScope = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const shared_1 = require("@vue/shared");
const parser_1 = require("@babel/parser");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const utils_1 = require("./utils");
const context_1 = require("./context");
const compiler_sfc_1 = require("@vue/compiler-sfc");
// import { createCache } from '../cache'
const path_1 = require("path");
const process = __importStar(require("process"));
class TypeScope {
    constructor(filename, source, offset = 0, imports = Object.create(null), types = Object.create(null), declares = Object.create(null)) {
        this.filename = filename;
        this.source = source;
        this.offset = offset;
        this.imports = imports;
        this.types = types;
        this.declares = declares;
        this.resolvedImportSources = Object.create(null);
        this.exportedTypes = Object.create(null);
        this.exportedDeclares = Object.create(null);
    }
}
exports.TypeScope = TypeScope;
/**
 * Resolve arbitrary type node to a list of type elements that can be then
 * mapped to runtime props or emits.
 */
function resolveTypeElements(ctx, node, scope, typeParameters) {
    if (node._resolvedElements) {
        return node._resolvedElements;
    }
    return (node._resolvedElements = innerResolveTypeElements(ctx, node, node._ownerScope || scope || ctxToScope(ctx), typeParameters));
}
exports.resolveTypeElements = resolveTypeElements;
function innerResolveTypeElements(ctx, node, scope, typeParameters) {
    switch (node.type) {
        case 'TSTypeLiteral':
            return typeElementsToMap(ctx, node.members, scope, typeParameters);
        case 'TSInterfaceDeclaration':
            return resolveInterfaceMembers(ctx, node, scope, typeParameters);
        case 'TSTypeAliasDeclaration':
        case 'TSParenthesizedType':
            return resolveTypeElements(ctx, node.typeAnnotation, scope, typeParameters);
        case 'TSFunctionType': {
            return { props: {}, calls: [node] };
        }
        case 'TSUnionType':
        case 'TSIntersectionType':
            return mergeElements(node.types.map((t) => resolveTypeElements(ctx, t, scope, typeParameters)), node.type);
        case 'TSMappedType':
            return resolveMappedType(ctx, node, scope);
        case 'TSIndexedAccessType': {
            const types = resolveIndexType(ctx, node, scope);
            return mergeElements(types.map((t) => resolveTypeElements(ctx, t, t._ownerScope)), 'TSUnionType');
        }
        case 'TSExpressionWithTypeArguments': // referenced by interface extends
        case 'TSTypeReference': {
            const typeName = getReferenceName(node);
            if ((typeName === 'ExtractPropTypes' ||
                typeName === 'ExtractPublicPropTypes') &&
                node.typeParameters &&
                scope.imports[typeName]?.source === 'vue') {
                return resolveExtractPropTypes(resolveTypeElements(ctx, node.typeParameters.params[0], scope, typeParameters), scope);
            }
            const resolved = resolveTypeReference(ctx, node, scope);
            if (resolved) {
                const typeParams = Object.create(null);
                if ((resolved.type === 'TSTypeAliasDeclaration' ||
                    resolved.type === 'TSInterfaceDeclaration') &&
                    resolved.typeParameters &&
                    node.typeParameters) {
                    resolved.typeParameters.params.forEach((p, i) => {
                        let param = typeParameters && typeParameters[p.name];
                        if (!param)
                            param = node.typeParameters.params[i];
                        typeParams[p.name] = param;
                    });
                }
                if (resolved.type === 'TSInterfaceDeclaration') {
                    ;
                    ctx.propsInterfaceDecl = resolved;
                }
                return resolveTypeElements(ctx, resolved, resolved._ownerScope, typeParams);
            }
            else {
                if (typeof typeName === 'string') {
                    if (typeParameters && typeParameters[typeName]) {
                        return resolveTypeElements(ctx, typeParameters[typeName], scope, typeParameters);
                    }
                    if (
                    // @ts-expect-error
                    SupportedBuiltinsSet.has(typeName)) {
                        return resolveBuiltin(ctx, node, typeName, scope, typeParameters);
                    }
                    else if (typeName === 'ReturnType' && node.typeParameters) {
                        // limited support, only reference types
                        const ret = resolveReturnType(ctx, node.typeParameters.params[0], scope);
                        if (ret) {
                            return resolveTypeElements(ctx, ret, scope);
                        }
                    }
                }
                return ctx.error(`Unresolvable type reference or unsupported built-in utility type`, node, scope);
            }
        }
        case 'TSImportType': {
            if ((0, utils_1.getId)(node.argument) === 'vue' &&
                node.qualifier?.type === 'Identifier' &&
                node.qualifier.name === 'ExtractPropTypes' &&
                node.typeParameters) {
                return resolveExtractPropTypes(resolveTypeElements(ctx, node.typeParameters.params[0], scope), scope);
            }
            const sourceScope = importSourceToScope(ctx, node.argument, scope, node.argument.value);
            const resolved = resolveTypeReference(ctx, node, sourceScope);
            if (resolved) {
                return resolveTypeElements(ctx, resolved, resolved._ownerScope);
            }
            break;
        }
        case 'TSTypeQuery':
            {
                const resolved = resolveTypeReference(ctx, node, scope);
                if (resolved) {
                    return resolveTypeElements(ctx, resolved, resolved._ownerScope);
                }
            }
            break;
    }
    return ctx.error(`Unresolvable type: ${node.type}`, node, scope);
}
function typeElementsToMap(ctx, elements, scope = ctxToScope(ctx), typeParameters) {
    const res = { props: {} };
    for (const e of elements) {
        if (e.type === 'TSPropertySignature' || e.type === 'TSMethodSignature') {
            // capture generic parameters on node's scope
            if (typeParameters) {
                scope = createChildScope(scope);
                Object.assign(scope.types, typeParameters);
            }
            ;
            e._ownerScope = scope;
            const name = (0, utils_1.getId)(e.key);
            if (name && !e.computed) {
                res.props[name] = e;
            }
            else if (e.key.type === 'TemplateLiteral') {
                for (const key of resolveTemplateKeys(ctx, e.key, scope)) {
                    res.props[key] = e;
                }
            }
            else {
                ctx.error(`Unsupported computed key in type referenced by a macro`, e.key, scope);
            }
        }
        else if (e.type === 'TSCallSignatureDeclaration') {
            ;
            (res.calls || (res.calls = [])).push(e);
        }
    }
    return res;
}
function mergeElements(maps, type) {
    if (maps.length === 1)
        return maps[0];
    const res = { props: {} };
    const { props: baseProps } = res;
    for (const { props, calls } of maps) {
        for (const key in props) {
            if (!(0, shared_1.hasOwn)(baseProps, key)) {
                baseProps[key] = props[key];
            }
            else {
                baseProps[key] = createProperty(baseProps[key].key, {
                    type,
                    // @ts-expect-error
                    types: [baseProps[key], props[key]],
                }, baseProps[key]._ownerScope, baseProps[key].optional || props[key].optional);
            }
        }
        if (calls) {
            ;
            (res.calls || (res.calls = [])).push(...calls);
        }
    }
    return res;
}
function createProperty(key, typeAnnotation, scope, optional) {
    return {
        type: 'TSPropertySignature',
        key,
        kind: 'get',
        optional,
        typeAnnotation: {
            type: 'TSTypeAnnotation',
            typeAnnotation,
        },
        _ownerScope: scope,
    };
}
function resolveInterfaceMembers(ctx, node, scope, typeParameters) {
    const base = typeElementsToMap(ctx, node.body.body, node._ownerScope, typeParameters);
    if (node.extends) {
        for (const ext of node.extends) {
            if (ext.leadingComments &&
                ext.leadingComments.some((c) => c.value.includes('@vue-ignore'))) {
                continue;
            }
            try {
                const { props, calls } = resolveTypeElements(ctx, ext, scope);
                for (const key in props) {
                    if (!(0, shared_1.hasOwn)(base.props, key)) {
                        base.props[key] = props[key];
                    }
                }
                if (calls) {
                    ;
                    (base.calls || (base.calls = [])).push(...calls);
                }
            }
            catch (e) {
                ctx.error(`Failed to resolve extends base type.\nIf this previously worked in 3.2, ` +
                    `you can instruct the compiler to ignore this extend by adding ` +
                    `/* @vue-ignore */ before it, for example:\n\n` +
                    `interface Props extends /* @vue-ignore */ Base {}\n\n` +
                    `Note: both in 3.2 or with the ignore, the properties in the base ` +
                    `type are treated as fallthrough attrs at runtime.`, ext);
            }
        }
    }
    return base;
}
function resolveMappedType(ctx, node, scope) {
    const res = { props: {} };
    const keys = resolveStringType(ctx, node.typeParameter.constraint, scope);
    for (const key of keys) {
        res.props[key] = createProperty({
            type: 'Identifier',
            name: key,
        }, node.typeAnnotation, scope, !!node.optional);
    }
    return res;
}
function resolveIndexType(ctx, node, scope) {
    if (node.indexType.type === 'TSNumberKeyword') {
        return resolveArrayElementType(ctx, node.objectType, scope);
    }
    const { indexType, objectType } = node;
    const types = [];
    let keys;
    let resolved;
    if (indexType.type === 'TSStringKeyword') {
        resolved = resolveTypeElements(ctx, objectType, scope);
        keys = Object.keys(resolved.props);
    }
    else {
        keys = resolveStringType(ctx, indexType, scope);
        resolved = resolveTypeElements(ctx, objectType, scope);
    }
    for (const key of keys) {
        const targetType = resolved.props[key]?.typeAnnotation?.typeAnnotation;
        if (targetType) {
            ;
            targetType._ownerScope =
                resolved.props[key]._ownerScope;
            types.push(targetType);
        }
    }
    return types;
}
function resolveArrayElementType(ctx, node, scope) {
    // type[]
    if (node.type === 'TSArrayType') {
        return [node.elementType];
    }
    // tuple
    if (node.type === 'TSTupleType') {
        return node.elementTypes.map((t) => t.type === 'TSNamedTupleMember' ? t.elementType : t);
    }
    if (node.type === 'TSTypeReference') {
        // Array<type>
        if (getReferenceName(node) === 'Array' && node.typeParameters) {
            return node.typeParameters.params;
        }
        else {
            const resolved = resolveTypeReference(ctx, node, scope);
            if (resolved) {
                return resolveArrayElementType(ctx, resolved, scope);
            }
        }
    }
    return ctx.error('Failed to resolve element type from target type', node, scope);
}
function resolveStringType(ctx, node, scope) {
    switch (node.type) {
        case 'StringLiteral':
            return [node.value];
        case 'TSLiteralType':
            return resolveStringType(ctx, node.literal, scope);
        case 'TSUnionType':
            return node.types.map((t) => resolveStringType(ctx, t, scope)).flat();
        case 'TemplateLiteral': {
            return resolveTemplateKeys(ctx, node, scope);
        }
        case 'TSTypeReference': {
            const resolved = resolveTypeReference(ctx, node, scope);
            if (resolved) {
                return resolveStringType(ctx, resolved, scope);
            }
            if (node.typeName.type === 'Identifier') {
                const getParam = (index = 0) => resolveStringType(ctx, node.typeParameters.params[index], scope);
                switch (node.typeName.name) {
                    case 'Extract':
                        return getParam(1);
                    case 'Exclude': {
                        const excluded = getParam(1);
                        return getParam().filter((s) => !excluded.includes(s));
                    }
                    case 'Uppercase':
                        return getParam().map((s) => s.toUpperCase());
                    case 'Lowercase':
                        return getParam().map((s) => s.toLowerCase());
                    case 'Capitalize':
                        return getParam().map(shared_1.capitalize);
                    case 'Uncapitalize':
                        return getParam().map((s) => s[0].toLowerCase() + s.slice(1));
                    default:
                        ctx.error('Unsupported type when resolving index type', node.typeName, scope);
                }
            }
        }
    }
    return ctx.error('Failed to resolve index type into finite keys', node, scope);
}
function resolveTemplateKeys(ctx, node, scope) {
    if (!node.expressions.length) {
        return [node.quasis[0].value.raw];
    }
    const res = [];
    const e = node.expressions[0];
    const q = node.quasis[0];
    const leading = q ? q.value.raw : ``;
    const resolved = resolveStringType(ctx, e, scope);
    const restResolved = resolveTemplateKeys(ctx, {
        ...node,
        expressions: node.expressions.slice(1),
        quasis: q ? node.quasis.slice(1) : node.quasis,
    }, scope);
    for (const r of resolved) {
        for (const rr of restResolved) {
            res.push(leading + r + rr);
        }
    }
    return res;
}
const SupportedBuiltinsSet = new Set([
    'Partial',
    'Required',
    'Readonly',
    'Pick',
    'Omit',
]);
function resolveBuiltin(ctx, node, name, scope, typeParameters) {
    const t = resolveTypeElements(ctx, node.typeParameters.params[0], scope, typeParameters);
    switch (name) {
        case 'Partial': {
            const res = { props: {}, calls: t.calls };
            Object.keys(t.props).forEach((key) => {
                res.props[key] = { ...t.props[key], optional: true };
            });
            return res;
        }
        case 'Required': {
            const res = { props: {}, calls: t.calls };
            Object.keys(t.props).forEach((key) => {
                res.props[key] = { ...t.props[key], optional: false };
            });
            return res;
        }
        case 'Readonly':
            return t;
        case 'Pick': {
            const picked = resolveStringType(ctx, node.typeParameters.params[1], scope);
            const res = { props: {}, calls: t.calls };
            for (const key of picked) {
                res.props[key] = t.props[key];
            }
            return res;
        }
        case 'Omit':
            const omitted = resolveStringType(ctx, node.typeParameters.params[1], scope);
            const res = { props: {}, calls: t.calls };
            for (const key in t.props) {
                if (!omitted.includes(key)) {
                    res.props[key] = t.props[key];
                }
            }
            return res;
    }
}
function resolveTypeReference(ctx, node, scope, name, onlyExported = false) {
    if (node._resolvedReference) {
        return node._resolvedReference;
    }
    return (node._resolvedReference = innerResolveTypeReference(ctx, scope || ctxToScope(ctx), name || getReferenceName(node), node, onlyExported));
}
function innerResolveTypeReference(ctx, scope, name, node, onlyExported) {
    if (typeof name === 'string') {
        if (scope.imports[name]) {
            return resolveTypeFromImport(ctx, node, name, scope);
        }
        else {
            const lookupSource = node.type === 'TSTypeQuery'
                ? onlyExported
                    ? scope.exportedDeclares
                    : scope.declares
                : onlyExported
                    ? scope.exportedTypes
                    : scope.types;
            if (lookupSource[name]) {
                return lookupSource[name];
            }
            else {
                // fallback to global
                const globalScopes = resolveGlobalScope(ctx);
                if (globalScopes) {
                    for (const s of globalScopes) {
                        const src = node.type === 'TSTypeQuery' ? s.declares : s.types;
                        if (src[name]) {
                            ;
                            (ctx.deps || (ctx.deps = new Set())).add(s.filename);
                            return src[name];
                        }
                    }
                }
            }
        }
    }
    else {
        let ns = innerResolveTypeReference(ctx, scope, name[0], node, onlyExported);
        if (ns) {
            if (ns.type !== 'TSModuleDeclaration') {
                // namespace merged with other types, attached as _ns
                ns = ns._ns;
            }
            if (ns) {
                const childScope = moduleDeclToScope(ctx, ns, ns._ownerScope || scope);
                return innerResolveTypeReference(ctx, childScope, name.length > 2 ? name.slice(1) : name[name.length - 1], node, !ns.declare);
            }
        }
    }
}
function getReferenceName(node) {
    const ref = node.type === 'TSTypeReference'
        ? node.typeName
        : node.type === 'TSExpressionWithTypeArguments'
            ? node.expression
            : node.type === 'TSImportType'
                ? node.qualifier
                : node.exprName;
    if (ref?.type === 'Identifier') {
        return ref.name;
    }
    else if (ref?.type === 'TSQualifiedName') {
        return qualifiedNameToPath(ref);
    }
    else {
        return 'default';
    }
}
function qualifiedNameToPath(node) {
    if (node.type === 'Identifier') {
        return [node.name];
    }
    else {
        return [...qualifiedNameToPath(node.left), node.right.name];
    }
}
function resolveGlobalScope(ctx) {
    if (ctx.options.globalTypeFiles) {
        const fs = resolveFS(ctx);
        if (!fs) {
            throw new Error('[vue/compiler-sfc] globalTypeFiles requires fs access.');
        }
        return ctx.options.globalTypeFiles.map((file) => fileToScope(ctx, (0, utils_1.normalizePath)(file), true));
    }
}
function resolveFS(ctx) {
    if (ctx.fs) {
        return ctx.fs;
    }
    const fs = ctx.options.fs || {
        fileExists: fs_extra_1.default.existsSync,
        readFile(file) {
            return fs_extra_1.default.readFileSync(file, 'utf-8');
        },
    };
    if (!fs) {
        return;
    }
    return (ctx.fs = {
        fileExists(file) {
            if (file.startsWith('@/')) {
                file = file.replace('@/', (0, utils_1.normalizePath)(process.env.UNI_INPUT_DIR));
            }
            return fs.fileExists(file);
        },
        readFile(file) {
            if (file.startsWith('@/')) {
                file = file.replace('@/', (0, utils_1.normalizePath)(process.env.UNI_INPUT_DIR));
            }
            return fs.readFile(file);
        },
    });
}
function resolveTypeFromImport(ctx, node, name, scope) {
    const { source, imported } = scope.imports[name];
    const sourceScope = importSourceToScope(ctx, node, scope, source);
    return resolveTypeReference(ctx, node, sourceScope, imported, true);
}
function importSourceToScope(ctx, node, scope, source) {
    let fs;
    try {
        fs = resolveFS(ctx);
    }
    catch (err) {
        return ctx.error(err.message, node, scope);
    }
    if (!fs) {
        return ctx.error(`No fs option provided to \`compileScript\` in non-Node environment. ` +
            `File system access is required for resolving imported types.`, node, scope);
    }
    let resolved = scope.resolvedImportSources[source];
    if (!resolved) {
        if (source.startsWith('..')) {
            const osSpecificJoinFn = process.platform === 'win32' ? path_1.join : utils_1.joinPaths;
            const filename = osSpecificJoinFn((0, path_1.dirname)(scope.filename), source);
            resolved = resolveExt(filename, fs);
        }
        else if (source.startsWith('.')) {
            // relative import - fast path
            const filename = (0, utils_1.joinPaths)((0, path_1.dirname)(scope.filename), source);
            resolved = resolveExt(filename, fs);
        }
        else if (source.startsWith('@/')) {
            const filename = (0, utils_1.joinPaths)(process.env.UNI_INPUT_DIR, source.replace('@/', ''));
            resolved = resolveExt(filename, fs);
        }
        else {
            // module or aliased import - use full TS resolution, only supported in Node
            return ctx.error(`Failed to resolve import source ${JSON.stringify(source)}. `, node, scope);
        }
        if (resolved) {
            resolved = scope.resolvedImportSources[source] = (0, utils_1.normalizePath)(resolved);
        }
    }
    if (resolved) {
        // (hmr) register dependency file on ctx
        ;
        (ctx.deps || (ctx.deps = new Set())).add(resolved);
        return fileToScope(ctx, resolved);
    }
    else {
        return ctx.error(`Failed to resolve import source ${JSON.stringify(source)}.`, node, scope);
    }
}
function resolveExt(filename, fs) {
    // #8339 ts may import .js but we should resolve to corresponding ts or d.ts
    filename = filename.replace(/\.js$/, '');
    const tryResolve = (filename) => {
        if (fs.fileExists(filename))
            return filename;
    };
    return (tryResolve(filename) ||
        tryResolve(filename + `.ts`) ||
        tryResolve(filename + `.d.ts`) ||
        tryResolve((0, utils_1.joinPaths)(filename, `index.ts`)) ||
        tryResolve((0, utils_1.joinPaths)(filename, `index.d.ts`)));
}
// const fileToScopeCache = createCache<TypeScope>()
/**
 * @private
 */
function invalidateTypeCache(filename) {
    // filename = normalizePath(filename)
    // fileToScopeCache.delete(filename)
}
exports.invalidateTypeCache = invalidateTypeCache;
function fileToScope(ctx, filename, asGlobal = false) {
    // const cached = fileToScopeCache.get(filename)
    // if (cached) {
    //   return cached
    // }
    // fs should be guaranteed to exist here
    const fs = resolveFS(ctx);
    const source = (0, uni_cli_shared_1.preUVueJs)(fs.readFile(filename) || '');
    const body = parseFile(filename, source, ctx.options.babelParserPlugins);
    const scope = new TypeScope(filename, source, 0, recordImports(body));
    recordTypes(ctx, body, scope, asGlobal);
    // fileToScopeCache.set(filename, scope)
    return scope;
}
exports.fileToScope = fileToScope;
function parseFile(filename, content, parserPlugins) {
    const ext = (0, path_1.extname)(filename);
    if (ext === '.uts' || ext === '.ts') {
        return (0, parser_1.parse)(content, {
            plugins: (0, context_1.resolveParserPlugins)('ts', parserPlugins, filename.endsWith('.d.ts')),
            sourceType: 'module',
        }).program.body;
    }
    else if (ext === '.vue') {
        const { descriptor: { script, scriptSetup }, } = (0, compiler_sfc_1.parse)(content);
        if (!script && !scriptSetup) {
            return [];
        }
        // ensure the correct offset with original source
        const scriptOffset = script ? script.loc.start.offset : Infinity;
        const scriptSetupOffset = scriptSetup
            ? scriptSetup.loc.start.offset
            : Infinity;
        const firstBlock = scriptOffset < scriptSetupOffset ? script : scriptSetup;
        const secondBlock = scriptOffset < scriptSetupOffset ? scriptSetup : script;
        let scriptContent = ' '.repeat(Math.min(scriptOffset, scriptSetupOffset)) +
            firstBlock.content;
        if (secondBlock) {
            scriptContent +=
                ' '.repeat(secondBlock.loc.start.offset - script.loc.end.offset) +
                    secondBlock.content;
        }
        return (0, parser_1.parse)(scriptContent, {
            plugins: (0, context_1.resolveParserPlugins)('ts', parserPlugins),
            sourceType: 'module',
        }).program.body;
    }
    return [];
}
function ctxToScope(ctx) {
    if (ctx.scope) {
        return ctx.scope;
    }
    const body = 'ast' in ctx
        ? ctx.ast
        : ctx.scriptAst
            ? [...ctx.scriptAst.body, ...ctx.scriptSetupAst.body]
            : ctx.scriptSetupAst.body;
    const scope = new TypeScope(ctx.filename, ctx.source, 'startOffset' in ctx ? ctx.startOffset : 0, 'userImports' in ctx ? Object.create(ctx.userImports) : recordImports(body));
    recordTypes(ctx, body, scope);
    return (ctx.scope = scope);
}
function moduleDeclToScope(ctx, node, parentScope) {
    if (node._resolvedChildScope) {
        return node._resolvedChildScope;
    }
    const scope = createChildScope(parentScope);
    if (node.body.type === 'TSModuleDeclaration') {
        const decl = node.body;
        decl._ownerScope = scope;
        const id = (0, utils_1.getId)(decl.id);
        scope.types[id] = scope.exportedTypes[id] = decl;
    }
    else {
        recordTypes(ctx, node.body.body, scope);
    }
    return (node._resolvedChildScope = scope);
}
function createChildScope(parentScope) {
    return new TypeScope(parentScope.filename, parentScope.source, parentScope.offset, Object.create(parentScope.imports), Object.create(parentScope.types), Object.create(parentScope.declares));
}
const importExportRE = /^Import|^Export/;
function recordTypes(ctx, body, scope, asGlobal = false) {
    const { types, declares, exportedTypes, exportedDeclares, imports } = scope;
    const isAmbient = asGlobal
        ? !body.some((s) => importExportRE.test(s.type))
        : false;
    for (const stmt of body) {
        if (asGlobal) {
            if (isAmbient) {
                if (stmt.declare) {
                    recordType(stmt, types, declares);
                }
            }
            else if (stmt.type === 'TSModuleDeclaration' && stmt.global) {
                for (const s of stmt.body.body) {
                    recordType(s, types, declares);
                }
            }
        }
        else {
            recordType(stmt, types, declares);
        }
    }
    if (!asGlobal) {
        for (const stmt of body) {
            if (stmt.type === 'ExportNamedDeclaration') {
                if (stmt.declaration) {
                    recordType(stmt.declaration, types, declares);
                    recordType(stmt.declaration, exportedTypes, exportedDeclares);
                }
                else {
                    for (const spec of stmt.specifiers) {
                        if (spec.type === 'ExportSpecifier') {
                            const local = spec.local.name;
                            const exported = (0, utils_1.getId)(spec.exported);
                            if (stmt.source) {
                                // re-export, register an import + export as a type reference
                                imports[exported] = {
                                    source: stmt.source.value,
                                    imported: local,
                                };
                                exportedTypes[exported] = {
                                    type: 'TSTypeReference',
                                    typeName: {
                                        type: 'Identifier',
                                        name: local,
                                    },
                                    _ownerScope: scope,
                                };
                            }
                            else if (types[local]) {
                                // exporting local defined type
                                exportedTypes[exported] = types[local];
                            }
                        }
                    }
                }
            }
            else if (stmt.type === 'ExportAllDeclaration') {
                const sourceScope = importSourceToScope(ctx, stmt.source, scope, stmt.source.value);
                Object.assign(scope.exportedTypes, sourceScope.exportedTypes);
            }
            else if (stmt.type === 'ExportDefaultDeclaration' && stmt.declaration) {
                if (stmt.declaration.type !== 'Identifier') {
                    recordType(stmt.declaration, types, declares, 'default');
                    recordType(stmt.declaration, exportedTypes, exportedDeclares, 'default');
                }
                else if (types[stmt.declaration.name]) {
                    exportedTypes['default'] = types[stmt.declaration.name];
                }
            }
        }
    }
    for (const key of Object.keys(types)) {
        const node = types[key];
        node._ownerScope = scope;
        if (node._ns)
            node._ns._ownerScope = scope;
    }
    for (const key of Object.keys(declares)) {
        declares[key]._ownerScope = scope;
    }
}
function recordType(node, types, declares, overwriteId) {
    switch (node.type) {
        case 'TSInterfaceDeclaration':
        case 'TSEnumDeclaration':
        case 'TSModuleDeclaration': {
            const id = overwriteId || (0, utils_1.getId)(node.id);
            let existing = types[id];
            if (existing) {
                if (node.type === 'TSModuleDeclaration') {
                    if (existing.type === 'TSModuleDeclaration') {
                        mergeNamespaces(existing, node);
                    }
                    else {
                        attachNamespace(existing, node);
                    }
                    break;
                }
                if (existing.type === 'TSModuleDeclaration') {
                    // replace and attach namespace
                    types[id] = node;
                    attachNamespace(node, existing);
                    break;
                }
                if (existing.type !== node.type) {
                    // type-level error
                    break;
                }
                if (node.type === 'TSInterfaceDeclaration') {
                    ;
                    existing.body.body.push(...node.body.body);
                }
                else {
                    ;
                    existing.members.push(...node.members);
                }
            }
            else {
                types[id] = node;
            }
            break;
        }
        case 'ClassDeclaration':
            if (overwriteId || node.id)
                types[overwriteId || (0, utils_1.getId)(node.id)] = node;
            break;
        case 'TSTypeAliasDeclaration':
            types[node.id.name] = node.typeParameters ? node : node.typeAnnotation;
            break;
        case 'TSDeclareFunction':
            if (node.id)
                declares[node.id.name] = node;
            break;
        case 'VariableDeclaration': {
            if (node.declare) {
                for (const decl of node.declarations) {
                    if (decl.id.type === 'Identifier' && decl.id.typeAnnotation) {
                        declares[decl.id.name] = decl.id.typeAnnotation.typeAnnotation;
                    }
                }
            }
            break;
        }
    }
}
function mergeNamespaces(to, from) {
    const toBody = to.body;
    const fromBody = from.body;
    if (toBody.type === 'TSModuleDeclaration') {
        if (fromBody.type === 'TSModuleDeclaration') {
            // both decl
            mergeNamespaces(toBody, fromBody);
        }
        else {
            // to: decl -> from: block
            fromBody.body.push({
                type: 'ExportNamedDeclaration',
                declaration: toBody,
                exportKind: 'type',
                specifiers: [],
            });
        }
    }
    else if (fromBody.type === 'TSModuleDeclaration') {
        // to: block <- from: decl
        toBody.body.push({
            type: 'ExportNamedDeclaration',
            declaration: fromBody,
            exportKind: 'type',
            specifiers: [],
        });
    }
    else {
        // both block
        toBody.body.push(...fromBody.body);
    }
}
function attachNamespace(to, ns) {
    if (!to._ns) {
        to._ns = ns;
    }
    else {
        mergeNamespaces(to._ns, ns);
    }
}
function recordImports(body) {
    const imports = Object.create(null);
    for (const s of body) {
        recordImport(s, imports);
    }
    return imports;
}
exports.recordImports = recordImports;
function recordImport(node, imports) {
    if (node.type !== 'ImportDeclaration') {
        return;
    }
    for (const s of node.specifiers) {
        imports[s.local.name] = {
            imported: (0, utils_1.getImportedName)(s),
            source: node.source.value,
        };
    }
}
function inferRuntimeType(ctx, node, from = 'defineProps', scope = node._ownerScope || ctxToScope(ctx)) {
    try {
        switch (node.type) {
            case 'TSAnyKeyword':
                return ['Object'];
            case 'TSStringKeyword':
                return ['String'];
            case 'TSNumberKeyword':
                return ['Number'];
            case 'TSBooleanKeyword':
                return ['Boolean'];
            case 'TSObjectKeyword':
                return ['Object'];
            case 'TSNullKeyword':
                return ['null'];
            case 'TSTypeLiteral':
            case 'TSInterfaceDeclaration': {
                // TODO (nice to have) generate runtime property validation
                const types = new Set();
                const members = node.type === 'TSTypeLiteral' ? node.members : node.body.body;
                for (const m of members) {
                    if (m.type === 'TSCallSignatureDeclaration' ||
                        m.type === 'TSConstructSignatureDeclaration') {
                        if (from === 'defineProps') {
                            types.add('Function as PropType<' +
                                scope.source.slice(m.start + scope.offset, m.end + scope.offset) +
                                '>');
                        }
                        else {
                            types.add('Function');
                        }
                    }
                    else {
                        if (from === 'defineProps') {
                            types.add('Object as PropType<' +
                                scope.source.slice(m.start + scope.offset, m.end + scope.offset) +
                                '>');
                        }
                        else {
                            types.add('Object');
                        }
                    }
                }
                return types.size ? Array.from(types) : ['Object'];
            }
            case 'TSPropertySignature':
                if (node.typeAnnotation) {
                    return inferRuntimeType(ctx, node.typeAnnotation.typeAnnotation, from, scope);
                }
                break;
            case 'TSMethodSignature':
                return ['Function'];
            case 'TSFunctionType':
                let type = 'Function';
                if (from === 'defineProps') {
                    const fnType = scope.source.slice(node.start + scope.offset, node.end + scope.offset);
                    type = 'Function as PropType<' + fnType + '>';
                }
                return [type];
            case 'TSArrayType':
            case 'TSTupleType':
                // TODO (nice to have) generate runtime element type/length checks
                return [
                    from === 'defineProps'
                        ? 'Array as PropType<' +
                            scope.source.slice(node.start + scope.offset, node.end + scope.offset) +
                            '>'
                        : 'Array',
                ];
            case 'TSLiteralType':
                switch (node.literal.type) {
                    case 'StringLiteral':
                        return ['String'];
                    case 'BooleanLiteral':
                        return ['Boolean'];
                    case 'NumericLiteral':
                    case 'BigIntLiteral':
                        return ['Number'];
                    default:
                        return [utils_1.UNKNOWN_TYPE];
                }
            case 'TSTypeReference': {
                const resolved = resolveTypeReference(ctx, node, scope);
                if (resolved) {
                    return inferRuntimeType(ctx, resolved, from, resolved._ownerScope);
                }
                if (node.typeName.type === 'Identifier') {
                    return [
                        scope.source.slice(node.start + scope.offset, node.end + scope.offset),
                    ];
                    // switch (node.typeName.name) {
                    //   case 'Array':
                    //   case 'Function':
                    //   case 'Object':
                    //   case 'Set':
                    //   case 'Map':
                    //   case 'WeakSet':
                    //   case 'WeakMap':
                    //   case 'Date':
                    //   case 'Promise':
                    //   case 'Error':
                    //     return [node.typeName.name]
                    //   // TS built-in utility types
                    //   // https://www.typescriptlang.org/docs/handbook/utility-types.html
                    //   case 'Partial':
                    //   case 'Required':
                    //   case 'Readonly':
                    //   case 'Record':
                    //   case 'Pick':
                    //   case 'Omit':
                    //   case 'InstanceType':
                    //     return ['Object']
                    //   case 'Uppercase':
                    //   case 'Lowercase':
                    //   case 'Capitalize':
                    //   case 'Uncapitalize':
                    //     return ['String']
                    //   case 'Parameters':
                    //   case 'ConstructorParameters':
                    //     return ['Array']
                    //   case 'NonNullable':
                    //     if (node.typeParameters && node.typeParameters.params[0]) {
                    //       return inferRuntimeType(
                    //         ctx,
                    //         node.typeParameters.params[0],
                    //         scope
                    //       ).filter((t) => t !== 'null')
                    //     }
                    //     break
                    //   case 'Extract':
                    //     if (node.typeParameters && node.typeParameters.params[1]) {
                    //       return inferRuntimeType(
                    //         ctx,
                    //         node.typeParameters.params[1],
                    //         scope
                    //       )
                    //     }
                    //     break
                    //   case 'Exclude':
                    //   case 'OmitThisParameter':
                    //     if (node.typeParameters && node.typeParameters.params[0]) {
                    //       return inferRuntimeType(
                    //         ctx,
                    //         node.typeParameters.params[0],
                    //         scope
                    //       )
                    //     }
                    //     break
                    //   default:
                    //     // fixed by xxxxxx 未知的类型也返回，比如UTSJSONObject
                    //     return [node.typeName.name]
                    // }
                }
                // cannot infer, fallback to UNKNOWN: ThisParameterType
                break;
            }
            case 'TSParenthesizedType':
                return inferRuntimeType(ctx, node.typeAnnotation, from, scope);
            case 'TSUnionType':
                return flattenTypes(ctx, node.types, from, scope);
            case 'TSIntersectionType': {
                return flattenTypes(ctx, node.types, from, scope).filter((t) => t !== utils_1.UNKNOWN_TYPE);
            }
            case 'TSEnumDeclaration':
                return inferEnumType(node);
            case 'TSSymbolKeyword':
                return ['Symbol'];
            case 'TSIndexedAccessType': {
                const types = resolveIndexType(ctx, node, scope);
                return flattenTypes(ctx, types, from, scope);
            }
            case 'ClassDeclaration':
                return ['Object'];
            case 'TSImportType': {
                const sourceScope = importSourceToScope(ctx, node.argument, scope, node.argument.value);
                const resolved = resolveTypeReference(ctx, node, sourceScope);
                if (resolved) {
                    return inferRuntimeType(ctx, resolved, from, resolved._ownerScope);
                }
                break;
            }
            case 'TSTypeQuery': {
                const id = node.exprName;
                if (id.type === 'Identifier') {
                    // typeof only support identifier in local scope
                    const matched = scope.declares[id.name];
                    if (matched) {
                        return inferRuntimeType(ctx, matched, from, matched._ownerScope);
                    }
                }
                break;
            }
        }
    }
    catch (e) {
        // always soft fail on failed runtime type inference
    }
    return [utils_1.UNKNOWN_TYPE]; // no runtime check
}
exports.inferRuntimeType = inferRuntimeType;
function flattenTypes(ctx, types, from, scope) {
    if (types.length === 1) {
        return inferRuntimeType(ctx, types[0], from, scope);
    }
    return [
        ...new Set([].concat(...types.map((t) => inferRuntimeType(ctx, t, from, scope)))),
    ];
}
function inferEnumType(node) {
    const types = new Set();
    for (const m of node.members) {
        if (m.initializer) {
            switch (m.initializer.type) {
                case 'StringLiteral':
                    types.add('String');
                    break;
                case 'NumericLiteral':
                    types.add('Number');
                    break;
            }
        }
    }
    return types.size ? [...types] : ['Number'];
}
/**
 * support for the `ExtractPropTypes` helper - it's non-exhaustive, mostly
 * tailored towards popular component libs like element-plus and antd-vue.
 */
function resolveExtractPropTypes({ props }, scope) {
    const res = { props: {} };
    for (const key in props) {
        const raw = props[key];
        res.props[key] = reverseInferType(raw.key, raw.typeAnnotation.typeAnnotation, scope);
    }
    return res;
}
function reverseInferType(key, node, scope, optional = true, checkObjectSyntax = true) {
    if (checkObjectSyntax && node.type === 'TSTypeLiteral') {
        // check { type: xxx }
        const typeType = findStaticPropertyType(node, 'type');
        if (typeType) {
            const requiredType = findStaticPropertyType(node, 'required');
            const optional = requiredType &&
                requiredType.type === 'TSLiteralType' &&
                requiredType.literal.type === 'BooleanLiteral'
                ? !requiredType.literal.value
                : true;
            return reverseInferType(key, typeType, scope, optional, false);
        }
    }
    else if (node.type === 'TSTypeReference' &&
        node.typeName.type === 'Identifier') {
        if (node.typeName.name.endsWith('Constructor')) {
            return createProperty(key, ctorToType(node.typeName.name), scope, optional);
        }
        else if (node.typeName.name === 'PropType' && node.typeParameters) {
            // PropType<{}>
            return createProperty(key, node.typeParameters.params[0], scope, optional);
        }
    }
    if ((node.type === 'TSTypeReference' || node.type === 'TSImportType') &&
        node.typeParameters) {
        // try if we can catch Foo.Bar<XXXConstructor>
        for (const t of node.typeParameters.params) {
            const inferred = reverseInferType(key, t, scope, optional);
            if (inferred)
                return inferred;
        }
    }
    return createProperty(key, { type: `TSNullKeyword` }, scope, optional);
}
function ctorToType(ctorType) {
    const ctor = ctorType.slice(0, -11);
    switch (ctor) {
        case 'String':
        case 'Number':
        case 'Boolean':
            return { type: `TS${ctor}Keyword` };
        case 'Array':
        case 'Function':
        case 'Object':
        case 'Set':
        case 'Map':
        case 'WeakSet':
        case 'WeakMap':
        case 'Date':
        case 'Promise':
            return {
                type: 'TSTypeReference',
                typeName: { type: 'Identifier', name: ctor },
            };
    }
    // fallback to null
    return { type: `TSNullKeyword` };
}
function findStaticPropertyType(node, key) {
    const prop = node.members.find((m) => m.type === 'TSPropertySignature' &&
        !m.computed &&
        (0, utils_1.getId)(m.key) === key &&
        m.typeAnnotation);
    return prop && prop.typeAnnotation.typeAnnotation;
}
function resolveReturnType(ctx, arg, scope) {
    let resolved = arg;
    if (arg.type === 'TSTypeReference' ||
        arg.type === 'TSTypeQuery' ||
        arg.type === 'TSImportType') {
        resolved = resolveTypeReference(ctx, arg, scope);
    }
    if (!resolved)
        return;
    if (resolved.type === 'TSFunctionType') {
        return resolved.typeAnnotation?.typeAnnotation;
    }
    if (resolved.type === 'TSDeclareFunction') {
        return resolved.returnType;
    }
}
function resolveUnionType(ctx, node, scope) {
    if (node.type === 'TSTypeReference') {
        const resolved = resolveTypeReference(ctx, node, scope);
        if (resolved)
            node = resolved;
    }
    let types;
    if (node.type === 'TSUnionType') {
        types = node.types.flatMap((node) => resolveUnionType(ctx, node, scope));
    }
    else {
        types = [node];
    }
    return types;
}
exports.resolveUnionType = resolveUnionType;
