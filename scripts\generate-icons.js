const fs = require('fs');
const path = require('path');

// 创建静态资源目录
const staticDir = path.join(__dirname, '../src/static');
const tabbarDir = path.join(staticDir, 'tabbar');

if (!fs.existsSync(staticDir)) {
  fs.mkdirSync(staticDir, { recursive: true });
}

if (!fs.existsSync(tabbarDir)) {
  fs.mkdirSync(tabbarDir, { recursive: true });
}

// 简单的SVG图标模板
const createSVGIcon = (iconType, isActive = false) => {
  const color = isActive ? '#007aff' : '#666666';
  
  const icons = {
    home: `<svg width="24" height="24" viewBox="0 0 24 24" fill="${color}" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
    </svg>`,
    
    category: `<svg width="24" height="24" viewBox="0 0 24 24" fill="${color}" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z"/>
    </svg>`,
    
    cart: `<svg width="24" height="24" viewBox="0 0 24 24" fill="${color}" xmlns="http://www.w3.org/2000/svg">
      <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
    </svg>`,
    
    profile: `<svg width="24" height="24" viewBox="0 0 24 24" fill="${color}" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
    </svg>`
  };
  
  return icons[iconType] || icons.home;
};

// 将SVG转换为PNG的简单方法（这里我们直接创建SVG文件）
const generateIcon = (iconType, isActive = false) => {
  const svgContent = createSVGIcon(iconType, isActive);
  const fileName = isActive ? `${iconType}-active.svg` : `${iconType}.svg`;
  const filePath = path.join(tabbarDir, fileName);
  
  fs.writeFileSync(filePath, svgContent);
  console.log(`Generated: ${fileName}`);
};

// 生成所有图标
const iconTypes = ['home', 'category', 'cart', 'profile'];

iconTypes.forEach(iconType => {
  generateIcon(iconType, false);  // 普通状态
  generateIcon(iconType, true);   // 选中状态
});

console.log('All tabbar icons generated successfully!');
