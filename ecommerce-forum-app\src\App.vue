<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
  console.log('App Launch')
})

onShow(() => {
  console.log('App Show')
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 通用样式 */
.container {
  padding: 0;
  margin: 0;
}

/* 主题色 */
:root {
  --primary-color: #ff6b35;
  --secondary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-placeholder: #999999;
  --border-color: #e8e8e8;
  --background-color: #f5f5f5;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.btn-primary {
    background-color: var(--primary-color);
    color: #ffffff;
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  &.btn-secondary {
    background-color: #ffffff;
    color: var(--primary-color);
    border: 2rpx solid var(--primary-color);
    
    &:hover {
      background-color: var(--primary-color);
      color: #ffffff;
    }
  }
  
  &.btn-small {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
  }
  
  &.btn-large {
    padding: 28rpx 56rpx;
    font-size: 32rpx;
  }
}

/* 通用卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 通用列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid var(--border-color);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f8f8f8;
  }
}

/* 通用文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 32rpx;
}

.text-bold {
  font-weight: 600;
}

/* 通用间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 通用布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

/* 通用图片样式 */
.image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 通用圆角 */
.radius-small {
  border-radius: 4rpx;
}

.radius-medium {
  border-radius: 8rpx;
}

.radius-large {
  border-radius: 12rpx;
}

.radius-circle {
  border-radius: 50%;
}

/* 通用阴影 */
.shadow-small {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
}

.shadow-large {
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}
</style>
