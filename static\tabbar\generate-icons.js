// 生成简单的SVG图标，然后可以转换为PNG
const fs = require('fs');
const path = require('path');

// 创建SVG图标
const icons = {
  home: {
    normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M22 8L8 18V36H16V26H28V36H36V18L22 8Z" fill="#999999"/>
    </svg>`,
    active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M22 8L8 18V36H16V26H28V36H36V18L22 8Z" fill="#1677ff"/>
    </svg>`
  },
  finance: {
    normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M22 4C12.6 4 5 11.6 5 21S12.6 38 22 38S39 30.4 39 21S31.4 4 22 4ZM22 6C30.3 6 37 12.7 37 21S30.3 36 22 36S7 29.3 7 21S13.7 6 22 6ZM20 12V14H18V16H20V18H18V20H20V22H24V20H26V18H24V16H26V14H24V12H20ZM20 14H24V16H20V14ZM20 18H24V20H20V18Z" fill="#999999"/>
    </svg>`,
    active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M22 4C12.6 4 5 11.6 5 21S12.6 38 22 38S39 30.4 39 21S31.4 4 22 4ZM22 6C30.3 6 37 12.7 37 21S30.3 36 22 36S7 29.3 7 21S13.7 6 22 6ZM20 12V14H18V16H20V18H18V20H20V22H24V20H26V18H24V16H26V14H24V12H20ZM20 14H24V16H20V14ZM20 18H24V20H20V18Z" fill="#1677ff"/>
    </svg>`
  },
  fund: {
    normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 32L16 24L22 28L36 14L38 16L22 32L16 28L10 34L8 32Z" fill="#999999"/>
      <rect x="6" y="6" width="32" height="32" rx="2" stroke="#999999" stroke-width="2" fill="none"/>
    </svg>`,
    active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 32L16 24L22 28L36 14L38 16L22 32L16 28L10 34L8 32Z" fill="#1677ff"/>
      <rect x="6" y="6" width="32" height="32" rx="2" stroke="#1677ff" stroke-width="2" fill="none"/>
    </svg>`
  },
  profile: {
    normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="22" cy="16" r="6" fill="#999999"/>
      <path d="M22 26C16 26 8 29 8 35V38H36V35C36 29 28 26 22 26Z" fill="#999999"/>
    </svg>`,
    active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="22" cy="16" r="6" fill="#1677ff"/>
      <path d="M22 26C16 26 8 29 8 35V38H36V35C36 29 28 26 22 26Z" fill="#1677ff"/>
    </svg>`
  }
};

// 保存SVG文件
Object.keys(icons).forEach(iconName => {
  fs.writeFileSync(`${iconName}.svg`, icons[iconName].normal);
  fs.writeFileSync(`${iconName}-active.svg`, icons[iconName].active);
});

console.log('SVG图标已生成，请使用在线工具或设计软件将其转换为PNG格式');
console.log('建议尺寸：44x44px');
console.log('文件命名：');
console.log('- home.png / home-active.png');
console.log('- finance.png / finance-active.png'); 
console.log('- fund.png / fund-active.png');
console.log('- profile.png / profile-active.png');
