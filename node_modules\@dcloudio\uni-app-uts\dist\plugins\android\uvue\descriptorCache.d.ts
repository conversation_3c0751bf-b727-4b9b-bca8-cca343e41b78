import type * as _compiler from '@vue/compiler-sfc';
import type { CompilerError, SFCDescriptor } from '@vue/compiler-sfc';
export interface ResolvedOptions {
    compiler: typeof _compiler;
    root: string;
    sourceMap: boolean;
    targetLanguage?: 'kotlin';
    classNamePrefix?: string;
    genDefaultAs?: string;
}
export declare function getResolvedOptions(): ResolvedOptions;
export interface SFCParseResult {
    descriptor: SFCDescriptor;
    errors: Array<CompilerError | SyntaxError>;
}
export declare const cache: Map<string, _compiler.SFCDescriptor>;
declare module '@vue/compiler-sfc' {
    interface SFCDescriptor {
        id: string;
        relativeFilename: string;
    }
}
export declare function createDescriptor(filename: string, source: string, { root, sourceMap, compiler }: ResolvedOptions): SFCParseResult;
export declare function getDescriptor(filename: string, options: ResolvedOptions, createIfNotFound?: boolean): SFCDescriptor | undefined;
export declare function getSrcDescriptor(filename: string): SFCDescriptor;
export declare function setSrcDescriptor(filename: string, entry: SFCDescriptor): void;
