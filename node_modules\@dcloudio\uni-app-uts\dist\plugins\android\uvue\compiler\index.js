"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.compile = exports.getBaseTransformPreset = void 0;
const shared_1 = require("@vue/shared");
const compiler_core_1 = require("@vue/compiler-core");
const uni_shared_1 = require("@dcloudio/uni-shared");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
require("./runtimeHelpers");
const codegen_1 = require("./codegen");
const transform_1 = require("./transform");
const vIf_1 = require("./transforms/vIf");
const vFor_1 = require("./transforms/vFor");
const vModel_1 = require("./transforms/vModel");
const vShow_1 = require("./transforms/vShow");
const vText_1 = require("./transforms/vText");
const transformInterpolation_1 = require("./transforms/transformInterpolation");
const transformText_1 = require("./transforms/transformText");
const vOnWithModifier_1 = require("./transforms/vOnWithModifier");
const vBind_1 = require("./transforms/vBind");
const transformSlotOutlet_1 = require("./transforms/transformSlotOutlet");
const transformObjectExpression_1 = require("./transforms/transformObjectExpression");
const transformExpression_1 = require("./transforms/transformExpression");
const transformElements_1 = require("./transforms/transformElements");
const transformStyle_1 = require("./transforms/transformStyle");
const vHtml_1 = require("./transforms/vHtml");
const vMemo_1 = require("./transforms/vMemo");
const vOnce_1 = require("./transforms/vOnce");
const source_map_js_1 = require("source-map-js");
const vSlot_1 = require("./transforms/vSlot");
const transformElement_1 = require("./transforms/transformElement");
function getBaseTransformPreset(prefixIdentifiers) {
    return [
        [
            vOnce_1.transformOnce,
            vIf_1.transformIf,
            vMemo_1.transformMemo,
            vFor_1.transformFor,
            // order is important
            vSlot_1.trackVForSlotScopes,
            vHtml_1.transformVHtml,
            transformExpression_1.transformExpression,
            transformSlotOutlet_1.transformSlotOutlet,
            transformElement_1.transformElement,
            vSlot_1.trackSlotScopes,
            transformText_1.transformText,
            uni_cli_shared_1.transformTapToClick,
            transformInterpolation_1.transformInterpolation,
            transformObjectExpression_1.transformObjectExpression,
            transformElements_1.transformElements,
            transformStyle_1.transformStyle,
        ],
        {
            on: vOnWithModifier_1.transformOn,
            bind: vBind_1.transformBind,
            model: vModel_1.transformModel,
            show: vShow_1.transformShow,
            text: vText_1.transformVText,
        },
    ];
}
exports.getBaseTransformPreset = getBaseTransformPreset;
function compile(template, options) {
    options.rootDir = options.rootDir || '';
    options.targetLanguage = options.targetLanguage || 'kotlin';
    options.prefixIdentifiers =
        'prefixIdentifiers' in options
            ? options.prefixIdentifiers
            : options.mode === 'module';
    wrapOptionsLog(template, options);
    const isNativeTag = options?.isNativeTag ||
        function (tag) {
            return ((0, uni_shared_1.isAppUVueNativeTag)(tag) ||
                !!options.parseUTSComponent?.(tag, options.targetLanguage));
        };
    const ast = (0, compiler_core_1.baseParse)(template, {
        comments: false,
        isNativeTag,
        onError: options.onError,
    });
    const [nodeTransforms, directiveTransforms] = getBaseTransformPreset(options.prefixIdentifiers);
    (0, transform_1.transform)(ast, (0, shared_1.extend)({}, options, {
        nodeTransforms: [
            ...nodeTransforms,
            ...(0, uni_cli_shared_1.getBaseNodeTransforms)('/'),
            ...(options.nodeTransforms || []), // user transforms
        ],
        directiveTransforms: (0, shared_1.extend)({}, directiveTransforms, options.directiveTransforms || {} // user transforms
        ),
    }));
    const result = (0, codegen_1.generate)(ast, options);
    // inMap should be the map produced by ./parse.ts which is a simple line-only
    // mapping. If it is present, we need to adjust the final map and errors to
    // reflect the original line numbers.
    if (options.inMap) {
        if (options.sourceMap) {
            result.map = mapLines(options.inMap, result.map);
        }
        // if (result.errors.length) {
        //   patchErrors(errors, source, inMap)
        // }
    }
    return result;
}
exports.compile = compile;
function mapLines(oldMap, newMap) {
    if (!oldMap)
        return newMap;
    if (!newMap)
        return oldMap;
    const oldMapConsumer = new source_map_js_1.SourceMapConsumer(oldMap);
    const newMapConsumer = new source_map_js_1.SourceMapConsumer(newMap);
    const mergedMapGenerator = new source_map_js_1.SourceMapGenerator();
    newMapConsumer.eachMapping((m) => {
        if (m.originalLine == null) {
            return;
        }
        const origPosInOldMap = oldMapConsumer.originalPositionFor({
            line: m.originalLine,
            column: m.originalColumn,
        });
        if (origPosInOldMap.source == null) {
            return;
        }
        mergedMapGenerator.addMapping({
            generated: {
                line: m.generatedLine,
                column: m.generatedColumn,
            },
            original: {
                line: origPosInOldMap.line, // map line
                // use current column, since the oldMap produced by @vue/compiler-sfc
                // does not
                column: m.originalColumn,
            },
            source: origPosInOldMap.source,
            name: origPosInOldMap.name,
        });
    });
    // source-map's type definition is incomplete
    const generator = mergedMapGenerator;
    oldMapConsumer.sources.forEach((sourceFile) => {
        generator._sources.add(sourceFile);
        const sourceContent = oldMapConsumer.sourceContentFor(sourceFile);
        if (sourceContent != null) {
            mergedMapGenerator.setSourceContent(sourceFile, sourceContent);
        }
    });
    generator._sourceRoot = oldMap.sourceRoot;
    generator._file = oldMap.file;
    return generator.toJSON();
}
function wrapOptionsLog(source, options) {
    const { onWarn, onError, inMap } = options;
    if (inMap && inMap.sourcesContent?.length) {
        if (onWarn || onError) {
            const originalSource = inMap.sourcesContent[0];
            const offset = originalSource.indexOf(source);
            const lineOffset = originalSource.slice(0, offset).split(/\r?\n/).length - 1;
            if (onWarn) {
                options.onWarn = (err) => {
                    patchError(err, lineOffset, offset);
                    onWarn(err);
                };
            }
            if (onError) {
                options.onError = (err) => {
                    patchError(err, lineOffset, offset);
                    onError(err);
                };
            }
        }
    }
}
function patchError(err, lineOffset, offset) {
    if (err.loc) {
        err.loc.start.line += lineOffset;
        err.loc.start.offset += offset;
        if (err.loc.end !== err.loc.start) {
            err.loc.end.line += lineOffset;
            err.loc.end.offset += offset;
        }
    }
}
