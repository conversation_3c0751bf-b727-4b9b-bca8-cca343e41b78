uni-toast {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: block;
  box-sizing: border-box;
  pointer-events: none;
  font-size: 16px;
}

.uni-sample-toast {
  position: fixed;
  z-index: 999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  max-width: 80%;
}

.uni-simple-toast__text {
  display: inline-block;
  vertical-align: middle;
  color: #ffffff;
  background-color: rgba(17, 17, 17, 0.7);
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 13px;
  text-align: center;
  max-width: 100%;
  word-break: break-all;
  white-space: normal;
}

uni-toast .uni-mask {
  pointer-events: auto;
}

.uni-toast {
  position: fixed;
  z-index: 999;
  width: 8em;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(17, 17, 17, 0.7);
  text-align: center;
  border-radius: 5px;
  color: #ffffff;
}

.uni-toast * {
  box-sizing: border-box;
}

.uni-toast__icon {
  margin: 20px 0 0;
  width: 38px!important;
  height: 38px!important;
  vertical-align: baseline!important;
}

.uni-icon_toast {
  margin: 15px 0 0;
}

.uni-icon_toast.uni-icon-success-no-circle:before {
  color: #ffffff;
  font-size: 55px;
}

.uni-icon_toast.uni-loading {
  margin: 20px 0 0;
  width: 38px;
  height: 38px;
  vertical-align: baseline;
}

.uni-toast__content {
  margin: 0 0 15px;
}

@media (prefers-color-scheme: dark) {
  .uni-toast {
    background-color: #606060;
    color: var(--UI-FG-0);
  }
}