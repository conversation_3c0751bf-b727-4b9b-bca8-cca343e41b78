<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成PNG图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .icon-preview {
            width: 44px;
            height: 44px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }
        .normal { background: #999999; }
        .active { background: #1677ff; }
        .download-btn {
            background: #1677ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #0056d3;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; margin-top: 30px; }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序TabBar图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击下载按钮生成对应的PNG图标</li>
                <li>图标尺寸：44x44px（适合微信小程序）</li>
                <li>每个图标有两个状态：普通状态和选中状态</li>
                <li>下载后将图标放到 static/tabbar/ 目录下</li>
            </ol>
        </div>

        <div class="icon-grid">
            <div class="icon-item">
                <h3>首页图标</h3>
                <div class="icon-preview normal" id="home-normal"></div>
                <div class="icon-preview active" id="home-active"></div>
                <button class="download-btn" onclick="downloadIcon('home', 'normal')">下载普通状态</button>
                <button class="download-btn" onclick="downloadIcon('home', 'active')">下载选中状态</button>
            </div>

            <div class="icon-item">
                <h3>理财图标</h3>
                <div class="icon-preview normal" id="finance-normal"></div>
                <div class="icon-preview active" id="finance-active"></div>
                <button class="download-btn" onclick="downloadIcon('finance', 'normal')">下载普通状态</button>
                <button class="download-btn" onclick="downloadIcon('finance', 'active')">下载选中状态</button>
            </div>

            <div class="icon-item">
                <h3>基金图标</h3>
                <div class="icon-preview normal" id="fund-normal"></div>
                <div class="icon-preview active" id="fund-active"></div>
                <button class="download-btn" onclick="downloadIcon('fund', 'normal')">下载普通状态</button>
                <button class="download-btn" onclick="downloadIcon('fund', 'active')">下载选中状态</button>
            </div>

            <div class="icon-item">
                <h3>我的图标</h3>
                <div class="icon-preview normal" id="profile-normal"></div>
                <div class="icon-preview active" id="profile-active"></div>
                <button class="download-btn" onclick="downloadIcon('profile', 'normal')">下载普通状态</button>
                <button class="download-btn" onclick="downloadIcon('profile', 'active')">下载选中状态</button>
            </div>
        </div>

        <h2>其他页面图标</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <h3>搜索图标</h3>
                <div class="icon-preview normal" id="search-normal"></div>
                <button class="download-btn" onclick="downloadIcon('search', 'normal')">下载图标</button>
            </div>

            <div class="icon-item">
                <h3>消息图标</h3>
                <div class="icon-preview normal" id="message-normal"></div>
                <button class="download-btn" onclick="downloadIcon('message', 'normal')">下载图标</button>
            </div>

            <div class="icon-item">
                <h3>通知图标</h3>
                <div class="icon-preview normal" id="notification-normal"></div>
                <button class="download-btn" onclick="downloadIcon('notification', 'normal')">下载图标</button>
            </div>

            <div class="icon-item">
                <h3>添加图标</h3>
                <div class="icon-preview normal" id="add-normal"></div>
                <button class="download-btn" onclick="downloadIcon('add', 'normal')">下载图标</button>
            </div>
        </div>
    </div>

    <script>
        // 图标SVG定义
        const icons = {
            home: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 8L8 18V36H16V26H28V36H36V18L22 8Z" fill="#999999"/>
                </svg>`,
                active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 8L8 18V36H16V26H28V36H36V18L22 8Z" fill="#1677ff"/>
                </svg>`
            },
            finance: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="22" cy="22" r="16" stroke="#999999" stroke-width="2" fill="none"/>
                    <path d="M18 18H26V20H20V22H24C25.1 22 26 22.9 26 24V26C26 27.1 25.1 28 24 28H18V26H24V24H20C18.9 24 18 23.1 18 22V20C18 18.9 18.9 18 20 18H18Z" fill="#999999"/>
                </svg>`,
                active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="22" cy="22" r="16" stroke="#1677ff" stroke-width="2" fill="none"/>
                    <path d="M18 18H26V20H20V22H24C25.1 22 26 22.9 26 24V26C26 27.1 25.1 28 24 28H18V26H24V24H20C18.9 24 18 23.1 18 22V20C18 18.9 18.9 18 20 18H18Z" fill="#1677ff"/>
                </svg>`
            },
            fund: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 32L16 24L22 28L36 14L38 16L22 32L16 28L10 34L8 32Z" fill="#999999"/>
                    <rect x="6" y="6" width="32" height="32" rx="2" stroke="#999999" stroke-width="2" fill="none"/>
                </svg>`,
                active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 32L16 24L22 28L36 14L38 16L22 32L16 28L10 34L8 32Z" fill="#1677ff"/>
                    <rect x="6" y="6" width="32" height="32" rx="2" stroke="#1677ff" stroke-width="2" fill="none"/>
                </svg>`
            },
            profile: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="22" cy="16" r="6" fill="#999999"/>
                    <path d="M22 26C16 26 8 29 8 35V38H36V35C36 29 28 26 22 26Z" fill="#999999"/>
                </svg>`,
                active: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="22" cy="16" r="6" fill="#1677ff"/>
                    <path d="M22 26C16 26 8 29 8 35V38H36V35C36 29 28 26 22 26Z" fill="#1677ff"/>
                </svg>`
            },
            search: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="12" stroke="#999999" stroke-width="2" fill="none"/>
                    <path d="M30 30L36 36" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                </svg>`
            },
            message: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 8H36C37.1 8 38 8.9 38 10V28C38 29.1 37.1 30 36 30H14L8 36V10C8 8.9 8.9 8 10 8H8Z" fill="#999999"/>
                </svg>`
            },
            notification: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 4C19.8 4 18 5.8 18 8V18C18 22.4 14.4 26 10 26V28H34V26C29.6 26 26 22.4 26 18V8C26 5.8 24.2 4 22 4Z" fill="#999999"/>
                    <path d="M19 32C19 34.2 20.8 36 23 36C25.2 36 27 34.2 27 32H19Z" fill="#999999"/>
                </svg>`
            },
            add: {
                normal: `<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="22" cy="22" r="18" stroke="#999999" stroke-width="2" fill="none"/>
                    <path d="M22 14V30M14 22H30" stroke="#999999" stroke-width="2" stroke-linecap="round"/>
                </svg>`
            }
        };

        // 渲染图标预览
        function renderPreviews() {
            Object.keys(icons).forEach(iconName => {
                const iconData = icons[iconName];
                if (iconData.normal) {
                    document.getElementById(`${iconName}-normal`).innerHTML = iconData.normal;
                }
                if (iconData.active) {
                    document.getElementById(`${iconName}-active`).innerHTML = iconData.active;
                }
            });
        }

        // 下载图标
        function downloadIcon(iconName, state) {
            const svgContent = icons[iconName][state];
            if (!svgContent) return;

            // 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = 44;
            canvas.height = 44;
            const ctx = canvas.getContext('2d');

            // 创建图片
            const img = new Image();
            const svgBlob = new Blob([svgContent], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);

            img.onload = function() {
                ctx.drawImage(img, 0, 0);
                
                // 下载PNG
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `${iconName}${state === 'active' ? '-active' : ''}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(link.href);
                });
                
                URL.revokeObjectURL(url);
            };

            img.src = url;
        }

        // 页面加载完成后渲染预览
        document.addEventListener('DOMContentLoaded', renderPreviews);
    </script>
</body>
</html>
