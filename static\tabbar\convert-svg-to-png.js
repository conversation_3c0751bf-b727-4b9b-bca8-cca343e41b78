const fs = require('fs');
const path = require('path');

// 使用sharp库进行SVG到PNG的转换
// 如果没有安装sharp，请运行: npm install sharp
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.error('请先安装sharp库: npm install sharp');
  process.exit(1);
}

// 获取当前目录下的所有SVG文件
const currentDir = __dirname;
const svgFiles = fs.readdirSync(currentDir).filter(file => file.endsWith('.svg'));

console.log(`找到 ${svgFiles.length} 个SVG文件:`);
svgFiles.forEach(file => console.log(`  - ${file}`));

// 转换函数
async function convertSvgToPng(svgFile) {
  const svgPath = path.join(currentDir, svgFile);
  const pngFile = svgFile.replace('.svg', '.png');
  const pngPath = path.join(currentDir, pngFile);
  
  try {
    await sharp(svgPath)
      .resize(88, 88) // 高清尺寸
      .png({
        quality: 100,
        compressionLevel: 0,
        adaptiveFiltering: false
      })
      .toFile(pngPath);
    
    console.log(`✅ ${svgFile} -> ${pngFile}`);
    return true;
  } catch (error) {
    console.error(`❌ 转换失败 ${svgFile}:`, error.message);
    return false;
  }
}

// 批量转换
async function convertAllSvgToPng() {
  console.log('\n开始转换SVG到PNG...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const svgFile of svgFiles) {
    const success = await convertSvgToPng(svgFile);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log(`\n转换完成!`);
  console.log(`✅ 成功: ${successCount} 个文件`);
  console.log(`❌ 失败: ${failCount} 个文件`);
  
  if (successCount > 0) {
    console.log('\n生成的PNG文件:');
    const pngFiles = fs.readdirSync(currentDir).filter(file => file.endsWith('.png'));
    pngFiles.forEach(file => {
      const stats = fs.statSync(path.join(currentDir, file));
      console.log(`  - ${file} (${Math.round(stats.size / 1024)}KB)`);
    });
  }
}

// 执行转换
convertAllSvgToPng().catch(console.error);
