<template>
  <view class="points-container">
    <!-- 积分总览 -->
    <view class="points-overview">
      <view class="points-card">
        <view class="points-header">
          <text class="points-title">我的积分</text>
          <view class="points-actions">
            <text class="action-btn" @click="goToRecord">明细</text>
            <text class="action-btn" @click="goToRules">规则</text>
          </view>
        </view>
        <view class="points-main">
          <text class="points-value">{{ userPoints.total }}</text>
          <text class="points-desc">可用积分</text>
        </view>
        <view class="points-stats">
          <view class="stat-item">
            <text class="stat-value">{{ userPoints.earned }}</text>
            <text class="stat-label">累计获得</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userPoints.used }}</text>
            <text class="stat-label">累计使用</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userPoints.expiring }}</text>
            <text class="stat-label">即将过期</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <view class="action-grid">
        <view class="action-item" v-for="action in quickActions" :key="action.key" @click="handleQuickAction(action)">
          <view class="action-icon">{{ action.icon }}</view>
          <text class="action-name">{{ action.name }}</text>
          <text class="action-desc">{{ action.desc }}</text>
        </view>
      </view>
    </view>

    <!-- 积分商城 -->
    <view class="points-mall">
      <view class="section-header" @click="goToMall">
        <view class="section-title">
          <text class="title-icon">🛍️</text>
          <text class="title-text">积分商城</text>
        </view>
        <text class="more-text">更多好礼 ></text>
      </view>
      <scroll-view class="mall-products" scroll-x>
        <view class="product-list">
          <view 
            class="product-item" 
            v-for="product in mallProducts" 
            :key="product.id"
            @click="goToProductDetail(product)"
          >
            <image :src="product.image" class="product-image" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <view class="product-price">
                <text class="points-price">{{ product.points }}积分</text>
                <text class="original-price">¥{{ product.price }}</text>
              </view>
              <text class="product-stock">库存{{ product.stock }}件</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 积分任务 -->
    <view class="points-tasks">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">📋</text>
          <text class="title-text">积分任务</text>
        </view>
      </view>
      <view class="task-list">
        <view 
          class="task-item" 
          v-for="task in pointsTasks" 
          :key="task.id"
          @click="handleTask(task)"
        >
          <view class="task-icon">{{ task.icon }}</view>
          <view class="task-info">
            <text class="task-name">{{ task.name }}</text>
            <text class="task-desc">{{ task.desc }}</text>
          </view>
          <view class="task-reward">
            <text class="reward-points">+{{ task.points }}</text>
            <text class="reward-label">积分</text>
          </view>
          <view class="task-action">
            <view class="action-btn" :class="{ completed: task.completed, disabled: !task.available }">
              <text class="btn-text">{{ getTaskButtonText(task) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="recent-records">
      <view class="section-header" @click="goToRecord">
        <view class="section-title">
          <text class="title-icon">📊</text>
          <text class="title-text">最近记录</text>
        </view>
        <text class="more-text">查看全部 ></text>
      </view>
      <view class="record-list">
        <view class="record-item" v-for="record in recentRecords" :key="record.id">
          <view class="record-info">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ formatTime(record.createTime) }}</text>
          </view>
          <view class="record-points" :class="{ positive: record.points > 0, negative: record.points < 0 }">
            <text class="points-text">{{ record.points > 0 ? '+' : '' }}{{ record.points }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface UserPoints {
  total: number
  earned: number
  used: number
  expiring: number
}

interface QuickAction {
  key: string
  name: string
  desc: string
  icon: string
}

interface MallProduct {
  id: number
  name: string
  image: string
  points: number
  price: number
  stock: number
}

interface PointsTask {
  id: number
  name: string
  desc: string
  icon: string
  points: number
  completed: boolean
  available: boolean
}

interface PointsRecord {
  id: number
  title: string
  points: number
  createTime: string
}

// 响应式数据
const userPoints = ref<UserPoints>({
  total: 1580,
  earned: 2350,
  used: 770,
  expiring: 120
})

const quickActions = ref<QuickAction[]>([
  { key: 'checkin', name: '每日签到', desc: '+10积分', icon: '📅' },
  { key: 'share', name: '分享商品', desc: '+5积分', icon: '📤' },
  { key: 'review', name: '商品评价', desc: '+20积分', icon: '⭐' },
  { key: 'invite', name: '邀请好友', desc: '+100积分', icon: '👥' }
])

const mallProducts = ref<MallProduct[]>([
  {
    id: 1,
    name: '精美水杯',
    image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=水杯',
    points: 500,
    price: 29.9,
    stock: 128
  },
  {
    id: 2,
    name: '蓝牙耳机',
    image: 'https://via.placeholder.com/200x200/1890ff/ffffff?text=耳机',
    points: 1200,
    price: 199.9,
    stock: 89
  },
  {
    id: 3,
    name: '手机支架',
    image: 'https://via.placeholder.com/200x200/52c41a/ffffff?text=支架',
    points: 300,
    price: 19.9,
    stock: 256
  }
])

const pointsTasks = ref<PointsTask[]>([
  {
    id: 1,
    name: '每日签到',
    desc: '连续签到获得更多积分',
    icon: '📅',
    points: 10,
    completed: false,
    available: true
  },
  {
    id: 2,
    name: '完善个人资料',
    desc: '完善头像、昵称等信息',
    icon: '👤',
    points: 50,
    completed: true,
    available: true
  },
  {
    id: 3,
    name: '首次购物',
    desc: '完成首次购物订单',
    icon: '🛒',
    points: 100,
    completed: false,
    available: true
  },
  {
    id: 4,
    name: '发布帖子',
    desc: '在论坛发布优质内容',
    icon: '📝',
    points: 30,
    completed: false,
    available: true
  }
])

const recentRecords = ref<PointsRecord[]>([
  {
    id: 1,
    title: '购买商品获得积分',
    points: 30,
    createTime: '2024-01-03 14:30:00'
  },
  {
    id: 2,
    title: '积分兑换商品',
    points: -500,
    createTime: '2024-01-03 10:15:00'
  },
  {
    id: 3,
    title: '每日签到',
    points: 10,
    createTime: '2024-01-03 09:00:00'
  },
  {
    id: 4,
    title: '分享商品',
    points: 5,
    createTime: '2024-01-02 16:45:00'
  }
])

// 方法
const goToRecord = () => {
  uni.navigateTo({
    url: '/pages/points/record'
  })
}

const goToRules = () => {
  uni.navigateTo({
    url: '/pages/points/rules'
  })
}

const goToMall = () => {
  uni.navigateTo({
    url: '/pages/points/mall'
  })
}

const goToProductDetail = (product: MallProduct) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}&type=points`
  })
}

const handleQuickAction = (action: QuickAction) => {
  switch (action.key) {
    case 'checkin':
      handleCheckin()
      break
    case 'share':
      handleShare()
      break
    case 'review':
      goToReview()
      break
    case 'invite':
      goToInvite()
      break
  }
}

const handleTask = (task: PointsTask) => {
  if (task.completed || !task.available) return
  
  switch (task.id) {
    case 1: // 每日签到
      handleCheckin()
      break
    case 3: // 首次购物
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
      break
    case 4: // 发布帖子
      uni.navigateTo({
        url: '/pages/forum/publish'
      })
      break
  }
}

const handleCheckin = () => {
  uni.showToast({
    title: '签到成功，获得10积分',
    icon: 'success'
  })
  userPoints.value.total += 10
  userPoints.value.earned += 10
  
  // 更新签到任务状态
  const checkinTask = pointsTasks.value.find(task => task.id === 1)
  if (checkinTask) {
    checkinTask.completed = true
  }
}

const handleShare = () => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: () => {
      uni.showToast({
        title: '分享成功，获得5积分',
        icon: 'success'
      })
      userPoints.value.total += 5
      userPoints.value.earned += 5
    }
  })
}

const goToReview = () => {
  uni.navigateTo({
    url: '/pages/order/list?tab=review'
  })
}

const goToInvite = () => {
  uni.navigateTo({
    url: '/pages/profile/invite'
  })
}

const getTaskButtonText = (task: PointsTask) => {
  if (task.completed) return '已完成'
  if (!task.available) return '不可用'
  return '去完成'
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return time.toLocaleDateString()
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.points-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.points-overview {
  padding: 30rpx;

  .points-card {
    background: linear-gradient(135deg, #ff6b35, #ff8f5a);
    border-radius: 20rpx;
    padding: 40rpx;
    color: #ffffff;

    .points-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .points-title {
        font-size: 32rpx;
        font-weight: 600;
      }

      .points-actions {
        display: flex;
        gap: 20rpx;

        .action-btn {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.9);
          padding: 8rpx 16rpx;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 16rpx;
        }
      }
    }

    .points-main {
      text-align: center;
      margin-bottom: 40rpx;

      .points-value {
        display: block;
        font-size: 80rpx;
        font-weight: 700;
        margin-bottom: 8rpx;
      }

      .points-desc {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .points-stats {
      display: flex;
      justify-content: space-around;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stat-value {
          font-size: 32rpx;
          font-weight: 600;
          margin-bottom: 8rpx;
        }

        .stat-label {
          font-size: 22rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

.quick-actions {
  padding: 0 30rpx 30rpx;

  .action-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30rpx 20rpx;
      background-color: #ffffff;
      border-radius: 16rpx;

      .action-icon {
        font-size: 48rpx;
        margin-bottom: 12rpx;
      }

      .action-name {
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 4rpx;
      }

      .action-desc {
        font-size: 20rpx;
        color: #52c41a;
      }
    }
  }
}

.points-mall, .points-tasks, .recent-records {
  margin-bottom: 30rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx 20rpx;

    .section-title {
      display: flex;
      align-items: center;

      .title-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }
    }

    .more-text {
      font-size: 26rpx;
      color: #999999;
    }
  }
}

.points-mall {
  .mall-products {
    padding-left: 30rpx;

    .product-list {
      display: flex;
      gap: 20rpx;
      padding-right: 30rpx;

      .product-item {
        flex-shrink: 0;
        width: 280rpx;
        background-color: #ffffff;
        border-radius: 16rpx;
        overflow: hidden;

        .product-image {
          width: 100%;
          height: 200rpx;
        }

        .product-info {
          padding: 20rpx;

          .product-name {
            display: block;
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 12rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .product-price {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;

            .points-price {
              font-size: 28rpx;
              font-weight: 600;
              color: #ff6b35;
              margin-right: 12rpx;
            }

            .original-price {
              font-size: 22rpx;
              color: #999999;
              text-decoration: line-through;
            }
          }

          .product-stock {
            font-size: 20rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

.points-tasks {
  .task-list {
    background-color: #ffffff;
    margin: 0 30rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .task-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .task-icon {
        font-size: 48rpx;
        margin-right: 20rpx;
      }

      .task-info {
        flex: 1;

        .task-name {
          display: block;
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .task-desc {
          font-size: 24rpx;
          color: #999999;
        }
      }

      .task-reward {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 20rpx;

        .reward-points {
          font-size: 28rpx;
          font-weight: 600;
          color: #52c41a;
        }

        .reward-label {
          font-size: 20rpx;
          color: #999999;
        }
      }

      .task-action {
        .action-btn {
          padding: 12rpx 24rpx;
          background-color: #ff6b35;
          border-radius: 20rpx;

          &.completed {
            background-color: #f0f0f0;
          }

          &.disabled {
            background-color: #f0f0f0;
            opacity: 0.5;
          }

          .btn-text {
            font-size: 24rpx;
            color: #ffffff;

            .completed &,
            .disabled & {
              color: #999999;
            }
          }
        }
      }
    }
  }
}

.recent-records {
  .record-list {
    background-color: #ffffff;
    margin: 0 30rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .record-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .record-info {
        flex: 1;

        .record-title {
          display: block;
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .record-time {
          font-size: 24rpx;
          color: #999999;
        }
      }

      .record-points {
        .points-text {
          font-size: 32rpx;
          font-weight: 600;
        }

        &.positive .points-text {
          color: #52c41a;
        }

        &.negative .points-text {
          color: #f5222d;
        }
      }
    }
  }
}
</style>
