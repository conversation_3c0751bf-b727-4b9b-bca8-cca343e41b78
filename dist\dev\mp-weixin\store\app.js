"use strict";const e=require("../common/vendor.js"),t=e.defineStore("app",()=>{const t=e.ref(null),n=e.ref("unknown"),r=e.ref(!1),o=e.ref(!0),a=e.ref(!1),i=()=>{var e;return(null==(e=t.value)?void 0:e.statusBarHeight)||0};return{systemInfo:t,networkType:n,isLoading:r,isOnline:o,isDarkMode:a,setSystemInfo:e=>{t.value=e},setNetworkType:e=>{n.value=e,o.value="none"!==e},setLoading:e=>{r.value=e},toggleDarkMode:()=>{a.value=!a.value,e.index.setStorageSync("darkMode",a.value)},initDarkMode:()=>{const t=e.index.getStorageSync("darkMode");void 0!==t&&(a.value=t)},getStatusBarHeight:i,getNavBarHeight:()=>{const t=e.index.getMenuButtonBoundingClientRect();return t.height+2*(t.top-i())},getSafeArea:()=>{var e;return(null==(e=t.value)?void 0:e.safeArea)||{left:0,right:0,top:0,bottom:0,width:0,height:0}},isIPhoneX:()=>{if(!t.value)return!1;const{model:e,screenHeight:n,screenWidth:r}=t.value;return/iPhone X/.test(e)||812===n&&375===r||896===n&&414===r}}});exports.useAppStore=t;
