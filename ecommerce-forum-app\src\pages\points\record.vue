<template>
  <view class="points-record-container">
    <!-- 积分统计 -->
    <view class="points-summary">
      <view class="summary-item">
        <text class="summary-value">{{ totalEarned }}</text>
        <text class="summary-label">累计获得</text>
      </view>
      <view class="summary-divider"></view>
      <view class="summary-item">
        <text class="summary-value">{{ totalUsed }}</text>
        <text class="summary-label">累计消耗</text>
      </view>
      <view class="summary-divider"></view>
      <view class="summary-item">
        <text class="summary-value">{{ currentPoints }}</text>
        <text class="summary-label">当前余额</text>
      </view>
    </view>

    <!-- 记录筛选 -->
    <view class="record-filter">
      <view 
        class="filter-item"
        :class="{ active: activeFilter === filter.key }"
        v-for="filter in filterOptions"
        :key="filter.key"
        @click="switchFilter(filter.key)"
      >
        <text class="filter-text">{{ filter.name }}</text>
      </view>
    </view>

    <!-- 记录列表 -->
    <scroll-view 
      class="record-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="refreshRecords"
      :refresher-triggered="isRefreshing"
    >
      <view class="record-list">
        <!-- 按日期分组 -->
        <view 
          class="date-group"
          v-for="group in groupedRecords"
          :key="group.date"
        >
          <view class="date-header">
            <text class="date-text">{{ group.date }}</text>
          </view>
          
          <view class="record-items">
            <view 
              class="record-item"
              v-for="record in group.records"
              :key="record.id"
            >
              <view class="record-icon">
                <text class="icon">{{ getRecordIcon(record.type) }}</text>
              </view>
              
              <view class="record-info">
                <text class="record-title">{{ record.title }}</text>
                <text class="record-desc" v-if="record.description">{{ record.description }}</text>
                <text class="record-time">{{ formatTime(record.createTime) }}</text>
              </view>
              
              <view class="record-points">
                <text class="points-change" :class="{ earn: record.change > 0, cost: record.change < 0 }">
                  {{ record.change > 0 ? '+' : '' }}{{ record.change }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载更多...</text>
      </view>

      <!-- 无记录 -->
      <view class="no-records" v-if="filteredRecords.length === 0 && !isLoading">
        <text class="no-records-icon">📊</text>
        <text class="no-records-text">暂无积分记录</text>
        <text class="no-records-tip">快去完成任务赚取积分吧</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface PointsRecord {
  id: number
  type: 'earn' | 'cost' | 'expire'
  title: string
  description?: string
  change: number
  createTime: string
  source: string
}

interface DateGroup {
  date: string
  records: PointsRecord[]
}

// 响应式数据
const activeFilter = ref('all')
const isRefreshing = ref(false)
const isLoading = ref(false)
const hasMore = ref(true)
const currentPoints = ref(2580)
const totalEarned = ref(8650)
const totalUsed = ref(6070)

const filterOptions = ref([
  { key: 'all', name: '全部' },
  { key: 'earn', name: '获得' },
  { key: 'cost', name: '消耗' },
  { key: 'expire', name: '过期' }
])

const records = ref<PointsRecord[]>([
  {
    id: 1,
    type: 'earn',
    title: '购物返积分',
    description: '订单号：EC202401010001',
    change: 50,
    createTime: '2024-01-15 14:30:00',
    source: 'order'
  },
  {
    id: 2,
    type: 'cost',
    title: '积分兑换',
    description: '兑换商品：蓝牙耳机',
    change: -1500,
    createTime: '2024-01-15 10:20:00',
    source: 'exchange'
  },
  {
    id: 3,
    type: 'earn',
    title: '每日签到',
    description: '连续签到第7天',
    change: 20,
    createTime: '2024-01-15 09:00:00',
    source: 'checkin'
  },
  {
    id: 4,
    type: 'earn',
    title: '发布帖子',
    description: '发布优质内容获得奖励',
    change: 30,
    createTime: '2024-01-14 16:45:00',
    source: 'post'
  },
  {
    id: 5,
    type: 'cost',
    title: '查看付费内容',
    description: '查看帖子：《投资理财心得分享》',
    change: -10,
    createTime: '2024-01-14 15:20:00',
    source: 'view'
  },
  {
    id: 6,
    type: 'earn',
    title: '评论获赞',
    description: '评论获得10个赞',
    change: 15,
    createTime: '2024-01-14 11:30:00',
    source: 'like'
  },
  {
    id: 7,
    type: 'expire',
    title: '积分过期',
    description: '180天未使用自动过期',
    change: -100,
    createTime: '2024-01-13 00:00:00',
    source: 'expire'
  },
  {
    id: 8,
    type: 'earn',
    title: '新用户奖励',
    description: '完成注册获得新手奖励',
    change: 100,
    createTime: '2024-01-12 18:00:00',
    source: 'register'
  }
])

// 计算属性
const filteredRecords = computed(() => {
  if (activeFilter.value === 'all') {
    return records.value
  }
  return records.value.filter(record => record.type === activeFilter.value)
})

const groupedRecords = computed(() => {
  const groups: Record<string, PointsRecord[]> = {}
  
  filteredRecords.value.forEach(record => {
    const date = formatDate(record.createTime)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(record)
  })
  
  return Object.keys(groups)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    .map(date => ({
      date: formatDateLabel(date),
      records: groups[date].sort((a, b) => 
        new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
      )
    }))
})

// 方法
const switchFilter = (filterKey: string) => {
  activeFilter.value = filterKey
}

const refreshRecords = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1000)
}

const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
}

const getRecordIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    earn: '💰',
    cost: '💸',
    expire: '⏰'
  }
  return iconMap[type] || '📊'
}

const formatDate = (timeStr: string) => {
  return timeStr.split(' ')[0]
}

const formatDateLabel = (dateStr: string) => {
  const today = new Date()
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const date = new Date(dateStr)
  
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return dateStr.replace(/-/g, '.')
  }
}

const formatTime = (timeStr: string) => {
  return timeStr.split(' ')[1].replace(/:\d{2}$/, '')
}

onLoad((options) => {
  if (options?.type) {
    activeFilter.value = options.type
  }
})
</script>

<style lang="scss" scoped>
.points-record-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.points-summary {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 40rpx 0;
  margin-bottom: 20rpx;
  
  .summary-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .summary-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff6b35;
      margin-bottom: 8rpx;
    }
    
    .summary-label {
      font-size: 22rpx;
      color: #666666;
    }
  }
  
  .summary-divider {
    width: 1rpx;
    height: 60rpx;
    background-color: #f0f0f0;
  }
}

.record-filter {
  display: flex;
  background-color: #ffffff;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  
  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
    position: relative;
    
    &.active {
      border-bottom: 4rpx solid #ff6b35;
      
      .filter-text {
        color: #ff6b35;
        font-weight: 600;
      }
    }
    
    .filter-text {
      font-size: 26rpx;
      color: #666666;
    }
  }
}

.record-scroll {
  height: calc(100vh - 280rpx);
}

.record-list {
  padding: 0 20rpx;
  
  .date-group {
    margin-bottom: 30rpx;
    
    .date-header {
      padding: 20rpx 0;
      
      .date-text {
        font-size: 24rpx;
        color: #999999;
      }
    }
    
    .record-items {
      background-color: #ffffff;
      border-radius: 12rpx;
      overflow: hidden;
      
      .record-item {
        display: flex;
        align-items: center;
        padding: 30rpx;
        border-bottom: 1rpx solid #f8f8f8;
        
        &:last-child {
          border-bottom: none;
        }
        
        .record-icon {
          width: 80rpx;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f8f8f8;
          border-radius: 50%;
          margin-right: 20rpx;
          
          .icon {
            font-size: 32rpx;
          }
        }
        
        .record-info {
          flex: 1;
          
          .record-title {
            display: block;
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 8rpx;
          }
          
          .record-desc {
            display: block;
            font-size: 22rpx;
            color: #666666;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .record-time {
            font-size: 20rpx;
            color: #999999;
          }
        }
        
        .record-points {
          .points-change {
            font-size: 28rpx;
            font-weight: 600;
            
            &.earn {
              color: #52c41a;
            }
            
            &.cost {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .load-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.no-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  
  .no-records-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }
  
  .no-records-text {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }
  
  .no-records-tip {
    font-size: 24rpx;
    color: #999999;
  }
}
</style>
