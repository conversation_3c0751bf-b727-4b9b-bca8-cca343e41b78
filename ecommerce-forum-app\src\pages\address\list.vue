<template>
  <view class="address-container">
    <!-- 地址列表 -->
    <view class="address-list">
      <view 
        class="address-item"
        v-for="address in addresses"
        :key="address.id"
        @click="selectAddress(address)"
      >
        <!-- 默认地址标签 -->
        <view class="default-badge" v-if="address.isDefault">
          <text class="badge-text">默认</text>
        </view>

        <!-- 地址信息 -->
        <view class="address-info">
          <view class="contact-info">
            <text class="contact-name">{{ address.name }}</text>
            <text class="contact-phone">{{ address.phone }}</text>
          </view>
          
          <view class="address-detail">
            <text class="address-text">
              {{ address.province }}{{ address.city }}{{ address.district }}{{ address.detail }}
            </text>
          </view>
          
          <view class="address-tags" v-if="address.tag">
            <text class="tag">{{ address.tag }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="address-actions">
          <view class="action-btn" @click.stop="editAddress(address)">
            <text class="btn-text">编辑</text>
          </view>
          <view class="action-btn delete" @click.stop="deleteAddress(address)">
            <text class="btn-text">删除</text>
          </view>
        </view>

        <!-- 选择状态 -->
        <view class="select-status" v-if="isSelectMode">
          <text class="select-icon">{{ selectedAddressId === address.id ? '☑️' : '☐' }}</text>
        </view>
      </view>
    </view>

    <!-- 无地址状态 -->
    <view class="no-address" v-if="addresses.length === 0">
      <text class="no-address-icon">📍</text>
      <text class="no-address-text">暂无收货地址</text>
      <text class="no-address-tip">添加地址后可享受更便捷的购物体验</text>
    </view>

    <!-- 添加地址按钮 -->
    <view class="add-address-btn" @click="addAddress">
      <text class="btn-text">+ 添加新地址</text>
    </view>

    <!-- 确认选择按钮 -->
    <view class="confirm-btn" v-if="isSelectMode && selectedAddressId" @click="confirmSelect">
      <text class="btn-text">确认选择</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onLoad } from 'vue'

interface Address {
  id: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  tag?: string
  isDefault: boolean
}

// 响应式数据
const isSelectMode = ref(false)
const selectedAddressId = ref<number | null>(null)

const addresses = ref<Address[]>([
  {
    id: 1,
    name: '张三',
    phone: '138****8888',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    detail: '三里屯街道工体北路8号院1号楼101室',
    tag: '家',
    isDefault: true
  },
  {
    id: 2,
    name: '李四',
    phone: '139****9999',
    province: '上海市',
    city: '上海市',
    district: '浦东新区',
    detail: '陆家嘴环路1000号恒生银行大厦50楼',
    tag: '公司',
    isDefault: false
  },
  {
    id: 3,
    name: '王五',
    phone: '136****6666',
    province: '广东省',
    city: '深圳市',
    district: '南山区',
    detail: '科技园南区深南大道9988号',
    isDefault: false
  }
])

// 方法
const selectAddress = (address: Address) => {
  if (isSelectMode.value) {
    selectedAddressId.value = address.id
  }
}

const editAddress = (address: Address) => {
  uni.navigateTo({
    url: `/pages/address/edit?id=${address.id}`
  })
}

const deleteAddress = (address: Address) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个地址吗？',
    success: (res) => {
      if (res.confirm) {
        const index = addresses.value.findIndex(item => item.id === address.id)
        if (index > -1) {
          addresses.value.splice(index, 1)
          
          // 如果删除的是默认地址，设置第一个为默认
          if (address.isDefault && addresses.value.length > 0) {
            addresses.value[0].isDefault = true
          }
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    }
  })
}

const addAddress = () => {
  uni.navigateTo({
    url: '/pages/address/edit'
  })
}

const confirmSelect = () => {
  if (!selectedAddressId.value) return
  
  const selectedAddress = addresses.value.find(item => item.id === selectedAddressId.value)
  if (selectedAddress) {
    // 返回选中的地址
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    
    if (prevPage) {
      // 通过事件总线或者页面参数传递选中的地址
      prevPage.$vm.selectedAddress = selectedAddress
    }
    
    uni.navigateBack()
  }
}

onLoad((options) => {
  // 检查是否是选择模式
  if (options?.mode === 'select') {
    isSelectMode.value = true
    
    // 如果有默认地址，预选中
    const defaultAddress = addresses.value.find(item => item.isDefault)
    if (defaultAddress) {
      selectedAddressId.value = defaultAddress.id
    }
  }
})
</script>

<style lang="scss" scoped>
.address-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.address-list {
  padding: 20rpx;
  
  .address-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    position: relative;
    
    .default-badge {
      position: absolute;
      top: 0;
      right: 0;
      background-color: #ff6b35;
      border-radius: 0 12rpx 0 12rpx;
      padding: 8rpx 16rpx;
      
      .badge-text {
        font-size: 20rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
    
    .address-info {
      margin-bottom: 20rpx;
      
      .contact-info {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .contact-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 20rpx;
        }
        
        .contact-phone {
          font-size: 26rpx;
          color: #666666;
        }
      }
      
      .address-detail {
        margin-bottom: 16rpx;
        
        .address-text {
          font-size: 26rpx;
          color: #333333;
          line-height: 1.5;
        }
      }
      
      .address-tags {
        .tag {
          display: inline-block;
          font-size: 20rpx;
          color: #ff6b35;
          background-color: #fff7f0;
          border: 1rpx solid #ff6b35;
          border-radius: 8rpx;
          padding: 4rpx 12rpx;
        }
      }
    }
    
    .address-actions {
      display: flex;
      gap: 20rpx;
      
      .action-btn {
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        border: 1rpx solid #d9d9d9;
        background-color: #ffffff;
        
        &.delete {
          border-color: #ff4d4f;
          
          .btn-text {
            color: #ff4d4f;
          }
        }
        
        .btn-text {
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
    
    .select-status {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
      
      .select-icon {
        font-size: 32rpx;
      }
    }
  }
}

.no-address {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  
  .no-address-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }
  
  .no-address-text {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }
  
  .no-address-tip {
    font-size: 24rpx;
    color: #999999;
    text-align: center;
    line-height: 1.5;
  }
}

.add-address-btn {
  position: fixed;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  height: 80rpx;
  background-color: #ff6b35;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .btn-text {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: 600;
  }
}

.confirm-btn {
  position: fixed;
  bottom: 120rpx;
  left: 20rpx;
  right: 20rpx;
  height: 80rpx;
  background-color: #52c41a;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .btn-text {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: 600;
  }
}
</style>
