<template>
  <view class="forum-detail">
    <!-- 帖子内容 -->
    <view class="post-content">
      <!-- 帖子头部 -->
      <view class="post-header">
        <view class="author-info">
          <image :src="post.avatar" class="author-avatar" mode="aspectFill" />
          <view class="author-details">
            <text class="author-name">{{ post.author }}</text>
            <text class="post-time">{{ formatTime(post.createTime) }}</text>
          </view>
        </view>
        <view class="post-category">
          <text class="category-text">{{ post.category }}</text>
        </view>
      </view>

      <!-- 帖子标题 -->
      <text class="post-title">{{ post.title }}</text>

      <!-- 帖子正文 -->
      <view class="post-body">
        <text class="post-text">{{ post.content }}</text>
        
        <!-- 帖子图片 -->
        <view class="post-images" v-if="post.images && post.images.length > 0">
          <image 
            v-for="(image, index) in post.images" 
            :key="index"
            :src="image" 
            class="post-image" 
            mode="aspectFill"
            @click="previewImage(index)"
          />
        </view>
      </view>

      <!-- 帖子标签 -->
      <view class="post-tags" v-if="post.tags && post.tags.length > 0">
        <text class="tag-item" v-for="tag in post.tags" :key="tag"># {{ tag }}</text>
      </view>

      <!-- 积分查看提示 -->
      <view class="points-tip" v-if="post.pointsRequired > 0 && !hasViewed">
        <text class="tip-icon">💎</text>
        <text class="tip-text">查看完整内容需要{{ post.pointsRequired }}积分</text>
        <view class="unlock-btn" @click="unlockContent">
          <text class="btn-text">解锁内容</text>
        </view>
      </view>

      <!-- 帖子统计 -->
      <view class="post-stats">
        <text class="stat-item">👁️ {{ post.views }}</text>
        <text class="stat-item">👍 {{ post.likes }}</text>
        <text class="stat-item">💬 {{ post.comments }}</text>
        <text class="stat-item">⭐ {{ post.favorites }}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="post-actions">
        <view class="action-btn" :class="{ active: isLiked }" @click="toggleLike">
          <text class="action-icon">{{ isLiked ? '👍' : '👍🏻' }}</text>
          <text class="action-text">{{ isLiked ? '已赞' : '点赞' }}</text>
        </view>
        <view class="action-btn" :class="{ active: isFavorited }" @click="toggleFavorite">
          <text class="action-icon">{{ isFavorited ? '⭐' : '☆' }}</text>
          <text class="action-text">{{ isFavorited ? '已收藏' : '收藏' }}</text>
        </view>
        <view class="action-btn" @click="sharePost">
          <text class="action-icon">📤</text>
          <text class="action-text">分享</text>
        </view>
        <view class="action-btn" @click="rewardAuthor">
          <text class="action-icon">🎁</text>
          <text class="action-text">打赏</text>
        </view>
      </view>
    </view>

    <!-- 评论区 -->
    <view class="comments-section">
      <view class="comments-header">
        <text class="comments-title">评论 ({{ comments.length }})</text>
        <view class="sort-options">
          <text 
            class="sort-item"
            :class="{ active: commentSort === sort.key }"
            v-for="sort in commentSorts"
            :key="sort.key"
            @click="changeCommentSort(sort.key)"
          >
            {{ sort.name }}
          </text>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comments-list">
        <view class="comment-item" v-for="comment in sortedComments" :key="comment.id">
          <image :src="comment.avatar" class="comment-avatar" mode="aspectFill" />
          <view class="comment-content">
            <view class="comment-header">
              <text class="comment-author">{{ comment.author }}</text>
              <text class="comment-time">{{ formatTime(comment.createTime) }}</text>
            </view>
            <text class="comment-text">{{ comment.content }}</text>
            
            <!-- 评论图片 -->
            <view class="comment-images" v-if="comment.images && comment.images.length > 0">
              <image 
                v-for="(image, index) in comment.images" 
                :key="index"
                :src="image" 
                class="comment-image" 
                mode="aspectFill"
              />
            </view>
            
            <view class="comment-actions">
              <text class="comment-like" :class="{ active: comment.isLiked }" @click="toggleCommentLike(comment)">
                👍 {{ comment.likes }}
              </text>
              <text class="comment-reply" @click="replyComment(comment)">回复</text>
            </view>

            <!-- 回复列表 -->
            <view class="replies-list" v-if="comment.replies && comment.replies.length > 0">
              <view class="reply-item" v-for="reply in comment.replies" :key="reply.id">
                <text class="reply-author">{{ reply.author }}</text>
                <text class="reply-text">{{ reply.content }}</text>
                <text class="reply-time">{{ formatTime(reply.createTime) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多评论 -->
      <view class="load-more-comments" v-if="hasMoreComments" @click="loadMoreComments">
        <text class="load-text">加载更多评论</text>
      </view>
    </view>

    <!-- 底部评论输入 -->
    <view class="comment-input-bar">
      <input 
        type="text" 
        v-model="commentText" 
        placeholder="写下你的评论..."
        class="comment-input"
        @focus="showCommentModal = true"
      />
      <view class="input-actions">
        <text class="emoji-btn" @click="showEmojiPanel">😊</text>
        <text class="image-btn" @click="chooseCommentImage">📷</text>
      </view>
    </view>

    <!-- 评论弹窗 -->
    <view class="comment-modal" v-if="showCommentModal" @click="showCommentModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">发表评论</text>
          <text class="close-btn" @click="showCommentModal = false">✕</text>
        </view>
        <textarea 
          v-model="commentText" 
          placeholder="写下你的评论..."
          class="comment-textarea"
          auto-height
        />
        <view class="comment-images-preview" v-if="commentImages.length > 0">
          <view class="image-item" v-for="(image, index) in commentImages" :key="index">
            <image :src="image" class="preview-image" mode="aspectFill" />
            <text class="remove-image" @click="removeCommentImage(index)">✕</text>
          </view>
        </view>
        <view class="modal-footer">
          <text class="add-image-btn" @click="chooseCommentImage">添加图片</text>
          <view class="submit-btn" :class="{ disabled: !canSubmitComment }" @click="submitComment">
            <text class="btn-text">发表</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface Post {
  id: number
  title: string
  content: string
  author: string
  avatar: string
  category: string
  createTime: string
  views: number
  likes: number
  comments: number
  favorites: number
  pointsRequired: number
  tags?: string[]
  images?: string[]
}

interface Comment {
  id: number
  author: string
  avatar: string
  content: string
  createTime: string
  likes: number
  isLiked: boolean
  images?: string[]
  replies?: Reply[]
}

interface Reply {
  id: number
  author: string
  content: string
  createTime: string
}

// 响应式数据
const isLiked = ref(false)
const isFavorited = ref(false)
const hasViewed = ref(false)
const commentSort = ref('time')
const showCommentModal = ref(false)
const commentText = ref('')
const commentImages = ref<string[]>([])
const hasMoreComments = ref(true)

const post = ref<Post>({
  id: 1,
  title: '智能手表使用心得分享 - 一个月体验总结',
  content: '最近入手了这款智能手表，使用了一个月后想和大家分享一下使用心得。首先说说外观设计，整体简约大方，表带材质舒适，长时间佩戴不会有不适感。功能方面，运动监测很准确，心率监测、步数统计都很精准。续航能力也不错，正常使用可以坚持3-4天。不过也有一些小问题，比如在强光下屏幕显示不够清晰，希望后续能够改进。总的来说，这款手表性价比很高，推荐给大家！',
  author: '数码达人',
  avatar: 'https://via.placeholder.com/80x80/ff6b35/ffffff?text=头像',
  category: '经验分享',
  createTime: '2024-01-02 15:30:00',
  views: 1256,
  likes: 128,
  comments: 45,
  favorites: 89,
  pointsRequired: 0,
  tags: ['智能手表', '使用心得', '数码产品', '推荐'],
  images: [
    'https://via.placeholder.com/400x300/ff6b35/ffffff?text=图片1',
    'https://via.placeholder.com/400x300/1890ff/ffffff?text=图片2',
    'https://via.placeholder.com/400x300/52c41a/ffffff?text=图片3'
  ]
})

const commentSorts = ref([
  { key: 'time', name: '最新' },
  { key: 'hot', name: '最热' },
  { key: 'likes', name: '点赞最多' }
])

const comments = ref<Comment[]>([
  {
    id: 1,
    author: '用户A',
    avatar: 'https://via.placeholder.com/60x60/ff6b35/ffffff?text=A',
    content: '很详细的分享，我也在考虑入手这款手表，请问续航真的有3-4天吗？',
    createTime: '2024-01-02 16:00:00',
    likes: 12,
    isLiked: false,
    replies: [
      {
        id: 1,
        author: '数码达人',
        content: '是的，我平时开启了心率监测和消息推送，正常使用确实能坚持3-4天。',
        createTime: '2024-01-02 16:30:00'
      }
    ]
  },
  {
    id: 2,
    author: '用户B',
    avatar: 'https://via.placeholder.com/60x60/1890ff/ffffff?text=B',
    content: '感谢分享！我也买了同款，确实很不错。',
    createTime: '2024-01-02 17:00:00',
    likes: 8,
    isLiked: false
  }
])

// 计算属性
const sortedComments = computed(() => {
  const sorted = [...comments.value]
  switch (commentSort.value) {
    case 'hot':
      return sorted.sort((a, b) => b.likes - a.likes)
    case 'likes':
      return sorted.sort((a, b) => b.likes - a.likes)
    case 'time':
    default:
      return sorted.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  }
})

const canSubmitComment = computed(() => {
  return commentText.value.trim().length > 0
})

// 方法
const previewImage = (index: number) => {
  uni.previewImage({
    urls: post.value.images || [],
    current: index
  })
}

const unlockContent = () => {
  uni.showModal({
    title: '解锁内容',
    content: `确定花费${post.value.pointsRequired}积分查看完整内容吗？`,
    success: (res) => {
      if (res.confirm) {
        hasViewed.value = true
        uni.showToast({
          title: '解锁成功',
          icon: 'success'
        })
      }
    }
  })
}

const toggleLike = () => {
  isLiked.value = !isLiked.value
  if (isLiked.value) {
    post.value.likes++
  } else {
    post.value.likes--
  }
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  if (isFavorited.value) {
    post.value.favorites++
  } else {
    post.value.favorites--
  }
  
  uni.showToast({
    title: isFavorited.value ? '已收藏' : '已取消收藏',
    icon: 'success'
  })
}

const sharePost = () => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      const actions = ['微信', '朋友圈', '复制链接']
      uni.showToast({
        title: `已分享到${actions[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}

const rewardAuthor = () => {
  uni.navigateTo({
    url: `/pages/reward/index?postId=${post.value.id}&author=${post.value.author}`
  })
}

const changeCommentSort = (sortKey: string) => {
  commentSort.value = sortKey
}

const toggleCommentLike = (comment: Comment) => {
  comment.isLiked = !comment.isLiked
  if (comment.isLiked) {
    comment.likes++
  } else {
    comment.likes--
  }
}

const replyComment = (comment: Comment) => {
  commentText.value = `@${comment.author} `
  showCommentModal.value = true
}

const loadMoreComments = () => {
  // 加载更多评论逻辑
  uni.showToast({
    title: '加载中...',
    icon: 'loading'
  })
}

const showEmojiPanel = () => {
  // 显示表情面板
}

const chooseCommentImage = () => {
  uni.chooseImage({
    count: 3 - commentImages.value.length,
    success: (res) => {
      commentImages.value.push(...res.tempFilePaths)
    }
  })
}

const removeCommentImage = (index: number) => {
  commentImages.value.splice(index, 1)
}

const submitComment = () => {
  if (!canSubmitComment.value) return
  
  const newComment: Comment = {
    id: Date.now(),
    author: '我',
    avatar: 'https://via.placeholder.com/60x60/52c41a/ffffff?text=我',
    content: commentText.value,
    createTime: new Date().toISOString(),
    likes: 0,
    isLiked: false,
    images: commentImages.value.length > 0 ? [...commentImages.value] : undefined
  }
  
  comments.value.unshift(newComment)
  post.value.comments++
  
  // 重置输入
  commentText.value = ''
  commentImages.value = []
  showCommentModal.value = false
  
  uni.showToast({
    title: '评论成功',
    icon: 'success'
  })
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return time.toLocaleDateString()
}

onLoad((options) => {
  if (options?.id) {
    // 根据ID加载帖子数据
    console.log('Post ID:', options.id)
  }
})
</script>

<style lang="scss" scoped>
.forum-detail {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.post-content {
  background-color: #ffffff;
  margin-bottom: 20rpx;

  .post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f8f8f8;

    .author-info {
      display: flex;
      align-items: center;

      .author-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .author-details {
        .author-name {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .post-time {
          font-size: 22rpx;
          color: #999999;
        }
      }
    }

    .post-category {
      .category-text {
        font-size: 22rpx;
        color: #ff6b35;
        background-color: #fff7f0;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
      }
    }
  }

  .post-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    line-height: 1.4;
    padding: 30rpx;
    padding-bottom: 20rpx;
  }

  .post-body {
    padding: 0 30rpx 30rpx;

    .post-text {
      display: block;
      font-size: 28rpx;
      color: #333333;
      line-height: 1.6;
      margin-bottom: 20rpx;
    }

    .post-images {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12rpx;

      .post-image {
        width: 100%;
        aspect-ratio: 1;
        border-radius: 8rpx;
      }
    }
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    padding: 0 30rpx 20rpx;

    .tag-item {
      font-size: 22rpx;
      color: #1890ff;
      background-color: #f0f8ff;
      padding: 6rpx 12rpx;
      border-radius: 12rpx;
    }
  }

  .points-tip {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff7f0;
    border: 1rpx solid #ff6b35;
    border-radius: 12rpx;
    padding: 20rpx;
    margin: 0 30rpx 20rpx;

    .tip-icon {
      font-size: 28rpx;
      margin-right: 12rpx;
    }

    .tip-text {
      flex: 1;
      font-size: 24rpx;
      color: #ff6b35;
    }

    .unlock-btn {
      padding: 12rpx 24rpx;
      background-color: #ff6b35;
      border-radius: 20rpx;

      .btn-text {
        font-size: 22rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }

  .post-stats {
    display: flex;
    gap: 40rpx;
    padding: 20rpx 30rpx;
    border-top: 1rpx solid #f8f8f8;
    border-bottom: 1rpx solid #f8f8f8;

    .stat-item {
      font-size: 24rpx;
      color: #666666;
    }
  }

  .post-actions {
    display: flex;
    justify-content: space-around;
    padding: 30rpx;

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;

      &.active {
        .action-icon {
          color: #ff6b35;
        }

        .action-text {
          color: #ff6b35;
        }
      }

      .action-icon {
        font-size: 32rpx;
        margin-bottom: 8rpx;
      }

      .action-text {
        font-size: 22rpx;
        color: #666666;
      }
    }
  }
}

.comments-section {
  background-color: #ffffff;

  .comments-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f8f8f8;

    .comments-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }

    .sort-options {
      display: flex;
      gap: 20rpx;

      .sort-item {
        font-size: 22rpx;
        color: #666666;
        padding: 8rpx 16rpx;
        border-radius: 16rpx;

        &.active {
          color: #ff6b35;
          background-color: #fff7f0;
        }
      }
    }
  }

  .comments-list {
    .comment-item {
      display: flex;
      padding: 30rpx;
      border-bottom: 1rpx solid #f8f8f8;

      .comment-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .comment-content {
        flex: 1;

        .comment-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12rpx;

          .comment-author {
            font-size: 24rpx;
            color: #333333;
            font-weight: 600;
          }

          .comment-time {
            font-size: 20rpx;
            color: #999999;
          }
        }

        .comment-text {
          display: block;
          font-size: 26rpx;
          color: #333333;
          line-height: 1.5;
          margin-bottom: 16rpx;
        }

        .comment-images {
          display: flex;
          gap: 8rpx;
          margin-bottom: 16rpx;

          .comment-image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
          }
        }

        .comment-actions {
          display: flex;
          gap: 30rpx;

          .comment-like, .comment-reply {
            font-size: 22rpx;
            color: #666666;

            &.active {
              color: #ff6b35;
            }
          }
        }

        .replies-list {
          margin-top: 20rpx;
          padding: 20rpx;
          background-color: #f8f8f8;
          border-radius: 8rpx;

          .reply-item {
            margin-bottom: 16rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .reply-author {
              font-size: 22rpx;
              color: #ff6b35;
              font-weight: 600;
              margin-right: 8rpx;
            }

            .reply-text {
              font-size: 22rpx;
              color: #333333;
            }

            .reply-time {
              display: block;
              font-size: 20rpx;
              color: #999999;
              margin-top: 8rpx;
            }
          }
        }
      }
    }
  }

  .load-more-comments {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx;

    .load-text {
      font-size: 24rpx;
      color: #666666;
    }
  }
}

.comment-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);

  .comment-input {
    flex: 1;
    height: 60rpx;
    font-size: 26rpx;
    color: #333333;
    background-color: #f8f8f8;
    border-radius: 30rpx;
    padding: 0 20rpx;
    margin-right: 20rpx;
  }

  .input-actions {
    display: flex;
    gap: 20rpx;

    .emoji-btn, .image-btn {
      font-size: 32rpx;
    }
  }
}

.comment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .modal-content {
    width: 100%;
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    max-height: 80vh;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }

      .close-btn {
        font-size: 32rpx;
        color: #999999;
      }
    }

    .comment-textarea {
      width: 100%;
      min-height: 200rpx;
      font-size: 28rpx;
      color: #333333;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      box-sizing: border-box;
    }

    .comment-images-preview {
      display: flex;
      gap: 12rpx;
      margin-bottom: 20rpx;

      .image-item {
        position: relative;

        .preview-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
        }

        .remove-image {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #ff4d4f;
          border-radius: 50%;
          font-size: 18rpx;
          color: #ffffff;
        }
      }
    }

    .modal-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .add-image-btn {
        font-size: 26rpx;
        color: #666666;
      }

      .submit-btn {
        padding: 20rpx 40rpx;
        background-color: #ff6b35;
        border-radius: 30rpx;

        &.disabled {
          background-color: #d9d9d9;
        }

        .btn-text {
          font-size: 26rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
