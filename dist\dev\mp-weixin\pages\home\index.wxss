.home.data-v-2c5296db {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header.data-v-2c5296db {
  text-align: center;
  margin-bottom: 60rpx;
}
.header .title.data-v-2c5296db {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}
.header .subtitle.data-v-2c5296db {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.features.data-v-2c5296db {
  margin-bottom: 60rpx;
}
.feature-item.data-v-2c5296db {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.feature-item .feature-icon.data-v-2c5296db {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.feature-item .feature-content.data-v-2c5296db {
  flex: 1;
}
.feature-item .feature-content .feature-title.data-v-2c5296db {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}
.feature-item .feature-content .feature-desc.data-v-2c5296db {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
}
.actions.data-v-2c5296db {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.btn.data-v-2c5296db {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}
.btn.primary.data-v-2c5296db {
  background: #ffffff;
  color: #667eea;
}
.btn.secondary.data-v-2c5296db {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}