.home.data-v-2c5296db {
  background: #f8f9fa;
  min-height: 100vh;
}
.header-section.data-v-2c5296db {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding: 0 30rpx 40rpx;
  border-radius: 0 0 40rpx 40rpx;
}
.header-section .user-section.data-v-2c5296db {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.header-section .user-section .user-info.data-v-2c5296db {
  display: flex;
  align-items: center;
}
.header-section .user-section .user-info .avatar.data-v-2c5296db {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}
.header-section .user-section .user-info .user-text .greeting.data-v-2c5296db {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}
.header-section .user-section .user-info .user-text .subtitle.data-v-2c5296db {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.header-section .user-section .header-actions.data-v-2c5296db {
  display: flex;
  gap: 20rpx;
}
.header-section .user-section .header-actions .action-btn.data-v-2c5296db {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.header-section .user-section .header-actions .action-btn .action-icon.data-v-2c5296db {
  font-size: 32rpx;
  color: #ffffff;
}
.header-section .user-section .header-actions .action-btn.notification .red-dot.data-v-2c5296db {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
}
.header-section .search-container .search-box.data-v-2c5296db {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.header-section .search-container .search-box .search-icon.data-v-2c5296db {
  font-size: 32rpx;
  color: #999999;
  margin-right: 20rpx;
}
.header-section .search-container .search-box .search-placeholder.data-v-2c5296db {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}
.main-content.data-v-2c5296db {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  margin-top: -20rpx;
}
.main-content .quick-card.data-v-2c5296db {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 30rpx;
  color: #ffffff;
}
.main-content .quick-card .card-header.data-v-2c5296db {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.main-content .quick-card .card-header .card-icon.data-v-2c5296db {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
}
.main-content .quick-card .card-header .card-title .title.data-v-2c5296db {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.main-content .quick-card .card-header .card-title .subtitle.data-v-2c5296db {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}
.main-content .quick-card .card-content .feature-item.data-v-2c5296db {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.main-content .quick-card .card-content .feature-item .feature-icon.data-v-2c5296db {
  font-size: 28rpx;
  margin-right: 16rpx;
}
.main-content .quick-card .card-content .feature-item .feature-text.data-v-2c5296db {
  font-size: 26rpx;
  opacity: 0.9;
}
.main-content .quick-card .card-content .tag-group.data-v-2c5296db {
  display: flex;
  gap: 12rpx;
}
.main-content .quick-card .card-content .tag-group .tag.data-v-2c5296db {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.main-content .side-cards.data-v-2c5296db {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.main-content .side-cards .function-card.data-v-2c5296db {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-content .side-cards .function-card .card-icon-wrapper.data-v-2c5296db {
  width: 60rpx;
  height: 60rpx;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.main-content .side-cards .function-card .card-icon-wrapper.green.data-v-2c5296db {
  background: #e8f5e8;
}
.main-content .side-cards .function-card .card-icon-wrapper .card-icon.data-v-2c5296db {
  font-size: 28rpx;
}
.main-content .side-cards .function-card .card-info.data-v-2c5296db {
  flex: 1;
}
.main-content .side-cards .function-card .card-info .card-title.data-v-2c5296db {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}
.main-content .side-cards .function-card .card-info .card-desc.data-v-2c5296db {
  display: block;
  font-size: 24rpx;
  color: #666666;
}
.bottom-nav.data-v-2c5296db {
  display: flex;
  justify-content: space-around;
  background: #ffffff;
  padding: 30rpx 20rpx;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.bottom-nav .nav-item.data-v-2c5296db {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bottom-nav .nav-item .nav-icon.data-v-2c5296db {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}
.bottom-nav .nav-item .nav-text.data-v-2c5296db {
  font-size: 22rpx;
  color: #666666;
}
.flight-section.data-v-2c5296db {
  background: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.flight-section .section-header.data-v-2c5296db {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.flight-section .section-header .section-title.data-v-2c5296db {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.flight-section .section-header .more-link.data-v-2c5296db {
  font-size: 26rpx;
  color: #4facfe;
}
.flight-section .flight-tabs.data-v-2c5296db {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  overflow-x: auto;
}
.flight-section .flight-tabs .tab-item.data-v-2c5296db {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  background: #f5f5f5;
  white-space: nowrap;
  transition: all 0.3s ease;
}
.flight-section .flight-tabs .tab-item.active.data-v-2c5296db {
  background: #4facfe;
  color: #ffffff;
}
.flight-section .flight-list .flight-item.data-v-2c5296db {
  border: 1rpx solid #f0f0f0;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}
.flight-section .flight-list .flight-item.data-v-2c5296db:last-child {
  margin-bottom: 0;
}
.flight-section .flight-list .flight-item .flight-header.data-v-2c5296db {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.flight-section .flight-list .flight-item .flight-header .flight-title.data-v-2c5296db {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}
.flight-section .flight-list .flight-item .flight-header .flight-time.data-v-2c5296db {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.flight-section .flight-list .flight-item .flight-header .flight-time .time-text.data-v-2c5296db {
  font-size: 24rpx;
  color: #666666;
}
.flight-section .flight-list .flight-item .flight-header .flight-time .arrow.data-v-2c5296db {
  font-size: 20rpx;
  color: #4facfe;
}
.flight-section .flight-list .flight-item .flight-details.data-v-2c5296db {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flight-section .flight-list .flight-item .flight-details .flight-info.data-v-2c5296db {
  font-size: 24rpx;
  color: #999999;
}
.flight-section .flight-list .flight-item .flight-details .action-btn-small.data-v-2c5296db {
  background: #4facfe;
  color: #ffffff;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.data-v-2c5296db .uni-swiper-dot {
  width: 16rpx !important;
  height: 16rpx !important;
  border-radius: 50% !important;
}
.data-v-2c5296db .uni-swiper-dot-active {
  background-color: #ffffff !important;
}