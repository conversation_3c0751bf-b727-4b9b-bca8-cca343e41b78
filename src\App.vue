<template>
  <view class="app">
    <!-- 应用根组件 -->
  </view>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
  console.log('App Launch')
  // 应用启动时的逻辑
})

onShow(() => {
  console.log('App Show')
  // 应用显示时的逻辑
})

onHide(() => {
  console.log('App Hide')
  // 应用隐藏时的逻辑
})
</script>

<style lang="scss">
@use '@/styles/index.scss';

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 全局样式优化 */
page {
  font-size: 28rpx;
  line-height: 1.6;
  background-color: #f8f9fa;
}

/* 微信小程序 tabBar 样式优化 - 增大尺寸 */
.wx-tab-bar {
  height: 160rpx !important;
  padding: 20rpx 0 !important;
  background: #ffffff !important;
  border-top: 1rpx solid #e5e5e5 !important;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1) !important;
}

.wx-tab-bar-item {
  font-size: 32rpx !important;
  font-weight: 500 !important;
  padding: 16rpx 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

/* tabBar 图标优化 */
.wx-tab-bar-item .wx-tab-bar-item-icon {
  width: 72rpx !important;
  height: 72rpx !important;
  font-size: 72rpx !important;
  margin-bottom: 12rpx !important;
}

.wx-tab-bar-item .wx-tab-bar-item-text {
  font-size: 32rpx !important;
  font-weight: 500 !important;
  line-height: 1.2 !important;
}

/* 选中状态优化 */
.wx-tab-bar-item.wx-tab-bar-item-active .wx-tab-bar-item-icon {
  transform: scale(1.1) !important;
  transition: transform 0.2s ease !important;
}

.wx-tab-bar-item.wx-tab-bar-item-active .wx-tab-bar-item-text {
  font-weight: bold !important;
}

/* 针对页面内容的底部边距，避免被更大的 tabBar 遮挡 */
.page-content {
  padding-bottom: 180rpx;
}

/* uni-app tabBar 全局样式优化 */
:deep(.uni-tabbar) {
  height: 160rpx !important;
  padding: 20rpx 0 !important;
  background: #ffffff !important;
  border-top: 1rpx solid #e5e5e5 !important;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1) !important;
}

:deep(.uni-tabbar .uni-tabbar-item) {
  height: 140rpx !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 16rpx 0 !important;
}

:deep(.uni-tabbar .uni-tabbar-item .uni-tabbar-item-icon) {
  width: 72rpx !important;
  height: 72rpx !important;
  font-size: 72rpx !important;
  margin-bottom: 12rpx !important;
  margin-top: 0 !important;
}

:deep(.uni-tabbar .uni-tabbar-item .uni-tabbar-item-text) {
  font-size: 32rpx !important;
  font-weight: 500 !important;
  margin-top: 0 !important;
  line-height: 1.2 !important;
}

/* 选中状态优化 */
:deep(.uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-icon) {
  transform: scale(1.1) !important;
  transition: transform 0.2s ease !important;
}

:deep(.uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-text) {
  font-weight: bold !important;
  color: #4facfe !important;
}
</style>
