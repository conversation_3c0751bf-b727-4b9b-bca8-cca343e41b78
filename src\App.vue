<template>
  <view class="app">
    <!-- 应用根组件 -->
  </view>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
  console.log('App Launch')
  // 应用启动时的逻辑
})

onShow(() => {
  console.log('App Show')
  // 应用显示时的逻辑
})

onHide(() => {
  console.log('App Hide')
  // 应用隐藏时的逻辑
})
</script>

<style lang="scss">
@use '@/styles/index.scss';

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 全局样式优化 */
page {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 微信小程序 tabBar 样式优化 */
.wx-tab-bar {
  height: 100rpx !important;
  padding: 8rpx 0 !important;
}

.wx-tab-bar-item {
  font-size: 24rpx !important;
  padding: 8rpx 0 !important;
}

/* 针对页面内容的底部边距，避免被 tabBar 遮挡 */
.page-content {
  padding-bottom: 120rpx;
}
</style>
