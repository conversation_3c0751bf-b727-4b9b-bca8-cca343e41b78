"use strict";var e=require("debug"),t=require("postcss-selector-parser"),o=require("fs");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=a(e),r=a(t),s=a(o);const i=n.default("automator:devtool");function c(e){e.walk((e=>{if("tag"===e.type){const t=e.value;e.value="page"===t?"uni-page-body":"uni-"+t}}))}const l=["Page.getElement","Page.getElements","Element.getElement","Element.getElements"];function p(e){try{return require(e)}catch(t){return require(require.resolve(e,{paths:[process.cwd()]}))}}/^win/.test(process.platform);const u=["chromium","firefox","webkit"];let w=!1;try{w=!!p("playwright")}catch(e){}const d=new Map;function f(e="chromium"){const t=e&&u.includes(e)?e:u[0];let o=d.get(t);return o||(o=function(e){if("webkit"===e)return h("webkit");if("firefox"===e)return h("firefox");if(w)return h("chromium");throw new Error("Playwright dependency not found, please install playwright!")}(t),d.set(t,o)),o}function h(e){const t=p("playwright");let o,a;return{type:e,provider:"playwright",async open(n,r,s){o=await t[e].launch(r.options),"firefox"===e&&(r.contextOptions.isMobile=!1),i(`browser.newContext ${JSON.stringify(r.contextOptions)}`);const c=await o.newContext(r.contextOptions);a=await c.newPage(),a.on("console",(e=>{s.emit("App.logAdded",{type:e.type(),args:[e.text()]})})),a.on("pageerror",(e=>{s.emit("App.exceptionThrown",e)})),await a.goto(r.url||n),await a.waitForTimeout(1e3)},close:()=>o.close(),screenshot:(e=!1)=>a.screenshot({fullPage:e}).then((e=>e.toString("base64"))),swipe:e=>new Promise((async t=>{const{startPoint:o,endPoint:n}=e;await a.evaluate((([e,t])=>{window.scrollBy({left:e.x-t.x,top:e.y-t.y,behavior:"smooth"})}),[o,n]),t("swipe success")})),tap:e=>new Promise((async t=>{const{x:o,y:n,duration:r}=e;await a.mouse.move(o,n),await a.mouse.down(),await a.waitForTimeout(r||0),await a.mouse.up(),t("tap success")})),keyboardInput:e=>new Promise((async t=>{await a.keyboard.type(e),t("keyboardInput success")}))}}let y;const m={"Tool.close":{reflect:async()=>{await y.close()}},"App.exit":{reflect:async()=>{}},"App.enableLog":{reflect:()=>Promise.resolve()},"App.captureScreenshot":{reflect:async(e,t)=>{const o=await y.screenshot(!!t.fullPage);return i(`App.captureScreenshot ${o.length}`),{data:o}}},"App.swipe":{reflect:async(e,t)=>{const o=await y.swipe(t);return i(`App.swipe ${o.length}`),{data:o}}},"App.tap":{reflect:async(e,t)=>{const o=await y.tap(t);return i(`App.tap ${o.length}`),{data:o}}},"App.keyboardInput":{reflect:async(e,t)=>{const o=await y.keyboardInput(t);return i(`App.keyboardInput ${o.length}`),{data:o}}}};!function(e){l.forEach((t=>{e[t]=function(e){return{reflect:async(t,o)=>t(e,o,!1),params:e=>(e.selector&&(e.selector=r.default(c).processSync(e.selector)),e)}}(t)}))}(m);const g={devtools:{name:"browser",paths:[],validate:async function(e){return e.options=e.options||{},e.executablePath&&!e.options.executablePath&&(e.options.executablePath=e.executablePath),e.contextOptions={viewport:Object.assign({width:375,height:667},e.options.defaultViewport||{}),hasTouch:!0,isMobile:!0,deviceScaleFactor:2},e.options.defaultViewport=Object.assign({width:375,height:667,deviceScaleFactor:2,hasTouch:!0,isMobile:!0},e.options.defaultViewport||{}),e.teardown||(e.teardown=!1===e.options.headless?"disconnect":"close"),e},create:async function(e,t,o){t.executablePath?await new Promise(((o,a)=>{const{exec:n}=require("node:child_process");if(/^win/.test(process.platform)){const a="C:\\Users\\<USER>\\AppData\\Local\\chrome";s.default.existsSync(a)||s.default.mkdirSync(a,{recursive:!0}),n(`start ${t.executablePath} --user-data-dir=${a} ${e}`,(e=>{if(e)throw console.error(`open ${t.executablePath} fail, ${e}`),Error(e)})),setTimeout((()=>{o(null)}),1e3)}else n(`open -a "${t.executablePath}" ${e}`,(e=>{e&&(console.error(`open ${t.executablePath} fail, ${e}`),a(e)),o(null)}))})):(y=f(process.env.BROWSER),i("createDevtools "+(y.provider+" "+y.type+" "+JSON.stringify(t))),await y.open(e,t,o))}},shouldCompile:(e,t)=>!t.url,adapter:m};module.exports=g;
