"use strict";var e=(e,n,o)=>new Promise((t,a)=>{var i=e=>{try{r(o.next(e))}catch(n){a(n)}},s=e=>{try{r(o.throw(e))}catch(n){a(n)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,s);r((o=o.apply(e,n)).next())});const n=require("../../common/vendor.js"),o=require("../../common/assets.js");if(!Array){(n.resolveComponent("mall-card")+n.resolveComponent("mall-button"))()}Math||((()=>"../../components/mall-card/mall-card.js")+(()=>"../../components/mall-button/mall-button.js"))();const t=n.defineComponent({__name:"index",setup(t){const a=n.ref(null),i=n.ref({orderCount:0,couponCount:0,favoriteCount:0}),s=n.computed(()=>!!a.value),r=n.ref([{key:"pending",name:"待付款",icon:"/static/icons/order-pending.png",count:2},{key:"paid",name:"待发货",icon:"/static/icons/order-paid.png",count:1},{key:"shipped",name:"待收货",icon:"/static/icons/order-shipped.png",count:0},{key:"completed",name:"已完成",icon:"/static/icons/order-completed.png",count:5}]),u=n.ref([{key:"address",name:"收货地址",icon:"/static/icons/address.png"},{key:"coupon",name:"优惠券",icon:"/static/icons/coupon.png"},{key:"favorite",name:"我的收藏",icon:"/static/icons/heart.png"},{key:"history",name:"浏览历史",icon:"/static/icons/history.png"},{key:"feedback",name:"意见反馈",icon:"/static/icons/feedback.png"},{key:"about",name:"关于我们",icon:"/static/icons/info.png"}]);n.onMounted(()=>{c()}),n.onShow(()=>{s.value&&l()});const c=()=>e(this,null,function*(){try{n.index.getStorageSync("token")&&(a.value={id:"1",nickname:"小明",avatar:"https://picsum.photos/100/100?random=40",phone:"138****8888",gender:1,level:3,points:1580,balance:299.5,status:"active",createdAt:"2024-01-01",updatedAt:"2024-01-01"},yield l())}catch(e){}}),l=()=>e(this,null,function*(){try{i.value={orderCount:8,couponCount:5,favoriteCount:12}}catch(e){}}),d=()=>{s.value?n.index.navigateTo({url:"/pages/user/profile/index"}):n.index.navigateTo({url:"/pages/user/login/index"})},p=e=>{const o=e?`/pages/order/list/index?status=${e}`:"/pages/order/list/index";n.index.navigateTo({url:o})},v=()=>{n.index.navigateTo({url:"/pages/user/coupon/index"})},g=()=>{n.index.navigateTo({url:"/pages/user/favorite/index"})},m=()=>{n.index.navigateTo({url:"/pages/user/balance/index"})},x=()=>{n.index.navigateTo({url:"plugin://wx-service-chat-plugin/chat"})},y=()=>{n.index.navigateTo({url:"/pages/user/settings/index"})},f=()=>e(this,null,function*(){try{(yield n.index.showModal({title:"确认退出",content:"确定要退出登录吗？"})).confirm&&(n.index.removeStorageSync("token"),n.index.removeStorageSync("userInfo"),a.value=null,i.value={orderCount:0,couponCount:0,favoriteCount:0},n.index.showToast({title:"已退出登录",icon:"success"}))}catch(e){}});return(e,t)=>{var c,l,h,k,b;return n.e({a:(null==(c=a.value)?void 0:c.avatar)||"/static/icons/avatar-default.png",b:s.value},s.value?{c:n.t((null==(l=a.value)?void 0:l.nickname)||"用户")}:{},{d:s.value},s.value?{e:n.t((null==(h=a.value)?void 0:h.level)||1),f:n.t((null==(k=a.value)?void 0:k.points)||0)}:{},{g:o._imports_0$1,h:n.o(d),i:s.value},s.value?{j:n.t(i.value.orderCount),k:n.o(p),l:n.t(i.value.couponCount),m:n.o(v),n:n.t(i.value.favoriteCount),o:n.o(g),p:n.t(((null==(b=a.value)?void 0:b.balance)||0).toFixed(2)),q:n.o(m)}:{},{r:s.value},s.value?{s:n.f(r.value,(e,o,t)=>n.e({a:e.icon,b:e.count>0},e.count>0?{c:n.t(e.count)}:{},{d:n.t(e.name),e:e.key,f:n.o(n=>p(e.key),e.key)})),t:n.o(p),v:n.p({title:"我的订单",extra:"查看全部"})}:{},{w:n.f(u.value,(e,o,t)=>({a:e.icon,b:n.t(e.name),c:e.key,d:n.o(o=>(e=>{const o={address:"/pages/user/address/index",coupon:"/pages/user/coupon/index",favorite:"/pages/user/favorite/index",history:"/pages/user/history/index",feedback:"/pages/user/feedback/index",about:"/pages/user/about/index"}[e.key];o&&n.index.navigateTo({url:o})})(e),e.key)})),x:o._imports_0$1,y:o._imports_1$1,z:o._imports_0$1,A:n.o(x),B:o._imports_2$1,C:o._imports_0$1,D:n.o(y),E:s.value},s.value?{F:n.o(f),G:n.p({type:"text",block:!0})}:{})}}}),a=n._export_sfc(t,[["__scopeId","data-v-642c545b"]]);wx.createPage(a);
