{"version": 3, "file": "simple-traverse.js", "sourceRoot": "", "sources": ["../src/simple-traverse.ts"], "names": [], "mappings": ";;AAmFA,wCASC;AA3FD,kEAA8D;AAI9D,SAAS,WAAW,CAAC,CAAU;IAC7B,OAAO,CACL,OAAO,CAAC,KAAK,QAAQ;QACrB,CAAC,IAAI,IAAI;QACT,MAAM,IAAI,CAAC;QACX,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAC3B,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,cAAkC,EAClC,IAAmB;IAEnB,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAU,CAAC;AAC/B,CAAC;AAgBD,MAAM,eAAe;IAKnB,YAAY,SAAgC,EAAE,iBAAiB,GAAG,KAAK;QAJtD,mBAAc,GAA0B,0BAAW,CAAC;QAKnE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,IAAa,EAAE,MAAiC;QACvD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YAElC,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;oBACpC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAED,SAAgB,cAAc,CAC5B,YAA2B,EAC3B,OAA8B,EAC9B,iBAAiB,GAAG,KAAK;IAEzB,IAAI,eAAe,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CACtD,YAAY,EACZ,SAAS,CACV,CAAC;AACJ,CAAC"}