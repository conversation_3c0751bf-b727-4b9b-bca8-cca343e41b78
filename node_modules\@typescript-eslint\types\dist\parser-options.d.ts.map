{"version": 3, "file": "parser-options.d.ts", "sourceRoot": "", "sources": ["../src/parser-options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAE1C,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAEjC,KAAK,UAAU,GAAG,CAAC,QAAQ,GAAG,mBAAmB,GAAG,YAAY,CAAC,EAAE,GAAG,OAAO,CAAC;AAC9E,KAAK,oBAAoB,GAAG,MAAM,GAAG,UAAU,CAAC;AAEhD,KAAK,WAAW,GACZ,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,CAAC,GACD,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,EAAE,GACF,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,QAAQ,GACR,SAAS,CAAC;AAEd,KAAK,iBAAiB,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,KAAK,UAAU,GAAG,iBAAiB,GAAG,UAAU,CAAC;AAEjD,KAAK,gBAAgB,GAAG,KAAK,GAAG,MAAM,GAAG,WAAW,CAAC;AAGrD,UAAU,aAAa;IACrB,YAAY,CAAC,EACT;QACE,YAAY,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QACnC,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QAC1B,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;KACxB,GACD,SAAS,CAAC;IACd,WAAW,CAAC,EAAE,WAAW,CAAC;IAG1B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,eAAe,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IAGZ,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC,sBAAsB,CAAC,EAAE,OAAO,CAAC;IAGjC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,2CAA2C,CAAC,EAAE,OAAO,CAAC;IACtD,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,8BAA8B,CAAC,EAAE,OAAO,CAAC;IACzC,gDAAgD,CAAC,EAAE,OAAO,CAAC;IAC3D,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC,GAAG,CAAC,EAAE,OAAO,CAAC;IACd,QAAQ,CAAC,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC;IAC7C,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC;IACnC,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC;IACpC,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,kCAAkC,CAAC,EAAE,OAAO,CAAC;IAC7C,aAAa,CAAC,EAAE;QACd,IAAI,CAAC,EAAE,oBAAoB,CAAC;KAC7B,CAAC;IAEF,CAAC,oBAAoB,EAAE,MAAM,GAAG,OAAO,CAAC;CACzC;AAED,OAAO,EACL,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,UAAU,GACX,CAAC"}