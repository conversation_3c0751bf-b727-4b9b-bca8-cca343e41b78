@use './variables.scss' as *;

// 全局样式重置 - 微信小程序兼容写法
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: $font-size-base;
  line-height: 1.6;
  color: $text-color-primary;
  background-color: $bg-color-secondary;
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 边距工具类
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-xs { margin: $spacing-xs; }
.mt-xs { margin-top: $spacing-xs; }
.mr-xs { margin-right: $spacing-xs; }
.mb-xs { margin-bottom: $spacing-xs; }
.ml-xs { margin-left: $spacing-xs; }

.m-sm { margin: $spacing-sm; }
.mt-sm { margin-top: $spacing-sm; }
.mr-sm { margin-right: $spacing-sm; }
.mb-sm { margin-bottom: $spacing-sm; }
.ml-sm { margin-left: $spacing-sm; }

.m-base { margin: $spacing-base; }
.mt-base { margin-top: $spacing-base; }
.mr-base { margin-right: $spacing-base; }
.mb-base { margin-bottom: $spacing-base; }
.ml-base { margin-left: $spacing-base; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-xs { padding: $spacing-xs; }
.pt-xs { padding-top: $spacing-xs; }
.pr-xs { padding-right: $spacing-xs; }
.pb-xs { padding-bottom: $spacing-xs; }
.pl-xs { padding-left: $spacing-xs; }

.p-sm { padding: $spacing-sm; }
.pt-sm { padding-top: $spacing-sm; }
.pr-sm { padding-right: $spacing-sm; }
.pb-sm { padding-bottom: $spacing-sm; }
.pl-sm { padding-left: $spacing-sm; }

.p-base { padding: $spacing-base; }
.pt-base { padding-top: $spacing-base; }
.pr-base { padding-right: $spacing-base; }
.pb-base { padding-bottom: $spacing-base; }
.pl-base { padding-left: $spacing-base; }

// 文字颜色工具类
.text-primary { color: $primary-color; }
.text-secondary { color: $secondary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-error { color: $error-color; }
.text-info { color: $info-color; }

// 背景颜色工具类
.bg-primary { background-color: $primary-color; }
.bg-secondary { background-color: $secondary-color; }
.bg-success { background-color: $success-color; }
.bg-warning { background-color: $warning-color; }
.bg-error { background-color: $error-color; }
.bg-info { background-color: $info-color; }

// 圆角工具类
.rounded-sm { border-radius: $border-radius-sm; }
.rounded { border-radius: $border-radius-base; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-full { border-radius: $border-radius-round; }

// 阴影工具类
.shadow-light { box-shadow: $box-shadow-light; }
.shadow { box-shadow: $box-shadow-base; }
.shadow-dark { box-shadow: $box-shadow-dark; }
