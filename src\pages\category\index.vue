<template>
  <view class="category-page">
    <!-- 搜索栏 -->
    <view class="search-section">
      <mall-search 
        v-model="searchKeyword"
        placeholder="搜索商品分类"
        @confirm="handleSearch"
      />
    </view>
    
    <view class="category-content">
      <!-- 左侧分类列表 -->
      <view class="category-sidebar">
        <scroll-view scroll-y class="category-list">
          <view 
            v-for="category in categories" 
            :key="category.id"
            class="category-item"
            :class="{ 'category-item--active': activeCategory?.id === category.id }"
            @click="handleCategorySelect(category)"
          >
            <image :src="category.icon" class="category-icon" mode="aspectFit" />
            <text class="category-name">{{ category.name }}</text>
          </view>
        </scroll-view>
      </view>
      
      <!-- 右侧子分类和商品 -->
      <view class="category-main">
        <scroll-view scroll-y class="main-content">
          <!-- 子分类 -->
          <view v-if="subCategories.length > 0" class="sub-category-section">
            <view class="section-title">{{ activeCategory?.name }}</view>
            <view class="sub-category-grid">
              <view 
                v-for="subCategory in subCategories" 
                :key="subCategory.id"
                class="sub-category-item"
                @click="handleSubCategoryClick(subCategory)"
              >
                <image :src="subCategory.icon" class="sub-category-icon" mode="aspectFit" />
                <text class="sub-category-name">{{ subCategory.name }}</text>
              </view>
            </view>
          </view>
          
          <!-- 热门商品 -->
          <view v-if="hotProducts.length > 0" class="hot-products-section">
            <view class="section-title">热门商品</view>
            <view class="product-list">
              <view 
                v-for="product in hotProducts" 
                :key="product.id"
                class="product-item"
                @click="handleProductClick(product)"
              >
                <image :src="product.images[0]" class="product-image" mode="aspectFill" />
                <view class="product-info">
                  <view class="product-name ellipsis-2">{{ product.name }}</view>
                  <view class="product-price">¥{{ product.price.toFixed(2) }}</view>
                  <view class="product-sales">已售{{ product.sales }}</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { Category, Product } from '@/types'

// 响应式数据
const searchKeyword = ref('')
const categories = ref<Category[]>([])
const activeCategory = ref<Category | null>(null)
const subCategories = ref<Category[]>([])
const hotProducts = ref<Product[]>([])
const loading = ref(false)

// 页面生命周期
onMounted(() => {
  initPage()
})

// 初始化页面
const initPage = async () => {
  try {
    loading.value = true
    await loadCategories()
    if (categories.value.length > 0) {
      await handleCategorySelect(categories.value[0])
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载分类数据
const loadCategories = async () => {
  // 模拟数据
  categories.value = [
    { id: '1', name: '服装鞋包', icon: '/static/icons/clothes.png', sort: 1, status: 'active' },
    { id: '2', name: '数码电器', icon: '/static/icons/digital.png', sort: 2, status: 'active' },
    { id: '3', name: '家居生活', icon: '/static/icons/home.png', sort: 3, status: 'active' },
    { id: '4', name: '美妆护肤', icon: '/static/icons/beauty.png', sort: 4, status: 'active' },
    { id: '5', name: '运动户外', icon: '/static/icons/sport.png', sort: 5, status: 'active' },
    { id: '6', name: '母婴用品', icon: '/static/icons/baby.png', sort: 6, status: 'active' },
    { id: '7', name: '食品饮料', icon: '/static/icons/food.png', sort: 7, status: 'active' },
    { id: '8', name: '图书文具', icon: '/static/icons/book.png', sort: 8, status: 'active' }
  ]
}

// 加载子分类
const loadSubCategories = async (categoryId: string) => {
  // 模拟数据
  const subCategoryMap: Record<string, Category[]> = {
    '1': [
      { id: '1-1', name: '女装', icon: '/static/icons/women-clothes.png', parentId: '1', sort: 1, status: 'active' },
      { id: '1-2', name: '男装', icon: '/static/icons/men-clothes.png', parentId: '1', sort: 2, status: 'active' },
      { id: '1-3', name: '鞋靴', icon: '/static/icons/shoes.png', parentId: '1', sort: 3, status: 'active' },
      { id: '1-4', name: '箱包', icon: '/static/icons/bag.png', parentId: '1', sort: 4, status: 'active' }
    ],
    '2': [
      { id: '2-1', name: '手机', icon: '/static/icons/phone.png', parentId: '2', sort: 1, status: 'active' },
      { id: '2-2', name: '电脑', icon: '/static/icons/computer.png', parentId: '2', sort: 2, status: 'active' },
      { id: '2-3', name: '家电', icon: '/static/icons/appliance.png', parentId: '2', sort: 3, status: 'active' },
      { id: '2-4', name: '数码配件', icon: '/static/icons/accessory.png', parentId: '2', sort: 4, status: 'active' }
    ]
  }
  
  subCategories.value = subCategoryMap[categoryId] || []
}

// 加载热门商品
const loadHotProducts = async (categoryId: string) => {
  // 模拟数据
  const mockProducts: Product[] = [
    {
      id: `${categoryId}-p1`,
      name: '热门商品示例 1',
      description: '商品描述',
      price: 99.00,
      images: ['https://picsum.photos/200/200?random=20'],
      categoryId,
      categoryName: activeCategory.value?.name || '',
      stock: 100,
      sales: 500,
      rating: 4.5,
      reviewCount: 100,
      tags: ['热销'],
      specs: [],
      status: 'active',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: `${categoryId}-p2`,
      name: '热门商品示例 2',
      description: '商品描述',
      price: 199.00,
      images: ['https://picsum.photos/200/200?random=21'],
      categoryId,
      categoryName: activeCategory.value?.name || '',
      stock: 50,
      sales: 300,
      rating: 4.8,
      reviewCount: 80,
      tags: ['新品'],
      specs: [],
      status: 'active',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    }
  ]
  
  hotProducts.value = mockProducts
}

// 事件处理
const handleSearch = (keyword: string) => {
  if (!keyword.trim()) return
  
  uni.navigateTo({
    url: `/pages/product/list/index?keyword=${encodeURIComponent(keyword)}`
  })
}

const handleCategorySelect = async (category: Category) => {
  activeCategory.value = category
  await Promise.all([
    loadSubCategories(category.id),
    loadHotProducts(category.id)
  ])
}

const handleSubCategoryClick = (subCategory: Category) => {
  uni.navigateTo({
    url: `/pages/product/list/index?categoryId=${subCategory.id}`
  })
}

const handleProductClick = (product: Product) => {
  uni.navigateTo({
    url: `/pages/product/detail/index?id=${product.id}`
  })
}
</script>

<style lang="scss" scoped>
.category-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color-page;
  
  .search-section {
    padding: $spacing-lg;
    background-color: $bg-color-white;
    border-bottom: 1px solid $border-color-light;
  }
  
  .category-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }
  
  .category-sidebar {
    width: 100px;
    background-color: $bg-color-white;
    border-right: 1px solid $border-color-light;
    
    .category-list {
      height: 100%;
      
      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: $spacing-lg $spacing-sm;
        cursor: pointer;
        transition: all $animation-duration-base;
        
        &--active {
          background-color: $bg-color-page;
          
          .category-name {
            color: $primary-color;
          }
        }
        
        &:active {
          background-color: $bg-color-gray;
        }
        
        .category-icon {
          width: 24px;
          height: 24px;
          margin-bottom: $spacing-xs;
        }
        
        .category-name {
          font-size: $font-size-xs;
          color: $text-color-primary;
          text-align: center;
          line-height: 1.2;
        }
      }
    }
  }
  
  .category-main {
    flex: 1;
    background-color: $bg-color-white;
    
    .main-content {
      height: 100%;
      padding: $spacing-lg;
    }
    
    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-color-primary;
      margin-bottom: $spacing-lg;
    }
    
    .sub-category-section {
      margin-bottom: $spacing-xl;
      
      .sub-category-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: $spacing-lg;
        
        .sub-category-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: $spacing-lg;
          background-color: $bg-color-gray;
          border-radius: $border-radius-base;
          cursor: pointer;
          transition: all $animation-duration-base;
          
          &:active {
            transform: scale(0.95);
            background-color: darken($bg-color-gray, 5%);
          }
          
          .sub-category-icon {
            width: 32px;
            height: 32px;
            margin-bottom: $spacing-xs;
          }
          
          .sub-category-name {
            font-size: $font-size-sm;
            color: $text-color-primary;
            text-align: center;
          }
        }
      }
    }
    
    .hot-products-section {
      .product-list {
        display: flex;
        flex-direction: column;
        gap: $spacing-lg;
        
        .product-item {
          display: flex;
          gap: $spacing-lg;
          padding: $spacing-lg;
          background-color: $bg-color-gray;
          border-radius: $border-radius-base;
          cursor: pointer;
          transition: all $animation-duration-base;
          
          &:active {
            transform: scale(0.98);
          }
          
          .product-image {
            width: 80px;
            height: 80px;
            border-radius: $border-radius-base;
            background-color: $bg-color-white;
          }
          
          .product-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            
            .product-name {
              font-size: $font-size-base;
              color: $text-color-primary;
              line-height: 1.4;
            }
            
            .product-price {
              font-size: $font-size-lg;
              font-weight: $font-weight-bold;
              color: $primary-color;
            }
            
            .product-sales {
              font-size: $font-size-xs;
              color: $text-color-secondary;
            }
          }
        }
      }
    }
  }
}
</style>
