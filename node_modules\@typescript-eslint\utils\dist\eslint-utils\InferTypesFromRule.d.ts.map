{"version": 3, "file": "InferTypesFromRule.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/InferTypesFromRule.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAEnE;;GAEG;AACH,KAAK,wBAAwB,CAAC,CAAC,IAC7B,CAAC,SAAS,UAAU,CAAC,MAAM,WAAW,EAAE,MAAM,OAAO,CAAC,GAClD,OAAO,GACP,CAAC,SAAS,kBAAkB,CAAC,MAAM,WAAW,EAAE,MAAM,OAAO,CAAC,GAC5D,OAAO,GACP,OAAO,CAAC;AAEhB;;GAEG;AACH,KAAK,2BAA2B,CAAC,CAAC,IAChC,CAAC,SAAS,UAAU,CAAC,MAAM,UAAU,EAAE,MAAM,SAAS,CAAC,GACnD,UAAU,GACV,CAAC,SAAS,kBAAkB,CAAC,MAAM,UAAU,EAAE,MAAM,SAAS,CAAC,GAC7D,UAAU,GACV,OAAO,CAAC;AAEhB,OAAO,EAAE,wBAAwB,EAAE,2BAA2B,EAAE,CAAC"}