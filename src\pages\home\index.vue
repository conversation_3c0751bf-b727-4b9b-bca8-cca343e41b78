<template>
  <view class="home-page">
    <!-- 搜索栏 -->
    <view class="search-section">
      <mall-search 
        v-model="searchKeyword"
        placeholder="搜索商品"
        @confirm="handleSearch"
      />
    </view>
    
    <!-- 轮播图 -->
    <view class="banner-section">
      <swiper 
        class="banner-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ff6b35"
      >
        <swiper-item v-for="banner in banners" :key="banner.id">
          <image 
            :src="banner.image" 
            class="banner-image"
            mode="aspectFill"
            @click="handleBannerClick(banner)"
          />
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 快捷入口 -->
    <view class="quick-entry-section">
      <mall-card>
        <view class="quick-entry-grid">
          <view 
            v-for="entry in quickEntries" 
            :key="entry.id"
            class="quick-entry-item"
            @click="handleQuickEntry(entry)"
          >
            <image :src="entry.icon" class="entry-icon" mode="aspectFit" />
            <text class="entry-text">{{ entry.name }}</text>
          </view>
        </view>
      </mall-card>
    </view>
    
    <!-- 商品分类 -->
    <view class="category-section">
      <mall-card title="商品分类">
        <view class="category-list">
          <view 
            v-for="category in categories" 
            :key="category.id"
            class="category-item"
            @click="handleCategoryClick(category)"
          >
            <image :src="category.icon" class="category-icon" mode="aspectFit" />
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </mall-card>
    </view>
    
    <!-- 推荐商品 -->
    <view class="recommend-section">
      <mall-card title="为你推荐" extra="更多">
        <view class="product-grid">
          <mall-product-card
            v-for="product in recommendProducts"
            :key="product.id"
            :product="product"
            @click="handleProductClick"
            @add-to-cart="handleAddToCart"
          />
        </view>
      </mall-card>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import type { Banner, Category, Product } from '@/types'

// 响应式数据
const searchKeyword = ref('')
const banners = ref<Banner[]>([])
const quickEntries = ref([
  { id: '1', name: '限时抢购', icon: '/static/icons/flash-sale.png' },
  { id: '2', name: '新品上市', icon: '/static/icons/new-product.png' },
  { id: '3', name: '品牌特卖', icon: '/static/icons/brand-sale.png' },
  { id: '4', name: '优惠券', icon: '/static/icons/coupon.png' }
])
const categories = ref<Category[]>([])
const recommendProducts = ref<Product[]>([])
const loading = ref(false)

// 页面生命周期
onMounted(() => {
  initPage()
})

onPullDownRefresh(() => {
  refreshPage()
})

onReachBottom(() => {
  loadMoreProducts()
})

// 初始化页面
const initPage = async () => {
  try {
    loading.value = true
    await Promise.all([
      loadBanners(),
      loadCategories(),
      loadRecommendProducts()
    ])
  } catch (error) {
    console.error('页面初始化失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 刷新页面
const refreshPage = async () => {
  try {
    await initPage()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'none'
    })
  } finally {
    uni.stopPullDownRefresh()
  }
}

// 加载轮播图
const loadBanners = async () => {
  // 模拟数据
  banners.value = [
    {
      id: '1',
      title: '春季新品上市',
      image: 'https://picsum.photos/750/300?random=1',
      link: '/pages/product/list/index?categoryId=1',
      linkType: 'category',
      sort: 1,
      status: 'active'
    },
    {
      id: '2',
      title: '限时特惠',
      image: 'https://picsum.photos/750/300?random=2',
      link: '/pages/product/list/index?tag=sale',
      linkType: 'page',
      sort: 2,
      status: 'active'
    }
  ]
}

// 加载分类
const loadCategories = async () => {
  // 模拟数据
  categories.value = [
    { id: '1', name: '服装', icon: '/static/icons/clothes.png', sort: 1, status: 'active' },
    { id: '2', name: '数码', icon: '/static/icons/digital.png', sort: 2, status: 'active' },
    { id: '3', name: '家居', icon: '/static/icons/home.png', sort: 3, status: 'active' },
    { id: '4', name: '美妆', icon: '/static/icons/beauty.png', sort: 4, status: 'active' }
  ]
}

// 加载推荐商品
const loadRecommendProducts = async () => {
  // 模拟数据
  const mockProducts: Product[] = [
    {
      id: '1',
      name: '春季新款连衣裙 优雅气质款 多色可选',
      description: '优质面料，舒适透气',
      price: 299.00,
      originalPrice: 399.00,
      images: ['https://picsum.photos/300/300?random=10'],
      categoryId: '1',
      categoryName: '服装',
      stock: 100,
      sales: 1234,
      rating: 4.8,
      reviewCount: 567,
      tags: ['新品', '热销'],
      specs: [],
      status: 'active',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: '2',
      name: '无线蓝牙耳机 降噪版 长续航',
      description: '高品质音效，舒适佩戴',
      price: 199.00,
      images: ['https://picsum.photos/300/300?random=11'],
      categoryId: '2',
      categoryName: '数码',
      stock: 50,
      sales: 890,
      rating: 4.6,
      reviewCount: 234,
      tags: ['热销'],
      specs: [],
      status: 'active',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    }
  ]
  
  recommendProducts.value = mockProducts
}

// 加载更多商品
const loadMoreProducts = async () => {
  // 实现分页加载逻辑
  console.log('加载更多商品')
}

// 事件处理
const handleSearch = (keyword: string) => {
  if (!keyword.trim()) return
  
  uni.navigateTo({
    url: `/pages/product/list/index?keyword=${encodeURIComponent(keyword)}`
  })
}

const handleBannerClick = (banner: Banner) => {
  if (banner.link) {
    if (banner.linkType === 'external') {
      // 处理外部链接
      console.log('打开外部链接:', banner.link)
    } else {
      uni.navigateTo({
        url: banner.link
      })
    }
  }
}

const handleQuickEntry = (entry: any) => {
  console.log('快捷入口点击:', entry.name)
  // 根据不同入口跳转到对应页面
}

const handleCategoryClick = (category: Category) => {
  uni.navigateTo({
    url: `/pages/product/list/index?categoryId=${category.id}`
  })
}

const handleProductClick = (product: Product) => {
  uni.navigateTo({
    url: `/pages/product/detail/index?id=${product.id}`
  })
}

const handleAddToCart = (product: Product) => {
  // 添加到购物车逻辑
  uni.showToast({
    title: '已添加到购物车',
    icon: 'success'
  })
}
</script>

<style lang="scss" scoped>
.home-page {
  background-color: $bg-color-page;
  min-height: 100vh;
  
  .search-section {
    padding: $spacing-lg;
    background-color: $bg-color-white;
  }
  
  .banner-section {
    margin-bottom: $spacing-lg;
    
    .banner-swiper {
      height: 180px;
      
      .banner-image {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .quick-entry-section {
    margin: 0 $spacing-lg $spacing-lg;
    
    .quick-entry-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: $spacing-lg;
      padding: $spacing-lg 0;
      
      .quick-entry-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $spacing-xs;
        cursor: pointer;
        
        &:active {
          opacity: 0.7;
        }
        
        .entry-icon {
          width: 40px;
          height: 40px;
        }
        
        .entry-text {
          font-size: $font-size-sm;
          color: $text-color-primary;
        }
      }
    }
  }
  
  .category-section {
    margin: 0 $spacing-lg $spacing-lg;
    
    .category-list {
      display: flex;
      justify-content: space-around;
      padding: $spacing-lg 0;
      
      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $spacing-xs;
        cursor: pointer;
        
        &:active {
          opacity: 0.7;
        }
        
        .category-icon {
          width: 32px;
          height: 32px;
        }
        
        .category-name {
          font-size: $font-size-sm;
          color: $text-color-primary;
        }
      }
    }
  }
  
  .recommend-section {
    margin: 0 $spacing-lg $spacing-lg;
    
    .product-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-lg;
      padding-top: $spacing-lg;
    }
  }
}
</style>
