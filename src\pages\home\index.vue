<template>
  <view class="home">
    <!-- 顶部渐变背景区域 -->
    <view class="header-section">
      <!-- 用户信息区域 -->
      <view class="user-section">
        <view class="user-info">
          <image class="avatar" :src="userAvatar" mode="aspectFill" />
          <view class="user-text">
            <text class="greeting">Hello, {{ userName }}</text>
            <text class="subtitle">欢迎使用京东购物小程序</text>
          </view>
        </view>
        <view class="header-actions">
          <view class="action-btn" @click="handleMessage">
            <image class="action-icon" src="/static/tabbar/message.svg" mode="aspectFit" />
          </view>
          <view class="action-btn notification" @click="handleNotification">
            <image class="action-icon" src="/static/tabbar/notification.svg" mode="aspectFit" />
            <view class="red-dot" v-if="hasNotification"></view>
          </view>
        </view>
      </view>

      <!-- 搜索框 -->
      <view class="search-container">
        <view class="search-box" @click="handleSearch">
          <image class="search-icon" src="/static/tabbar/search.svg" mode="aspectFit" />
          <text class="search-placeholder">搜索人气好物 / 百亿补贴 / 手机数码</text>
        </view>
      </view>
    </view>

    <!-- 主要功能卡片区域 -->
    <view class="main-content">
      <!-- 快速建单卡片 -->
      <view class="quick-card primary-card">
        <view class="card-header">
          <image class="card-icon" src="/static/tabbar/add.svg" mode="aspectFit" />
          <view class="card-title">
            <text class="title">快速建单</text>
            <text class="subtitle">行李托运建单</text>
          </view>
        </view>
        <view class="card-content">
          <view class="feature-item">
            <image class="feature-icon" src="/static/tabbar/package.svg" mode="aspectFit" />
            <text class="feature-text">建单记录 32</text>
          </view>
          <view class="tag-group">
            <text class="tag">PVG</text>
            <text class="tag">SYU</text>
            <text class="tag">MLJ</text>
          </view>
        </view>
      </view>

      <!-- 右侧功能卡片组 -->
      <view class="side-cards">
        <view class="function-card">
          <view class="card-icon-wrapper">
            <image class="card-icon" src="/static/tabbar/chart.svg" mode="aspectFit" />
          </view>
          <view class="card-info">
            <text class="card-title">行李分拣</text>
            <text class="card-desc">24件待分拣</text>
          </view>
        </view>

        <view class="function-card">
          <view class="card-icon-wrapper green">
            <image class="card-icon" src="/static/tabbar/target.svg" mode="aspectFit" />
          </view>
          <view class="card-info">
            <text class="card-title">行李装载</text>
            <text class="card-desc">28件待装载</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部功能导航 -->
    <view class="bottom-nav">
      <view class="nav-item" v-for="nav in bottomNavs" :key="nav.id" @click="handleNavClick(nav)">
        <image class="nav-icon" :src="nav.icon" mode="aspectFit" />
        <text class="nav-text">{{ nav.title }}</text>
      </view>
    </view>

    <!-- 当前航线行李 -->
    <view class="flight-section">
      <view class="section-header">
        <text class="section-title">当前航线行李</text>
        <text class="more-link" @click="handleMoreFlights">查看全部 ></text>
      </view>

      <view class="flight-tabs">
        <text class="tab-item"
              v-for="tab in flightTabs"
              :key="tab.id"
              :class="{ active: activeTab === tab.id }"
              @click="switchTab(tab.id)">
          {{ tab.name }}
        </text>
      </view>

      <view class="flight-list">
        <view class="flight-item" v-for="flight in currentFlights" :key="flight.id">
          <view class="flight-header">
            <text class="flight-title">{{ flight.title }}</text>
            <view class="flight-time">
              <text class="time-text">{{ flight.departTime }}</text>
              <text class="arrow">✈️</text>
              <text class="time-text">{{ flight.arriveTime }}</text>
            </view>
          </view>
          <view class="flight-details">
            <text class="flight-info">{{ flight.aircraft }} | {{ flight.seat }}</text>
            <view class="action-btn-small" @click="handleFlightAction(flight)">
              <text>操作</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 接口定义
interface NavItem {
  id: number
  icon: string
  title: string
  url?: string
}

interface FlightTab {
  id: string
  name: string
}

interface Flight {
  id: number
  title: string
  departTime: string
  arriveTime: string
  aircraft: string
  seat: string
}

// 用户头像 - 使用base64编码的占位图片
const userAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzQyODVmNCIvPgogIDxjaXJjbGUgY3g9IjQwIiBjeT0iMzAiIHI9IjE1IiBmaWxsPSIjZmZmZmZmIi8+CiAgPHBhdGggZD0iTTEwIDcwYzAtMTUgMTMuNDMtMjcgMzAtMjdzMzAgMTIgMzAgMjciIGZpbGw9IiNmZmZmZmYiLz4KPC9zdmc+'



// 用户信息
const userName = ref('东小航')
const hasNotification = ref(true)

// 底部导航数据
const bottomNavs = ref<NavItem[]>([
  { id: 1, icon: '/static/tabbar/target.svg', title: '改签行李' },
  { id: 2, icon: '/static/tabbar/mail.svg', title: '转运行李' },
  { id: 3, icon: '/static/tabbar/target.svg', title: '中转分拣' },
  { id: 4, icon: '/static/tabbar/clock.svg', title: '航班追踪' },
  { id: 5, icon: '/static/tabbar/document.svg', title: '交接记录' }
])

// 航班标签页
const flightTabs = ref<FlightTab[]>([
  { id: 'hongqiao-t2', name: '虹桥T2' },
  { id: 'pudong-t1', name: '浦东T1' },
  { id: 'shuangliu-t2', name: '双流T2' },
  { id: 'baiyun-t3', name: '白云T3' },
  { id: 'daxing', name: '大兴' }
])

// 当前选中的标签页
const activeTab = ref('hongqiao-t2')

// 航班数据
const flightData = ref<Record<string, Flight[]>>({
  'hongqiao-t2': [
    {
      id: 1,
      title: '虹桥T2航站行李@PVG容器',
      departTime: '10:40虹桥T2',
      arriveTime: '12:40首都T2',
      aircraft: '已装载37件',
      seat: 'MU5080'
    },
    {
      id: 2,
      title: '虹桥T2航站行李@SVU容器',
      departTime: '14:20虹桥T2',
      arriveTime: '16:30白云T2',
      aircraft: '待装载15件',
      seat: 'CZ3847'
    }
  ],
  'pudong-t1': [
    {
      id: 3,
      title: '浦东T1航站行李@SHA容器',
      departTime: '08:30浦东T1',
      arriveTime: '11:20成都T1',
      aircraft: '已装载42件',
      seat: 'CA1234'
    }
  ],
  'shuangliu-t2': [
    {
      id: 4,
      title: '双流T2航站行李@CTU容器',
      departTime: '13:15双流T2',
      arriveTime: '15:45深圳T3',
      aircraft: '装载中28件',
      seat: 'ZH9876'
    }
  ],
  'baiyun-t3': [
    {
      id: 5,
      title: '白云T3航站行李@CAN容器',
      departTime: '16:40白云T3',
      arriveTime: '19:20杭州T3',
      aircraft: '待分拣33件',
      seat: 'MF5432'
    }
  ],
  'daxing': [
    {
      id: 6,
      title: '大兴航站行李@PKX容器',
      departTime: '11:25大兴',
      arriveTime: '14:10西安T3',
      aircraft: '已装载51件',
      seat: 'CA9988'
    }
  ]
})

// 当前航班列表
const currentFlights = ref<Flight[]>(flightData.value[activeTab.value] || [])

// 生命周期
onMounted(() => {
  console.log('航旅纵横风格首页加载完成')
})



// 切换标签页
const switchTab = (tabId: string) => {
  activeTab.value = tabId
  currentFlights.value = flightData.value[tabId] || []
}

// 事件处理函数
const handleSearch = () => {
  uni.showToast({
    title: '搜索功能',
    icon: 'none'
  })
}

const handleMessage = () => {
  uni.showToast({
    title: '消息中心',
    icon: 'none'
  })
}

const handleNotification = () => {
  hasNotification.value = false
  uni.showToast({
    title: '通知中心',
    icon: 'none'
  })
}

const handleNavClick = (nav: NavItem) => {
  uni.showToast({
    title: `${nav.title}功能`,
    icon: 'none'
  })
}

const handleMoreFlights = () => {
  uni.showToast({
    title: '查看全部航班',
    icon: 'none'
  })
}

const handleFlightAction = (flight: Flight) => {
  uni.showToast({
    title: `操作${flight.title}`,
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.home {
  background: #f8f9fa;
  min-height: 100vh;
}

// 顶部渐变背景区域
.header-section {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding: 0 30rpx 40rpx;
  border-radius: 0 0 40rpx 40rpx;



  // 用户信息区域
  .user-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        border: 3rpx solid rgba(255, 255, 255, 0.3);
      }

      .user-text {
        .greeting {
          display: block;
          font-size: 36rpx;
          font-weight: bold;
          color: #ffffff;
          margin-bottom: 8rpx;
        }

        .subtitle {
          display: block;
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 20rpx;

      .action-btn {
        position: relative;
        width: 60rpx;
        height: 60rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10rpx);

        .action-icon {
          font-size: 32rpx;
          color: #ffffff;
        }

        &.notification .red-dot {
          position: absolute;
          top: 8rpx;
          right: 8rpx;
          width: 16rpx;
          height: 16rpx;
          background: #ff4757;
          border-radius: 50%;
          border: 2rpx solid #ffffff;
        }
      }
    }
  }

  // 搜索框
  .search-container {
    .search-box {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50rpx;
      padding: 24rpx 30rpx;
      display: flex;
      align-items: center;
      backdrop-filter: blur(10rpx);

      .search-icon {
        font-size: 32rpx;
        color: #999999;
        margin-right: 20rpx;
      }

      .search-placeholder {
        font-size: 28rpx;
        color: #666666;
        flex: 1;
      }
    }
  }
}

// 主要内容区域
.main-content {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  margin-top: -20rpx;

  // 快速建单卡片
  .quick-card {
    flex: 2;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    padding: 30rpx;
    color: #ffffff;

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .card-icon {
        width: 60rpx;
        height: 60rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        margin-right: 20rpx;
      }

      .card-title {
        .title {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
        }

        .subtitle {
          display: block;
          font-size: 24rpx;
          opacity: 0.8;
        }
      }
    }

    .card-content {
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .feature-icon {
          font-size: 28rpx;
          margin-right: 16rpx;
        }

        .feature-text {
          font-size: 26rpx;
          opacity: 0.9;
        }
      }

      .tag-group {
        display: flex;
        gap: 12rpx;

        .tag {
          background: rgba(255, 255, 255, 0.2);
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          border: 1rpx solid rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  // 右侧功能卡片组
  .side-cards {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .function-card {
      background: #ffffff;
      border-radius: 20rpx;
      padding: 24rpx;
      display: flex;
      align-items: center;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

      .card-icon-wrapper {
        width: 60rpx;
        height: 60rpx;
        background: #e3f2fd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;

        &.green {
          background: #e8f5e8;
        }

        .card-icon {
          font-size: 28rpx;
        }
      }

      .card-info {
        flex: 1;

        .card-title {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .card-desc {
          display: block;
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }
}

// 底部功能导航
.bottom-nav {
  display: flex;
  justify-content: space-around;
  background: #ffffff;
  padding: 30rpx 20rpx;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .nav-icon {
      font-size: 40rpx;
      margin-bottom: 12rpx;
    }

    .nav-text {
      font-size: 22rpx;
      color: #666666;
    }
  }
}

// 航班区域
.flight-section {
  background: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }

    .more-link {
      font-size: 26rpx;
      color: #4facfe;
    }
  }

  .flight-tabs {
    display: flex;
    gap: 20rpx;
    margin-bottom: 30rpx;
    overflow-x: auto;

    .tab-item {
      padding: 12rpx 24rpx;
      border-radius: 20rpx;
      font-size: 26rpx;
      color: #666666;
      background: #f5f5f5;
      white-space: nowrap;
      transition: all 0.3s ease;

      &.active {
        background: #4facfe;
        color: #ffffff;
      }
    }
  }

  .flight-list {
    .flight-item {
      border: 1rpx solid #f0f0f0;
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .flight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .flight-title {
          font-size: 28rpx;
          font-weight: bold;
          color: #333333;
          flex: 1;
        }

        .flight-time {
          display: flex;
          align-items: center;
          gap: 12rpx;

          .time-text {
            font-size: 24rpx;
            color: #666666;
          }

          .arrow {
            font-size: 20rpx;
            color: #4facfe;
          }
        }
      }

      .flight-details {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .flight-info {
          font-size: 24rpx;
          color: #999999;
        }

        .action-btn-small {
          background: #4facfe;
          color: #ffffff;
          padding: 8rpx 20rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}

// 全局样式优化
:deep(.uni-swiper-dot) {
  width: 16rpx !important;
  height: 16rpx !important;
  border-radius: 50% !important;
}

:deep(.uni-swiper-dot-active) {
  background-color: #ffffff !important;
}
</style>
