# TabBar 图标优化说明

## 当前解决方案

使用 emoji + 文字的组合作为底部导航图标，已通过以下方式优化：

### ✅ 已完成的优化
1. **增大图标显示**：使用 emoji 图标 + 文字组合
2. **优化颜色对比**：选中状态使用蓝色 (#007aff)
3. **改善边框样式**：使用白色边框减少视觉干扰
4. **全局样式优化**：在 App.vue 中添加了针对性的样式

### 📱 当前图标列表
- 🏠 首页 (home)
- 📂 分类 (category)
- 🛒 购物车 (cart)
- 👤 我的 (profile)

## 🎨 进一步优化方案

### 方案1：使用真实PNG图标
1. 准备 PNG 格式图标文件（建议尺寸：81x81px）
2. 每个图标需要两个状态：
   - 普通状态：`icon-name.png`
   - 选中状态：`icon-name-active.png`
3. 更新 `src/pages.json` 配置：

```json
{
  "pagePath": "pages/home/<USER>",
  "text": "首页",
  "iconPath": "static/tabbar/home.png",
  "selectedIconPath": "static/tabbar/home-active.png"
}
```

### 方案2：自定义TabBar组件
如需完全控制样式和大小，可以使用自定义TabBar：
1. 设置 `"tabBar": { "custom": true }`
2. 创建自定义TabBar组件
3. 在每个页面中引入组件

## 📋 图标设计要求

- **格式**：PNG（推荐）或 SVG
- **尺寸**：81x81px（微信小程序标准）
- **颜色**：
  - 普通状态：#666666（灰色）
  - 选中状态：#007aff（蓝色）
- **背景**：透明
- **风格**：简洁、清晰、符合品牌调性
