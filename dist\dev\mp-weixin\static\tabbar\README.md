# TabBar 图标说明

当前使用 emoji 作为底部导航图标的临时解决方案。

## 如需使用真实图标

1. 准备 PNG 格式图标文件（建议尺寸：81x81px）
2. 每个图标需要两个状态：
   - 普通状态：`icon-name.png`
   - 选中状态：`icon-name-active.png`

3. 将图标文件放置在 `src/static/tabbar/` 目录下

4. 更新 `src/pages.json` 中的 tabBar 配置：

```json
{
  "pagePath": "pages/home/<USER>",
  "text": "首页",
  "iconPath": "static/tabbar/home.png",
  "selectedIconPath": "static/tabbar/home-active.png"
}
```

## 图标要求

- 格式：PNG
- 尺寸：建议 81x81px
- 颜色：普通状态建议使用灰色 (#666666)，选中状态使用主题色 (#007aff)
- 背景：透明

## 当前图标列表

- 🏠 首页 (home)
- 📂 分类 (category) 
- 🛒 购物车 (cart)
- 👤 我的 (profile)
