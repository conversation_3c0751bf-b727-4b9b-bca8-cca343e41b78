<template>
  <view class="category-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar" @click="goToSearch">
        <text class="search-icon">🔍</text>
        <text class="search-placeholder">搜索商品</text>
      </view>
    </view>

    <view class="category-content">
      <!-- 左侧分类列表 -->
      <scroll-view class="category-sidebar" scroll-y>
        <view 
          class="category-item"
          :class="{ active: activeCategory === category.id }"
          v-for="category in categories"
          :key="category.id"
          @click="selectCategory(category)"
        >
          <text class="category-icon">{{ category.icon }}</text>
          <text class="category-name">{{ category.name }}</text>
        </view>
      </scroll-view>

      <!-- 右侧商品列表 -->
      <scroll-view class="products-content" scroll-y>
        <!-- 子分类 -->
        <view class="subcategories" v-if="currentSubcategories.length > 0">
          <view class="subcategory-title">热门分类</view>
          <view class="subcategory-grid">
            <view 
              class="subcategory-item"
              v-for="sub in currentSubcategories"
              :key="sub.id"
              @click="goToProductList(sub.id)"
            >
              <image :src="sub.image" class="subcategory-image" mode="aspectFill" />
              <text class="subcategory-name">{{ sub.name }}</text>
            </view>
          </view>
        </view>

        <!-- 推荐商品 -->
        <view class="recommended-products">
          <view class="section-title">推荐商品</view>
          <view class="products-grid">
            <view 
              class="product-item"
              v-for="product in currentProducts"
              :key="product.id"
              @click="goToProductDetail(product)"
            >
              <image :src="product.image" class="product-image" mode="aspectFill" />
              <view class="product-info">
                <text class="product-name">{{ product.name }}</text>
                <view class="product-price">
                  <text class="current-price">¥{{ product.price }}</text>
                  <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
                </view>
                <view class="product-meta">
                  <text class="sales-count">已售{{ product.sales }}件</text>
                  <text class="points-reward">+{{ product.pointsReward }}积分</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Category {
  id: number
  name: string
  icon: string
}

interface Subcategory {
  id: number
  name: string
  image: string
  categoryId: number
}

interface Product {
  id: number
  name: string
  image: string
  price: number
  originalPrice?: number
  pointsReward: number
  sales: number
  categoryId: number
}

// 响应式数据
const activeCategory = ref(1)

const categories = ref<Category[]>([
  { id: 1, name: '数码电子', icon: '📱' },
  { id: 2, name: '服装鞋包', icon: '👕' },
  { id: 3, name: '家居生活', icon: '🏠' },
  { id: 4, name: '美妆护肤', icon: '💄' },
  { id: 5, name: '运动户外', icon: '⚽' },
  { id: 6, name: '食品饮料', icon: '🍎' },
  { id: 7, name: '母婴用品', icon: '🍼' },
  { id: 8, name: '图书文具', icon: '📚' },
  { id: 9, name: '汽车用品', icon: '🚗' },
  { id: 10, name: '其他分类', icon: '📦' }
])

const subcategories = ref<Subcategory[]>([
  // 数码电子
  { id: 1, name: '手机', image: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=手机', categoryId: 1 },
  { id: 2, name: '耳机', image: 'https://via.placeholder.com/120x120/1890ff/ffffff?text=耳机', categoryId: 1 },
  { id: 3, name: '充电器', image: 'https://via.placeholder.com/120x120/52c41a/ffffff?text=充电器', categoryId: 1 },
  { id: 4, name: '数据线', image: 'https://via.placeholder.com/120x120/722ed1/ffffff?text=数据线', categoryId: 1 },
  
  // 服装鞋包
  { id: 5, name: '男装', image: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=男装', categoryId: 2 },
  { id: 6, name: '女装', image: 'https://via.placeholder.com/120x120/1890ff/ffffff?text=女装', categoryId: 2 },
  { id: 7, name: '鞋子', image: 'https://via.placeholder.com/120x120/52c41a/ffffff?text=鞋子', categoryId: 2 },
  { id: 8, name: '包包', image: 'https://via.placeholder.com/120x120/722ed1/ffffff?text=包包', categoryId: 2 },
  
  // 家居生活
  { id: 9, name: '厨具', image: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=厨具', categoryId: 3 },
  { id: 10, name: '家纺', image: 'https://via.placeholder.com/120x120/1890ff/ffffff?text=家纺', categoryId: 3 },
  { id: 11, name: '收纳', image: 'https://via.placeholder.com/120x120/52c41a/ffffff?text=收纳', categoryId: 3 },
  { id: 12, name: '装饰', image: 'https://via.placeholder.com/120x120/722ed1/ffffff?text=装饰', categoryId: 3 }
])

const products = ref<Product[]>([
  // 数码电子产品
  { id: 1, name: '智能手机', image: 'https://via.placeholder.com/300x300/ff6b35/ffffff?text=手机', price: 2999, originalPrice: 3299, pointsReward: 300, sales: 1234, categoryId: 1 },
  { id: 2, name: '蓝牙耳机', image: 'https://via.placeholder.com/300x300/1890ff/ffffff?text=耳机', price: 199, pointsReward: 20, sales: 567, categoryId: 1 },
  { id: 3, name: '无线充电器', image: 'https://via.placeholder.com/300x300/52c41a/ffffff?text=充电器', price: 89, originalPrice: 129, pointsReward: 9, sales: 890, categoryId: 1 },
  { id: 4, name: '数据线', image: 'https://via.placeholder.com/300x300/722ed1/ffffff?text=数据线', price: 29, originalPrice: 49, pointsReward: 3, sales: 2345, categoryId: 1 },
  
  // 服装鞋包产品
  { id: 5, name: '休闲T恤', image: 'https://via.placeholder.com/300x300/ff6b35/ffffff?text=T恤', price: 89, originalPrice: 129, pointsReward: 9, sales: 456, categoryId: 2 },
  { id: 6, name: '牛仔裤', image: 'https://via.placeholder.com/300x300/1890ff/ffffff?text=牛仔裤', price: 199, pointsReward: 20, sales: 234, categoryId: 2 },
  { id: 7, name: '运动鞋', image: 'https://via.placeholder.com/300x300/52c41a/ffffff?text=运动鞋', price: 299, originalPrice: 399, pointsReward: 30, sales: 678, categoryId: 2 },
  { id: 8, name: '双肩包', image: 'https://via.placeholder.com/300x300/722ed1/ffffff?text=双肩包', price: 159, pointsReward: 16, sales: 345, categoryId: 2 }
])

// 计算属性
const currentSubcategories = computed(() => {
  return subcategories.value.filter(sub => sub.categoryId === activeCategory.value)
})

const currentProducts = computed(() => {
  return products.value.filter(product => product.categoryId === activeCategory.value)
})

// 方法
const selectCategory = (category: Category) => {
  activeCategory.value = category.id
}

const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index'
  })
}

const goToProductList = (subcategoryId: number) => {
  uni.navigateTo({
    url: `/pages/product/list?subcategoryId=${subcategoryId}`
  })
}

const goToProductDetail = (product: Product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}`
  })
}
</script>

<style lang="scss" scoped>
.category-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.search-section {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  
  .search-bar {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    background-color: #f5f5f5;
    border-radius: 50rpx;
    
    .search-icon {
      font-size: 28rpx;
      margin-right: 12rpx;
      color: #999999;
    }
    
    .search-placeholder {
      font-size: 28rpx;
      color: #999999;
    }
  }
}

.category-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.category-sidebar {
  width: 200rpx;
  background-color: #ffffff;
  border-right: 1rpx solid #f0f0f0;
  
  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 20rpx;
    border-bottom: 1rpx solid #f8f8f8;
    
    &.active {
      background-color: #fff7f0;
      border-right: 4rpx solid #ff6b35;
      
      .category-name {
        color: #ff6b35;
        font-weight: 600;
      }
    }
    
    .category-icon {
      font-size: 32rpx;
      margin-bottom: 8rpx;
    }
    
    .category-name {
      font-size: 24rpx;
      color: #333333;
      text-align: center;
    }
  }
}

.products-content {
  flex: 1;
  padding: 30rpx;
}

.subcategories {
  margin-bottom: 40rpx;
  
  .subcategory-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .subcategory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    
    .subcategory-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .subcategory-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        margin-bottom: 8rpx;
      }
      
      .subcategory-name {
        font-size: 22rpx;
        color: #333333;
        text-align: center;
      }
    }
  }
}

.recommended-products {
  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    
    .product-item {
      background-color: #ffffff;
      border-radius: 12rpx;
      overflow: hidden;
      
      .product-image {
        width: 100%;
        height: 300rpx;
      }
      
      .product-info {
        padding: 20rpx;
        
        .product-name {
          display: block;
          font-size: 26rpx;
          color: #333333;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .product-price {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;
          
          .current-price {
            font-size: 28rpx;
            font-weight: 600;
            color: #ff6b35;
            margin-right: 12rpx;
          }
          
          .original-price {
            font-size: 22rpx;
            color: #999999;
            text-decoration: line-through;
          }
        }
        
        .product-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .sales-count {
            font-size: 20rpx;
            color: #999999;
          }
          
          .points-reward {
            font-size: 20rpx;
            color: #52c41a;
            background-color: #f6ffed;
            padding: 4rpx 8rpx;
            border-radius: 8rpx;
          }
        }
      }
    }
  }
}
</style>
