<template>
  <view class="product-list-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showSortModal = true">
        <text class="filter-text">{{ currentSort.name }}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" @click="showFilterModal = true">
        <text class="filter-text">筛选</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="view-toggle">
        <text class="toggle-btn" :class="{ active: viewMode === 'grid' }" @click="viewMode = 'grid'">⊞</text>
        <text class="toggle-btn" :class="{ active: viewMode === 'list' }" @click="viewMode = 'list'">☰</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <scroll-view 
      class="product-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="refreshProducts"
      :refresher-triggered="isRefreshing"
    >
      <!-- 网格视图 -->
      <view class="product-grid" v-if="viewMode === 'grid'">
        <view 
          class="grid-item" 
          v-for="product in products" 
          :key="product.id"
          @click="goToProduct(product.id)"
        >
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <view class="product-price">
              <text class="current-price">¥{{ product.price }}</text>
              <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-tags">
              <text class="tag" v-for="tag in product.tags" :key="tag">{{ tag }}</text>
            </view>
            <view class="product-stats">
              <text class="sales">销量{{ product.sales }}</text>
              <text class="rating">{{ product.rating }}分</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 列表视图 -->
      <view class="product-list" v-else>
        <view 
          class="list-item" 
          v-for="product in products" 
          :key="product.id"
          @click="goToProduct(product.id)"
        >
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc" v-if="product.description">{{ product.description }}</text>
            <view class="product-tags">
              <text class="tag" v-for="tag in product.tags" :key="tag">{{ tag }}</text>
            </view>
            <view class="product-bottom">
              <view class="product-price">
                <text class="current-price">¥{{ product.price }}</text>
                <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
              </view>
              <view class="product-stats">
                <text class="sales">销量{{ product.sales }}</text>
                <text class="rating">{{ product.rating }}分</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载更多...</text>
      </view>

      <!-- 无商品 -->
      <view class="no-products" v-if="products.length === 0 && !isLoading">
        <text class="no-products-icon">📦</text>
        <text class="no-products-text">暂无商品</text>
      </view>
    </scroll-view>

    <!-- 排序弹窗 -->
    <view class="sort-modal" v-if="showSortModal" @click="showSortModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">排序方式</text>
          <text class="close-btn" @click="showSortModal = false">✕</text>
        </view>
        <view class="sort-options">
          <view 
            class="sort-option"
            :class="{ selected: currentSort.key === option.key }"
            v-for="option in sortOptions"
            :key="option.key"
            @click="selectSort(option)"
          >
            <text class="option-text">{{ option.name }}</text>
            <text class="check-icon" v-if="currentSort.key === option.key">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <view class="filter-modal" v-if="showFilterModal" @click="showFilterModal = false">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <text class="close-btn" @click="showFilterModal = false">✕</text>
        </view>
        
        <!-- 价格筛选 -->
        <view class="filter-section">
          <text class="section-title">价格区间</text>
          <view class="price-range">
            <input type="number" v-model="priceRange.min" placeholder="最低价" class="price-input" />
            <text class="range-separator">-</text>
            <input type="number" v-model="priceRange.max" placeholder="最高价" class="price-input" />
          </view>
        </view>

        <!-- 品牌筛选 -->
        <view class="filter-section">
          <text class="section-title">品牌</text>
          <view class="brand-list">
            <view 
              class="brand-item"
              :class="{ selected: selectedBrands.includes(brand) }"
              v-for="brand in brands"
              :key="brand"
              @click="toggleBrand(brand)"
            >
              <text class="brand-text">{{ brand }}</text>
            </view>
          </view>
        </view>

        <!-- 特色筛选 -->
        <view class="filter-section">
          <text class="section-title">商品特色</text>
          <view class="feature-list">
            <view 
              class="feature-item"
              :class="{ selected: selectedFeatures.includes(feature) }"
              v-for="feature in features"
              :key="feature"
              @click="toggleFeature(feature)"
            >
              <text class="feature-text">{{ feature }}</text>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <view class="reset-btn" @click="resetFilters">
            <text class="btn-text">重置</text>
          </view>
          <view class="confirm-btn" @click="applyFilters">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onLoad } from 'vue'

interface Product {
  id: number
  name: string
  description?: string
  image: string
  price: number
  originalPrice?: number
  sales: number
  rating: number
  tags: string[]
  brand: string
  features: string[]
}

interface SortOption {
  key: string
  name: string
}

// 响应式数据
const viewMode = ref<'grid' | 'list'>('grid')
const isRefreshing = ref(false)
const isLoading = ref(false)
const hasMore = ref(true)
const showSortModal = ref(false)
const showFilterModal = ref(false)

const currentSort = ref<SortOption>({ key: 'default', name: '综合排序' })
const priceRange = ref({ min: '', max: '' })
const selectedBrands = ref<string[]>([])
const selectedFeatures = ref<string[]>([])

const sortOptions = ref<SortOption[]>([
  { key: 'default', name: '综合排序' },
  { key: 'sales', name: '销量优先' },
  { key: 'price_asc', name: '价格从低到高' },
  { key: 'price_desc', name: '价格从高到低' },
  { key: 'rating', name: '评分优先' },
  { key: 'newest', name: '最新上架' }
])

const brands = ref(['苹果', '华为', '小米', '三星', '一加', 'OPPO', 'vivo'])
const features = ref(['包邮', '现货', '新品', '热销', '限时优惠', '品牌直营'])

const products = ref<Product[]>([
  {
    id: 1,
    name: '智能手表 运动版',
    description: '健康监测 | 运动追踪 | 长续航',
    image: 'https://via.placeholder.com/300x300/ff6b35/ffffff?text=手表',
    price: 299.9,
    originalPrice: 399.9,
    sales: 1234,
    rating: 4.8,
    tags: ['热销', '新品'],
    brand: '苹果',
    features: ['包邮', '现货']
  },
  {
    id: 2,
    name: '蓝牙耳机 降噪版',
    description: '主动降噪 | 无线充电 | 高音质',
    image: 'https://via.placeholder.com/300x300/1890ff/ffffff?text=耳机',
    price: 199.9,
    sales: 856,
    rating: 4.6,
    tags: ['推荐'],
    brand: '华为',
    features: ['包邮', '热销']
  },
  {
    id: 3,
    name: '无线充电器 快充版',
    description: '15W快充 | 智能识别 | 安全保护',
    image: 'https://via.placeholder.com/300x300/52c41a/ffffff?text=充电器',
    price: 89.9,
    originalPrice: 129.9,
    sales: 2341,
    rating: 4.7,
    tags: ['限时优惠'],
    brand: '小米',
    features: ['包邮', '限时优惠']
  },
  {
    id: 4,
    name: '手机支架 桌面版',
    description: '可调角度 | 稳固支撑 | 防滑设计',
    image: 'https://via.placeholder.com/300x300/faad14/ffffff?text=支架',
    price: 29.9,
    sales: 3456,
    rating: 4.5,
    tags: ['性价比'],
    brand: '三星',
    features: ['包邮', '现货']
  }
])

// 方法
const refreshProducts = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  }, 1000)
}

const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
}

const goToProduct = (productId: number) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${productId}`
  })
}

const selectSort = (option: SortOption) => {
  currentSort.value = option
  showSortModal.value = false
  
  // 根据排序重新排列商品
  sortProducts(option.key)
}

const sortProducts = (sortKey: string) => {
  const sorted = [...products.value]
  
  switch (sortKey) {
    case 'sales':
      products.value = sorted.sort((a, b) => b.sales - a.sales)
      break
    case 'price_asc':
      products.value = sorted.sort((a, b) => a.price - b.price)
      break
    case 'price_desc':
      products.value = sorted.sort((a, b) => b.price - a.price)
      break
    case 'rating':
      products.value = sorted.sort((a, b) => b.rating - a.rating)
      break
    case 'newest':
      products.value = sorted.sort((a, b) => b.id - a.id)
      break
    default:
      // 综合排序保持原顺序
      break
  }
}

const toggleBrand = (brand: string) => {
  const index = selectedBrands.value.indexOf(brand)
  if (index > -1) {
    selectedBrands.value.splice(index, 1)
  } else {
    selectedBrands.value.push(brand)
  }
}

const toggleFeature = (feature: string) => {
  const index = selectedFeatures.value.indexOf(feature)
  if (index > -1) {
    selectedFeatures.value.splice(index, 1)
  } else {
    selectedFeatures.value.push(feature)
  }
}

const resetFilters = () => {
  priceRange.value = { min: '', max: '' }
  selectedBrands.value = []
  selectedFeatures.value = []
}

const applyFilters = () => {
  showFilterModal.value = false
  
  // 应用筛选条件
  let filtered = [...products.value]
  
  // 价格筛选
  if (priceRange.value.min) {
    filtered = filtered.filter(p => p.price >= Number(priceRange.value.min))
  }
  if (priceRange.value.max) {
    filtered = filtered.filter(p => p.price <= Number(priceRange.value.max))
  }
  
  // 品牌筛选
  if (selectedBrands.value.length > 0) {
    filtered = filtered.filter(p => selectedBrands.value.includes(p.brand))
  }
  
  // 特色筛选
  if (selectedFeatures.value.length > 0) {
    filtered = filtered.filter(p => 
      p.features.some(f => selectedFeatures.value.includes(f))
    )
  }
  
  products.value = filtered
  
  uni.showToast({
    title: '筛选完成',
    icon: 'success'
  })
}

onLoad((options) => {
  if (options?.category) {
    // 根据分类加载商品
    console.log('Category:', options.category)
  }
  if (options?.keyword) {
    // 根据关键词搜索商品
    console.log('Keyword:', options.keyword)
  }
})
</script>

<style lang="scss" scoped>
.product-list-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-bar {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .filter-item {
    display: flex;
    align-items: center;
    margin-right: 40rpx;

    .filter-text {
      font-size: 26rpx;
      color: #333333;
      margin-right: 8rpx;
    }

    .filter-arrow {
      font-size: 20rpx;
      color: #999999;
    }
  }

  .view-toggle {
    margin-left: auto;
    display: flex;
    gap: 16rpx;

    .toggle-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #999999;
      background-color: #f8f8f8;
      border-radius: 8rpx;

      &.active {
        color: #ff6b35;
        background-color: #fff7f0;
      }
    }
  }
}

.product-scroll {
  height: calc(100vh - 120rpx);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;

  .grid-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    overflow: hidden;

    .product-image {
      width: 100%;
      height: 300rpx;
    }

    .product-info {
      padding: 20rpx;

      .product-name {
        display: block;
        font-size: 26rpx;
        color: #333333;
        margin-bottom: 12rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-price {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .current-price {
          font-size: 28rpx;
          font-weight: 600;
          color: #ff6b35;
          margin-right: 12rpx;
        }

        .original-price {
          font-size: 22rpx;
          color: #999999;
          text-decoration: line-through;
        }
      }

      .product-tags {
        display: flex;
        gap: 8rpx;
        margin-bottom: 12rpx;

        .tag {
          font-size: 20rpx;
          color: #ff6b35;
          background-color: #fff7f0;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }

      .product-stats {
        display: flex;
        justify-content: space-between;

        .sales, .rating {
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
}

.product-list {
  padding: 20rpx;

  .list-item {
    display: flex;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 16rpx;

    .product-image {
      width: 200rpx;
      height: 200rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }

    .product-info {
      flex: 1;
      display: flex;
      flex-direction: column;

      .product-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 8rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-desc {
        font-size: 22rpx;
        color: #666666;
        margin-bottom: 12rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .product-tags {
        display: flex;
        gap: 8rpx;
        margin-bottom: 12rpx;

        .tag {
          font-size: 20rpx;
          color: #ff6b35;
          background-color: #fff7f0;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }

      .product-bottom {
        margin-top: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .product-price {
          display: flex;
          align-items: center;

          .current-price {
            font-size: 28rpx;
            font-weight: 600;
            color: #ff6b35;
            margin-right: 12rpx;
          }

          .original-price {
            font-size: 22rpx;
            color: #999999;
            text-decoration: line-through;
          }
        }

        .product-stats {
          display: flex;
          gap: 20rpx;

          .sales, .rating {
            font-size: 22rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;

  .load-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.no-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .no-products-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }

  .no-products-text {
    font-size: 28rpx;
    color: #666666;
  }
}

.sort-modal, .filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .modal-content {
    width: 100%;
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    max-height: 80vh;
    overflow-y: auto;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }

      .close-btn {
        font-size: 32rpx;
        color: #999999;
      }
    }
  }
}

.sort-options {
  .sort-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f8f8f8;

    &:last-child {
      border-bottom: none;
    }

    &.selected {
      .option-text {
        color: #ff6b35;
      }

      .check-icon {
        color: #ff6b35;
      }
    }

    .option-text {
      font-size: 28rpx;
      color: #333333;
    }

    .check-icon {
      font-size: 28rpx;
      font-weight: 600;
    }
  }
}

.filter-section {
  margin-bottom: 40rpx;

  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
  }

  .price-range {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .price-input {
      flex: 1;
      height: 60rpx;
      font-size: 26rpx;
      color: #333333;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 0 16rpx;
      text-align: center;
    }

    .range-separator {
      font-size: 24rpx;
      color: #999999;
    }
  }

  .brand-list, .feature-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .brand-item, .feature-item {
      padding: 12rpx 20rpx;
      background-color: #f8f8f8;
      border-radius: 20rpx;
      border: 1rpx solid transparent;

      &.selected {
        background-color: #fff7f0;
        border-color: #ff6b35;

        .brand-text, .feature-text {
          color: #ff6b35;
        }
      }

      .brand-text, .feature-text {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;

  .reset-btn, .confirm-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;

    .btn-text {
      font-size: 28rpx;
      font-weight: 600;
    }
  }

  .reset-btn {
    background-color: #f8f8f8;
    border: 1rpx solid #d9d9d9;

    .btn-text {
      color: #666666;
    }
  }

  .confirm-btn {
    background-color: #ff6b35;

    .btn-text {
      color: #ffffff;
    }
  }
}
</style>
