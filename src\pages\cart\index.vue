<template>
  <view class="cart">
    <view v-if="cartItems.length === 0" class="empty-cart">
      <view class="empty-icon">🛒</view>
      <text class="empty-text">购物车是空的</text>
      <button class="btn-go-shopping" @click="goShopping">去购物</button>
    </view>
    
    <view v-else class="cart-content">
      <view class="cart-list">
        <view 
          class="cart-item" 
          v-for="item in cartItems" 
          :key="item.id"
        >
          <checkbox 
            class="item-checkbox" 
            :checked="item.selected"
            @change="handleItemSelect(item)"
          />
          <image class="item-image" :src="item.image" mode="aspectFill" />
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-price">¥{{ item.price }}</text>
          </view>
          <view class="item-actions">
            <view class="quantity-control">
              <button class="quantity-btn" @click="decreaseQuantity(item)">-</button>
              <text class="quantity">{{ item.quantity }}</text>
              <button class="quantity-btn" @click="increaseQuantity(item)">+</button>
            </view>
          </view>
        </view>
      </view>
      
      <view class="cart-footer">
        <view class="select-all">
          <checkbox :checked="allSelected" @change="handleSelectAll" />
          <text>全选</text>
        </view>
        <view class="total-info">
          <text class="total-text">合计：¥{{ totalPrice }}</text>
          <button class="btn-checkout" @click="checkout">结算</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface CartItem {
  id: number
  name: string
  price: number
  quantity: number
  image: string
  selected: boolean
}

// 使用base64编码的占位图片，避免网络请求问题
const placeholderImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNmMGYwZjAiLz4KICA8cmVjdCB4PSI1MCIgeT0iNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjZTBlMGUwIi8+CiAgPGNpcmNsZSBjeD0iODAiIGN5PSI4MCIgcj0iMTUiIGZpbGw9IiNjY2NjY2MiLz4KICA8cGF0aCBkPSJNNjAgMTIwIEw5MCA5MCBMMTI0IDExMCBMMTQwIDkwIiBzdHJva2U9IiNjY2NjY2MiIHN0cm9rZS13aWR0aD0iMyIgZmlsbD0ibm9uZSIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iMTYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiPuWVhuWTgeWbvueJhzwvdGV4dD4KPC9zdmc+'

const cartItems = ref<CartItem[]>([
  {
    id: 1,
    name: '示例商品1',
    price: 99.9,
    quantity: 1,
    image: placeholderImage,
    selected: true
  },
  {
    id: 2,
    name: '示例商品2',
    price: 199.9,
    quantity: 2,
    image: placeholderImage,
    selected: false
  }
])

const allSelected = computed(() => {
  return cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
})

const totalPrice = computed(() => {
  return cartItems.value
    .filter(item => item.selected)
    .reduce((total, item) => total + item.price * item.quantity, 0)
    .toFixed(2)
})

const handleItemSelect = (item: CartItem) => {
  item.selected = !item.selected
}

const handleSelectAll = () => {
  const selectAll = !allSelected.value
  cartItems.value.forEach(item => {
    item.selected = selectAll
  })
}

const increaseQuantity = (item: CartItem) => {
  item.quantity++
}

const decreaseQuantity = (item: CartItem) => {
  if (item.quantity > 1) {
    item.quantity--
  }
}

const goShopping = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  })
}

const checkout = () => {
  const selectedItems = cartItems.value.filter(item => item.selected)
  if (selectedItems.length === 0) {
    uni.showToast({
      title: '请选择商品',
      icon: 'none'
    })
    return
  }
  
  uni.showToast({
    title: '跳转到结算页面',
    icon: 'success'
  })
}
</script>

<style lang="scss" scoped>
.cart {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  
  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 60rpx;
  }
  
  .btn-go-shopping {
    background-color: #007aff;
    color: #ffffff;
    border-radius: 50rpx;
    padding: 20rpx 60rpx;
    font-size: 28rpx;
    border: none;
  }
}

.cart-content {
  padding-bottom: 120rpx;
}

.cart-list {
  background-color: #ffffff;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .item-checkbox {
    margin-right: 20rpx;
  }
  
  .item-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
    background-color: #f0f0f0;
  }
  
  .item-info {
    flex: 1;
    
    .item-name {
      display: block;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 10rpx;
    }
    
    .item-price {
      font-size: 32rpx;
      color: #ff4757;
      font-weight: bold;
    }
  }
  
  .item-actions {
    .quantity-control {
      display: flex;
      align-items: center;
      
      .quantity-btn {
        width: 60rpx;
        height: 60rpx;
        border: 1rpx solid #ddd;
        background-color: #ffffff;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .quantity {
        width: 80rpx;
        text-align: center;
        font-size: 28rpx;
      }
    }
  }
}

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  
  .select-all {
    display: flex;
    align-items: center;
    
    text {
      margin-left: 10rpx;
      font-size: 28rpx;
    }
  }
  
  .total-info {
    display: flex;
    align-items: center;
    
    .total-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff4757;
      margin-right: 20rpx;
    }
    
    .btn-checkout {
      background-color: #ff4757;
      color: #ffffff;
      border-radius: 50rpx;
      padding: 15rpx 40rpx;
      font-size: 28rpx;
      border: none;
    }
  }
}
</style>
