<template>
  <view class="cart-page">
    <!-- 购物车为空 -->
    <view v-if="cartItems.length === 0" class="empty-cart">
      <image src="/static/icons/cart-empty.png" class="empty-icon" mode="aspectFit" />
      <text class="empty-text">购物车还是空的</text>
      <text class="empty-desc">快去挑选心仪的商品吧</text>
      <mall-button type="primary" @click="goShopping">去逛逛</mall-button>
    </view>
    
    <!-- 购物车列表 -->
    <view v-else class="cart-content">
      <!-- 全选栏 -->
      <view class="select-all-bar">
        <view class="select-all" @click="toggleSelectAll">
          <view class="checkbox" :class="{ 'checkbox--checked': isAllSelected }">
            <image v-if="isAllSelected" src="/static/icons/check.png" class="check-icon" mode="aspectFit" />
          </view>
          <text class="select-text">全选</text>
        </view>
        <view class="edit-button" @click="toggleEditMode">
          {{ isEditMode ? '完成' : '编辑' }}
        </view>
      </view>
      
      <!-- 商品列表 -->
      <scroll-view scroll-y class="cart-list">
        <view 
          v-for="item in cartItems" 
          :key="item.id"
          class="cart-item"
        >
          <view class="item-checkbox" @click="toggleItemSelect(item)">
            <view class="checkbox" :class="{ 'checkbox--checked': item.checked }">
              <image v-if="item.checked" src="/static/icons/check.png" class="check-icon" mode="aspectFit" />
            </view>
          </view>
          
          <image :src="item.product.images[0]" class="item-image" mode="aspectFill" />
          
          <view class="item-info">
            <view class="item-name ellipsis-2">{{ item.product.name }}</view>
            <view class="item-specs">
              <text 
                v-for="spec in item.selectedSpecs" 
                :key="spec.specId"
                class="spec-text"
              >
                {{ spec.optionName }}
              </text>
            </view>
            <view class="item-bottom">
              <view class="item-price">¥{{ item.price.toFixed(2) }}</view>
              <view class="quantity-control">
                <view 
                  class="quantity-btn"
                  :class="{ 'quantity-btn--disabled': item.quantity <= 1 }"
                  @click="updateQuantity(item, item.quantity - 1)"
                >
                  -
                </view>
                <input 
                  v-model.number="item.quantity"
                  class="quantity-input"
                  type="number"
                  @blur="handleQuantityChange(item)"
                />
                <view 
                  class="quantity-btn"
                  @click="updateQuantity(item, item.quantity + 1)"
                >
                  +
                </view>
              </view>
            </view>
          </view>
          
          <view v-if="isEditMode" class="item-delete" @click="deleteItem(item)">
            <image src="/static/icons/delete.png" class="delete-icon" mode="aspectFit" />
          </view>
        </view>
      </scroll-view>
      
      <!-- 底部结算栏 -->
      <view class="checkout-bar">
        <view class="checkout-info">
          <view class="selected-count">已选择{{ selectedCount }}件商品</view>
          <view class="total-price">
            <text class="total-label">合计：</text>
            <text class="total-amount">¥{{ totalAmount.toFixed(2) }}</text>
          </view>
        </view>
        <mall-button 
          type="primary" 
          :disabled="selectedCount === 0"
          @click="goCheckout"
        >
          结算({{ selectedCount }})
        </mall-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { CartItem } from '@/types'

// 响应式数据
const cartItems = ref<CartItem[]>([])
const isEditMode = ref(false)
const loading = ref(false)

// 计算属性
const selectedItems = computed(() => cartItems.value.filter(item => item.checked))
const selectedCount = computed(() => selectedItems.value.length)
const isAllSelected = computed(() => cartItems.value.length > 0 && selectedItems.value.length === cartItems.value.length)
const totalAmount = computed(() => selectedItems.value.reduce((sum, item) => sum + item.price * item.quantity, 0))

// 页面生命周期
onMounted(() => {
  loadCartItems()
})

// 加载购物车数据
const loadCartItems = async () => {
  try {
    loading.value = true
    // 模拟数据
    cartItems.value = [
      {
        id: '1',
        productId: '1',
        product: {
          id: '1',
          name: '春季新款连衣裙 优雅气质款 多色可选',
          description: '优质面料，舒适透气',
          price: 299.00,
          originalPrice: 399.00,
          images: ['https://picsum.photos/300/300?random=30'],
          categoryId: '1',
          categoryName: '服装',
          stock: 100,
          sales: 1234,
          rating: 4.8,
          reviewCount: 567,
          tags: ['新品', '热销'],
          specs: [],
          status: 'active',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        quantity: 1,
        selectedSpecs: [
          { specId: '1', specName: '颜色', optionId: '1-1', optionName: '红色', optionValue: 'red' },
          { specId: '2', specName: '尺码', optionId: '2-1', optionName: 'M', optionValue: 'M' }
        ],
        price: 299.00,
        checked: true,
        createdAt: '2024-01-01'
      },
      {
        id: '2',
        productId: '2',
        product: {
          id: '2',
          name: '无线蓝牙耳机 降噪版 长续航',
          description: '高品质音效，舒适佩戴',
          price: 199.00,
          images: ['https://picsum.photos/300/300?random=31'],
          categoryId: '2',
          categoryName: '数码',
          stock: 50,
          sales: 890,
          rating: 4.6,
          reviewCount: 234,
          tags: ['热销'],
          specs: [],
          status: 'active',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        quantity: 2,
        selectedSpecs: [
          { specId: '3', specName: '颜色', optionId: '3-1', optionName: '黑色', optionValue: 'black' }
        ],
        price: 199.00,
        checked: false,
        createdAt: '2024-01-01'
      }
    ]
  } catch (error) {
    console.error('加载购物车失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 事件处理
const goShopping = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  })
}

const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value
}

const toggleSelectAll = () => {
  const shouldSelectAll = !isAllSelected.value
  cartItems.value.forEach(item => {
    item.checked = shouldSelectAll
  })
}

const toggleItemSelect = (item: CartItem) => {
  item.checked = !item.checked
}

const updateQuantity = async (item: CartItem, newQuantity: number) => {
  if (newQuantity < 1) return
  
  try {
    item.quantity = newQuantity
    // 这里应该调用API更新购物车
    console.log('更新商品数量:', item.id, newQuantity)
  } catch (error) {
    console.error('更新数量失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    })
  }
}

const handleQuantityChange = (item: CartItem) => {
  if (item.quantity < 1) {
    item.quantity = 1
  }
  updateQuantity(item, item.quantity)
}

const deleteItem = async (item: CartItem) => {
  try {
    await uni.showModal({
      title: '确认删除',
      content: '确定要删除这件商品吗？'
    })
    
    const index = cartItems.value.findIndex(cartItem => cartItem.id === item.id)
    if (index > -1) {
      cartItems.value.splice(index, 1)
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
    }
  } catch (error) {
    // 用户取消删除
  }
}

const goCheckout = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请选择要结算的商品',
      icon: 'none'
    })
    return
  }
  
  const selectedItemIds = selectedItems.value.map(item => item.id)
  uni.navigateTo({
    url: `/pages/order/confirm/index?items=${selectedItemIds.join(',')}`
  })
}
</script>

<style lang="scss" scoped>
.cart-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color-page;
  
  .empty-cart {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    
    .empty-icon {
      width: 120px;
      height: 120px;
      margin-bottom: $spacing-xl;
      opacity: 0.6;
    }
    
    .empty-text {
      font-size: $font-size-lg;
      color: $text-color-primary;
      margin-bottom: $spacing-sm;
    }
    
    .empty-desc {
      font-size: $font-size-base;
      color: $text-color-secondary;
      margin-bottom: $spacing-xl;
    }
  }
  
  .cart-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .select-all-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    background-color: $bg-color-white;
    border-bottom: 1px solid $border-color-light;
    
    .select-all {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .select-text {
        margin-left: $spacing-sm;
        color: $text-color-primary;
      }
    }
    
    .edit-button {
      color: $primary-color;
      cursor: pointer;
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .cart-list {
    flex: 1;
    background-color: $bg-color-white;
  }
  
  .cart-item {
    display: flex;
    align-items: flex-start;
    padding: $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    
    .item-checkbox {
      margin-right: $spacing-lg;
      cursor: pointer;
    }
    
    .checkbox {
      width: 20px;
      height: 20px;
      border: 2px solid $border-color-base;
      border-radius: $border-radius-small;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $animation-duration-base;
      
      &--checked {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      .check-icon {
        width: 12px;
        height: 12px;
      }
    }
    
    .item-image {
      width: 80px;
      height: 80px;
      border-radius: $border-radius-base;
      margin-right: $spacing-lg;
      background-color: $bg-color-gray;
    }
    
    .item-info {
      flex: 1;
      
      .item-name {
        font-size: $font-size-base;
        color: $text-color-primary;
        line-height: 1.4;
        margin-bottom: $spacing-xs;
      }
      
      .item-specs {
        display: flex;
        gap: $spacing-xs;
        margin-bottom: $spacing-sm;
        
        .spec-text {
          padding: 2px $spacing-xs;
          background-color: $bg-color-gray;
          color: $text-color-secondary;
          font-size: $font-size-xs;
          border-radius: $border-radius-small;
        }
      }
      
      .item-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .item-price {
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          color: $primary-color;
        }
        
        .quantity-control {
          display: flex;
          align-items: center;
          border: 1px solid $border-color-base;
          border-radius: $border-radius-small;
          
          .quantity-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: $bg-color-gray;
            cursor: pointer;
            user-select: none;
            
            &--disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
            
            &:active:not(.quantity-btn--disabled) {
              background-color: darken($bg-color-gray, 10%);
            }
          }
          
          .quantity-input {
            width: 50px;
            height: 32px;
            text-align: center;
            border: none;
            outline: none;
            font-size: $font-size-base;
          }
        }
      }
    }
    
    .item-delete {
      margin-left: $spacing-lg;
      cursor: pointer;
      
      .delete-icon {
        width: 24px;
        height: 24px;
      }
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .checkout-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    background-color: $bg-color-white;
    border-top: 1px solid $border-color-light;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    
    .checkout-info {
      flex: 1;
      
      .selected-count {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        margin-bottom: $spacing-xs;
      }
      
      .total-price {
        .total-label {
          font-size: $font-size-base;
          color: $text-color-primary;
        }
        
        .total-amount {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $primary-color;
        }
      }
    }
  }
}
</style>
