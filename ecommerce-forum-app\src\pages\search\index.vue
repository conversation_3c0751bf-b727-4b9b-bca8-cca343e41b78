<template>
  <view class="search-container">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="search-input-wrapper">
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索商品、帖子..."
          class="search-input"
          @confirm="performSearch"
          focus
        />
        <text class="search-btn" @click="performSearch">搜索</text>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>

    <!-- 搜索类型切换 -->
    <view class="search-tabs" v-if="hasSearched">
      <view 
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        v-for="tab in searchTabs"
        :key="tab.key"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <text class="tab-count" v-if="tab.count > 0">({{ tab.count }})</text>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="search-results" v-if="hasSearched">
      <!-- 商品结果 -->
      <view class="products-results" v-if="activeTab === 'products'">
        <view class="results-header">
          <text class="results-title">商品 ({{ productResults.length }})</text>
          <view class="sort-options">
            <text 
              class="sort-item"
              :class="{ active: productSort === sort.key }"
              v-for="sort in productSorts"
              :key="sort.key"
              @click="changeProductSort(sort.key)"
            >
              {{ sort.name }}
            </text>
          </view>
        </view>
        
        <view class="product-list">
          <view 
            class="product-item" 
            v-for="product in productResults" 
            :key="product.id"
            @click="goToProduct(product.id)"
          >
            <image :src="product.image" class="product-image" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name" v-html="highlightKeyword(product.name)"></text>
              <view class="product-tags">
                <text class="tag" v-for="tag in product.tags" :key="tag">{{ tag }}</text>
              </view>
              <view class="product-price">
                <text class="current-price">¥{{ product.price }}</text>
                <text class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
              </view>
              <view class="product-stats">
                <text class="sales">销量{{ product.sales }}</text>
                <text class="rating">{{ product.rating }}分</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 帖子结果 -->
      <view class="posts-results" v-if="activeTab === 'posts'">
        <view class="results-header">
          <text class="results-title">帖子 ({{ postResults.length }})</text>
          <view class="sort-options">
            <text 
              class="sort-item"
              :class="{ active: postSort === sort.key }"
              v-for="sort in postSorts"
              :key="sort.key"
              @click="changePostSort(sort.key)"
            >
              {{ sort.name }}
            </text>
          </view>
        </view>
        
        <view class="post-list">
          <view 
            class="post-item" 
            v-for="post in postResults" 
            :key="post.id"
            @click="goToPost(post.id)"
          >
            <view class="post-header">
              <image :src="post.avatar" class="author-avatar" mode="aspectFill" />
              <view class="author-info">
                <text class="author-name">{{ post.author }}</text>
                <text class="post-time">{{ formatTime(post.createTime) }}</text>
              </view>
              <view class="post-category">
                <text class="category-text">{{ post.category }}</text>
              </view>
            </view>
            
            <text class="post-title" v-html="highlightKeyword(post.title)"></text>
            <text class="post-content" v-html="highlightKeyword(post.content)"></text>
            
            <view class="post-images" v-if="post.images && post.images.length > 0">
              <image 
                v-for="(image, index) in post.images.slice(0, 3)" 
                :key="index"
                :src="image" 
                class="post-image" 
                mode="aspectFill" 
              />
            </view>
            
            <view class="post-stats">
              <text class="stat-item">👍 {{ post.likes }}</text>
              <text class="stat-item">💬 {{ post.comments }}</text>
              <text class="stat-item">👁️ {{ post.views }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 无结果 -->
      <view class="no-results" v-if="!hasResults">
        <text class="no-results-icon">🔍</text>
        <text class="no-results-text">没有找到相关内容</text>
        <text class="no-results-tip">试试其他关键词吧</text>
      </view>
    </view>

    <!-- 搜索历史和热门搜索 -->
    <view class="search-suggestions" v-else>
      <!-- 搜索历史 -->
      <view class="search-history" v-if="searchHistory.length > 0">
        <view class="history-header">
          <text class="history-title">搜索历史</text>
          <text class="clear-btn" @click="clearHistory">清空</text>
        </view>
        <view class="history-list">
          <text 
            class="history-item"
            v-for="(item, index) in searchHistory"
            :key="index"
            @click="searchHistoryItem(item)"
          >
            {{ item }}
          </text>
        </view>
      </view>

      <!-- 热门搜索 -->
      <view class="hot-search">
        <view class="hot-header">
          <text class="hot-title">热门搜索</text>
        </view>
        <view class="hot-list">
          <text 
            class="hot-item"
            :class="{ highlight: index < 3 }"
            v-for="(item, index) in hotSearches"
            :key="index"
            @click="searchHotItem(item)"
          >
            <text class="hot-rank">{{ index + 1 }}</text>
            {{ item }}
          </text>
        </view>
      </view>

      <!-- 搜索发现 -->
      <view class="search-discovery">
        <view class="discovery-header">
          <text class="discovery-title">搜索发现</text>
        </view>
        <view class="discovery-list">
          <view 
            class="discovery-item"
            v-for="item in discoveryItems"
            :key="item.id"
            @click="searchDiscoveryItem(item.keyword)"
          >
            <image :src="item.image" class="discovery-image" mode="aspectFill" />
            <view class="discovery-info">
              <text class="discovery-name">{{ item.name }}</text>
              <text class="discovery-desc">{{ item.desc }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onLoad } from 'vue'

interface Product {
  id: number
  name: string
  image: string
  price: number
  originalPrice?: number
  sales: number
  rating: number
  tags: string[]
}

interface Post {
  id: number
  title: string
  content: string
  author: string
  avatar: string
  category: string
  createTime: string
  likes: number
  comments: number
  views: number
  images?: string[]
}

interface DiscoveryItem {
  id: number
  name: string
  desc: string
  image: string
  keyword: string
}

// 响应式数据
const searchKeyword = ref('')
const hasSearched = ref(false)
const activeTab = ref('products')
const productSort = ref('default')
const postSort = ref('default')

const searchTabs = ref([
  { key: 'products', name: '商品', count: 0 },
  { key: 'posts', name: '帖子', count: 0 }
])

const productSorts = ref([
  { key: 'default', name: '综合' },
  { key: 'sales', name: '销量' },
  { key: 'price', name: '价格' },
  { key: 'rating', name: '评分' }
])

const postSorts = ref([
  { key: 'default', name: '综合' },
  { key: 'time', name: '时间' },
  { key: 'hot', name: '热度' },
  { key: 'likes', name: '点赞' }
])

const productResults = ref<Product[]>([])
const postResults = ref<Post[]>([])

const searchHistory = ref<string[]>(['智能手表', '蓝牙耳机', '运动鞋'])

const hotSearches = ref([
  '新年特惠', '智能家居', '运动装备', '美妆护肤', '数码配件',
  '家居用品', '服装搭配', '健康养生', '美食推荐', '旅行攻略'
])

const discoveryItems = ref<DiscoveryItem[]>([
  {
    id: 1,
    name: '春季新品上市',
    desc: '时尚潮流，焕新登场',
    image: 'https://via.placeholder.com/120x80/ff6b35/ffffff?text=春季',
    keyword: '春季新品'
  },
  {
    id: 2,
    name: '数码好物推荐',
    desc: '科技改变生活',
    image: 'https://via.placeholder.com/120x80/1890ff/ffffff?text=数码',
    keyword: '数码产品'
  },
  {
    id: 3,
    name: '居家生活必备',
    desc: '品质生活从这里开始',
    image: 'https://via.placeholder.com/120x80/52c41a/ffffff?text=居家',
    keyword: '居家用品'
  }
])

// 计算属性
const hasResults = computed(() => {
  if (activeTab.value === 'products') {
    return productResults.value.length > 0
  } else {
    return postResults.value.length > 0
  }
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const performSearch = () => {
  if (!searchKeyword.value.trim()) return
  
  hasSearched.value = true
  
  // 添加到搜索历史
  const keyword = searchKeyword.value.trim()
  if (!searchHistory.value.includes(keyword)) {
    searchHistory.value.unshift(keyword)
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10)
    }
  }
  
  // 模拟搜索结果
  mockSearchResults(keyword)
}

const mockSearchResults = (keyword: string) => {
  // 模拟商品搜索结果
  productResults.value = [
    {
      id: 1,
      name: `智能${keyword}手表 运动版`,
      image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=手表',
      price: 299.9,
      originalPrice: 399.9,
      sales: 1234,
      rating: 4.8,
      tags: ['热销', '新品']
    },
    {
      id: 2,
      name: `蓝牙${keyword}耳机 降噪版`,
      image: 'https://via.placeholder.com/200x200/1890ff/ffffff?text=耳机',
      price: 199.9,
      sales: 856,
      rating: 4.6,
      tags: ['推荐']
    }
  ]
  
  // 模拟帖子搜索结果
  postResults.value = [
    {
      id: 1,
      title: `${keyword}使用心得分享`,
      content: `最近入手了这款${keyword}，使用体验非常不错，特别是...`,
      author: '数码达人',
      avatar: 'https://via.placeholder.com/60x60/ff6b35/ffffff?text=头像',
      category: '经验分享',
      createTime: '2024-01-02 15:30:00',
      likes: 128,
      comments: 45,
      views: 1256,
      images: ['https://via.placeholder.com/200x200/ff6b35/ffffff?text=图1']
    }
  ]
  
  // 更新标签页计数
  searchTabs.value[0].count = productResults.value.length
  searchTabs.value[1].count = postResults.value.length
}

const switchTab = (tabKey: string) => {
  activeTab.value = tabKey
}

const changeProductSort = (sortKey: string) => {
  productSort.value = sortKey
  // 重新排序商品结果
}

const changePostSort = (sortKey: string) => {
  postSort.value = sortKey
  // 重新排序帖子结果
}

const highlightKeyword = (text: string) => {
  if (!searchKeyword.value) return text
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<span style="color: #ff6b35; font-weight: 600;">$1</span>')
}

const goToProduct = (productId: number) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${productId}`
  })
}

const goToPost = (postId: number) => {
  uni.navigateTo({
    url: `/pages/forum/detail?id=${postId}`
  })
}

const searchHistoryItem = (keyword: string) => {
  searchKeyword.value = keyword
  performSearch()
}

const searchHotItem = (keyword: string) => {
  searchKeyword.value = keyword
  performSearch()
}

const searchDiscoveryItem = (keyword: string) => {
  searchKeyword.value = keyword
  performSearch()
}

const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = []
      }
    }
  })
}

const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return time.toLocaleDateString()
}

onLoad((options) => {
  if (options?.keyword) {
    searchKeyword.value = decodeURIComponent(options.keyword)
    performSearch()
  }
})
</script>

<style lang="scss" scoped>
.search-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-header {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx 30rpx;

  .search-input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #f8f8f8;
    border-radius: 30rpx;
    padding: 0 20rpx;
    margin-right: 20rpx;

    .search-input {
      flex: 1;
      height: 60rpx;
      font-size: 26rpx;
      color: #333333;
    }

    .search-btn {
      font-size: 26rpx;
      color: #ff6b35;
      padding: 8rpx 16rpx;
    }
  }

  .cancel-btn {
    font-size: 26rpx;
    color: #666666;
  }
}

.search-tabs {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;

    &.active {
      border-bottom: 4rpx solid #ff6b35;

      .tab-text {
        color: #ff6b35;
        font-weight: 600;
      }
    }

    .tab-text {
      font-size: 28rpx;
      color: #666666;
      margin-right: 8rpx;
    }

    .tab-count {
      font-size: 22rpx;
      color: #999999;
    }
  }
}

.search-results {
  padding: 20rpx;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;

  .results-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
  }

  .sort-options {
    display: flex;
    gap: 20rpx;

    .sort-item {
      font-size: 24rpx;
      color: #666666;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;

      &.active {
        color: #ff6b35;
        background-color: #fff7f0;
      }
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 16rpx;

    .product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }

    .product-info {
      flex: 1;

      .product-name {
        font-size: 26rpx;
        color: #333333;
        line-height: 1.4;
        margin-bottom: 12rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-tags {
        display: flex;
        gap: 8rpx;
        margin-bottom: 12rpx;

        .tag {
          font-size: 20rpx;
          color: #ff6b35;
          background-color: #fff7f0;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }

      .product-price {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .current-price {
          font-size: 28rpx;
          font-weight: 600;
          color: #ff6b35;
          margin-right: 12rpx;
        }

        .original-price {
          font-size: 22rpx;
          color: #999999;
          text-decoration: line-through;
        }
      }

      .product-stats {
        display: flex;
        gap: 20rpx;

        .sales, .rating {
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
}

.post-list {
  .post-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 16rpx;

    .post-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .author-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }

      .author-info {
        flex: 1;

        .author-name {
          display: block;
          font-size: 24rpx;
          color: #333333;
          margin-bottom: 4rpx;
        }

        .post-time {
          font-size: 20rpx;
          color: #999999;
        }
      }

      .post-category {
        .category-text {
          font-size: 20rpx;
          color: #ff6b35;
          background-color: #fff7f0;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }
    }

    .post-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;
      margin-bottom: 12rpx;
    }

    .post-content {
      display: block;
      font-size: 24rpx;
      color: #666666;
      line-height: 1.5;
      margin-bottom: 16rpx;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }

    .post-images {
      display: flex;
      gap: 8rpx;
      margin-bottom: 16rpx;

      .post-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 8rpx;
      }
    }

    .post-stats {
      display: flex;
      gap: 30rpx;

      .stat-item {
        font-size: 22rpx;
        color: #999999;
      }
    }
  }
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .no-results-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.3;
  }

  .no-results-text {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 12rpx;
  }

  .no-results-tip {
    font-size: 24rpx;
    color: #999999;
  }
}

.search-suggestions {
  padding: 30rpx;
}

.search-history, .hot-search, .search-discovery {
  margin-bottom: 40rpx;

  .history-header, .hot-header, .discovery-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .history-title, .hot-title, .discovery-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }

    .clear-btn {
      font-size: 24rpx;
      color: #999999;
    }
  }

  .history-list, .hot-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .history-item, .hot-item {
      font-size: 24rpx;
      color: #666666;
      background-color: #f8f8f8;
      padding: 12rpx 20rpx;
      border-radius: 20rpx;

      &.highlight {
        background-color: #fff7f0;
        color: #ff6b35;
      }
    }

    .hot-item {
      display: flex;
      align-items: center;

      .hot-rank {
        font-size: 20rpx;
        font-weight: 600;
        margin-right: 8rpx;
        min-width: 24rpx;
      }
    }
  }

  .discovery-list {
    .discovery-item {
      display: flex;
      align-items: center;
      background-color: #ffffff;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 16rpx;

      .discovery-image {
        width: 120rpx;
        height: 80rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .discovery-info {
        flex: 1;

        .discovery-name {
          display: block;
          font-size: 26rpx;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .discovery-desc {
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
}
</style>
