"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const categories = common_vendor.ref([
      { id: 1, name: "服装鞋帽", icon: "👕" },
      { id: 2, name: "数码电器", icon: "📱" },
      { id: 3, name: "食品饮料", icon: "🍎" },
      { id: 4, name: "美妆护肤", icon: "💄" },
      { id: 5, name: "家居用品", icon: "🏠" },
      { id: 6, name: "运动户外", icon: "⚽" },
      { id: 7, name: "图书文具", icon: "📚" },
      { id: 8, name: "母婴用品", icon: "🍼" }
    ]);
    const filteredCategories = common_vendor.computed(() => {
      if (!searchKeyword.value) {
        return categories.value;
      }
      return categories.value.filter(
        (category) => category.name.includes(searchKeyword.value)
      );
    });
    const handleSearch = (e) => {
      searchKeyword.value = e.detail.value;
    };
    const handleCategoryClick = (category) => {
      common_vendor.index.showToast({
        title: `选择了${category.name}`,
        icon: "success"
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, handleSearch]),
        b: searchKeyword.value,
        c: common_vendor.f(filteredCategories.value, (category, k0, i0) => {
          return {
            a: common_vendor.t(category.icon),
            b: common_vendor.t(category.name),
            c: category.id,
            d: common_vendor.o(($event) => handleCategoryClick(category), category.id)
          };
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f0c2a821"]]);
wx.createPage(MiniProgramPage);
