import type { Scope, SourceCode } from '../ts-eslint';
import type { RuleContext } from '../ts-eslint/Rule';
import type { TSESTree } from '../ts-estree';
/** @deprecated use `context.sourceCode.getAncestors(node)` */
export declare function getAncestors(context: Readonly<RuleContext<string, unknown[]>>): TSESTree.Node[];
/** @deprecated use `context.sourceCode.getCwd()` */
export declare function getCwd(context: Readonly<RuleContext<string, unknown[]>>): string;
/** @deprecated use `context.sourceCode.getDeclaredVariables(node)` */
export declare function getDeclaredVariables(context: Readonly<RuleContext<string, unknown[]>>, node: TSESTree.Node): readonly Scope.Variable[];
/** @deprecated use `context.filename` */
export declare function getFilename(context: Readonly<RuleContext<string, unknown[]>>): string;
/** @deprecated use `context.sourceCode.getScope(node) */
export declare function getScope(context: Readonly<RuleContext<string, readonly unknown[]>>): Scope.Scope;
/** @deprecated use `context.sourceCode` */
export declare function getSourceCode(context: Readonly<RuleContext<string, readonly unknown[]>>): Readonly<SourceCode>;
//# sourceMappingURL=context.d.ts.map