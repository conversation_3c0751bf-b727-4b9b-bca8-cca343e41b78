"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniAppCssPlugin = void 0;
const path_1 = __importDefault(require("path"));
const picocolors_1 = __importDefault(require("picocolors"));
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const uni_nvue_styler_1 = require("@dcloudio/uni-nvue-styler");
const descriptorCache_1 = require("./uvue/descriptorCache");
const utils_1 = require("./utils");
function uniAppCssPlugin() {
    let resolvedConfig;
    const name = 'uni:app-uvue-css';
    const descriptorOptions = {
        ...(0, descriptorCache_1.getResolvedOptions)(),
        sourceMap: false,
    };
    return {
        name,
        apply: 'build',
        configResolved(config) {
            resolvedConfig = config;
            const uvueCssPostPlugin = (0, uni_cli_shared_1.cssPostPlugin)(config, {
                isJsCode: true,
                platform: process.env.UNI_PLATFORM,
                includeComponentCss: false,
                chunkCssFilename(id) {
                    const { filename } = (0, uni_cli_shared_1.parseVueRequest)(id);
                    if ((0, utils_1.isVue)(filename)) {
                        return (0, uni_cli_shared_1.normalizeNodeModules)((path_1.default.isAbsolute(filename)
                            ? path_1.default.relative(process.env.UNI_INPUT_DIR, filename)
                            : filename) + '.style.uts');
                    }
                },
                async chunkCssCode(filename, cssCode) {
                    cssCode = (0, uni_cli_shared_1.parseAssets)(resolvedConfig, cssCode);
                    const { code, messages } = await (0, uni_nvue_styler_1.parse)(cssCode, {
                        filename,
                        logLevel: 'ERROR',
                        mapOf: 'utsMapOf',
                        chunk: 100,
                        type: 'uvue',
                        platform: process.env.UNI_UTS_PLATFORM,
                        trim: true,
                    });
                    messages.forEach((message) => {
                        if (message.type === 'error') {
                            let msg = `[plugin:uni:app-uvue-css] ${message.text}`;
                            if (message.line && message.column) {
                                msg += `\n${(0, uni_cli_shared_1.generateCodeFrame)(cssCode, {
                                    line: message.line,
                                    column: message.column,
                                }).replace(/\t/g, ' ')}`;
                            }
                            msg += `\n${(0, uni_cli_shared_1.formatAtFilename)(filename)}`;
                            resolvedConfig.logger.error(picocolors_1.default.red(msg));
                        }
                    });
                    return `export const ${(0, uni_cli_shared_1.genUTSClassName)(filename.replace('.style.uts', ''))}Styles = ${code}`;
                },
            });
            // 增加 css plugins
            // TODO 加密插件
            (0, uni_cli_shared_1.insertBeforePlugin)((0, uni_cli_shared_1.cssPlugin)(config, {
                isAndroidX: true,
                // android 不处理 css url
                // createUrlReplacer:
                //   process.env.UNI_COMPILE_TARGET === 'uni_modules'
                //     ? createEncryptCssUrlReplacer
                //     : undefined,
                getDescriptor: (filename) => {
                    return (0, descriptorCache_1.getDescriptor)(filename, descriptorOptions, false);
                },
            }), name, config);
            const plugins = config.plugins;
            const index = plugins.findIndex((p) => p.name === 'uni:app-uvue');
            plugins.splice(index, 0, uvueCssPostPlugin);
        },
        async transform(source, filename) {
            if (!uni_cli_shared_1.cssLangRE.test(filename) || uni_cli_shared_1.commonjsProxyRE.test(filename)) {
                return;
            }
            if (source.includes('#endif')) {
                source = (0, uni_cli_shared_1.preUVueCss)(source);
            }
            source = (0, uni_cli_shared_1.parseAssets)(resolvedConfig, source);
            // 仅做校验使用
            const { messages } = await (0, uni_nvue_styler_1.parse)(source, {
                filename,
                logLevel: 'WARNING',
                map: true,
                ts: true,
                noCode: true,
                type: 'uvue',
                platform: process.env.UNI_UTS_PLATFORM,
            });
            messages.forEach((message) => {
                if (message.type === 'warning') {
                    // 拆分成多行，第一行输出信息（有颜色），后续输出错误代码+文件行号
                    resolvedConfig.logger.warn(picocolors_1.default.yellow(`[plugin:uni:app-uvue-css] ${message.text}`));
                    let msg = '';
                    if (message.line && message.column) {
                        msg += `\n${(0, uni_cli_shared_1.generateCodeFrame)(source, {
                            line: message.line,
                            column: message.column,
                        }).replace(/\t/g, ' ')}\n`;
                    }
                    msg += `${(0, uni_cli_shared_1.formatAtFilename)(filename)}`;
                    resolvedConfig.logger.warn(msg);
                }
            });
            return { code: source };
        },
    };
}
exports.uniAppCssPlugin = uniAppCssPlugin;
