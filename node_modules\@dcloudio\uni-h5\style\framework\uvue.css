html,
body {
  line-height: 1.2;
  font-family: -apple-system, HelveticaNeue;
}

body {
  background-color: var(--background-color-content);
}

uni-app uni-page-body {
  height: 100%;
  overflow: visible;
  position: relative;
}

uni-app uni-page-body,
uni-app uni-top-window,
uni-app uni-left-window,
uni-app uni-right-window {
  position: relative;
}

uni-app uni-left-window .uni-left-window,
uni-app uni-right-window .uni-right-window {
  height: 100%;
}

uni-app.uni-app--showtabbar uni-page-body::after {
  content: '';
  display: block;
  width: 100%;
  height: var(--tab-bar-height);
  height: calc(var(--tab-bar-height) + constant(safe-area-inset-bottom));
  height: calc(var(--tab-bar-height) + env(safe-area-inset-bottom));
  min-height: var(--tab-bar-height);
  min-height: calc(var(--tab-bar-height) + constant(safe-area-inset-bottom));
  min-height: calc(var(--tab-bar-height) + env(safe-area-inset-bottom));
}

uni-app.uni-app--showtabbar uni-page-wrapper::after {
  display: none;
}

uni-app uni-text,
uni-app uni-input,
uni-app uni-textarea,
uni-app uni-view {
  font-size: 16px;
  text-align: left;
}

/* 修正box-sizing: border-box;负面效果 */
uni-app uni-input {
  height: auto;
}

/* 文字容器 */
uni-app uni-text,
uni-app uni-textarea,
uni-app uni-label {
  line-height: 1.2;
  overflow: hidden;
  flex-basis: auto;
  letter-spacing: 0;
}

uni-app uni-text,
uni-app uni-label {
  letter-spacing: 0;
}

/* uni-app uni-* */
uni-app uni-ad-draw,
uni-app uni-ad-fullscreen-video,
uni-app uni-ad-interactive,
uni-app uni-ad-interstitial,
uni-app uni-ad-rewarded-video,
uni-app uni-ad,
uni-app uni-animation-view,
uni-app uni-audio,
uni-app uni-block,
uni-app uni-button,
uni-app uni-camera,
uni-app uni-canvas,
uni-app uni-checkbox-group,
uni-app uni-checkbox,
uni-app uni-cover-image,
uni-app uni-cover-view,
uni-app uni-custom-tab-bar,
uni-app uni-editor,
uni-app uni-form,
uni-app uni-icon,
uni-app uni-image,
uni-app uni-input,
uni-app uni-label,
uni-app uni-list-item,
uni-app uni-list-view,
uni-app uni-live-player,
uni-app uni-live-pusher,
uni-app uni-map,
uni-app uni-match-media,
uni-app uni-movable-area,
uni-app uni-movable-view,
uni-app uni-navigation-bar,
uni-app uni-navigator,
uni-app uni-open-data,
uni-app uni-page-meta,
/* uni-app uni-picker-view-column, */
uni-app uni-picker-view,
uni-app uni-picker,
uni-app uni-progress,
uni-app uni-radio-group,
uni-app uni-radio,
uni-app uni-rich-text,
uni-app uni-scroll-view,
uni-app uni-slider,
uni-app uni-sticky-header,
uni-app uni-sticky-section,
uni-app uni-swiper-item,
uni-app uni-swiper,
uni-app uni-switch,
uni-app uni-template,
uni-app uni-text,
uni-app uni-textarea,
uni-app uni-unicloud-db,
uni-app uni-video,
uni-app uni-view,
uni-app uni-web-view {
  position: relative;
  box-sizing: border-box;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  /* 默认情况下，元素不会缩短至小于内容框尺寸，若想改变这一状况，请设置元素的min-width 与 min-height属性。https://developer.mozilla.org/zh-CN/docs/Web/CSS/flex */
  min-height: 0px;
  min-width: 0px;
  border-width: medium;
}

uni-app uni-text {
  display: inline;
}

/* 与开发者元素接触的容器 */
uni-app uni-page-body,
uni-app uni-left-window .uni-left-window,
uni-app uni-right-window .uni-right-window,
uni-app uni-top-window .uni-top-window,
uni-app uni-animation-view,
uni-app uni-block,
uni-app uni-view,
uni-app uni-label,
uni-app uni-swiper,
uni-app uni-swiper-item,
uni-app uni-movable-area,
uni-app uni-movable-view,
uni-app uni-cover-view,
uni-app uni-list-item,
uni-app uni-navigator,
uni-app uni-radio-group,
uni-app uni-checkbox-group,
uni-app uni-scroll-view,
uni-app uni-list-view,
uni-app uni-unicloud-db,
uni-app uni-custom-tab-bar,
uni-app uni-sticky-section,
uni-app uni-sticky-header {
  display: flex;
  align-items: stretch;
  align-content: stretch;
  justify-content: flex-start;
}

/* 此样式可被manifest.json内配置的flex-direction重置 */
uni-app uni-page-body,
uni-app uni-left-window .uni-left-window,
uni-app uni-right-window .uni-right-window,
uni-app uni-top-window .uni-top-window,
uni-app uni-view,
uni-app uni-label,
uni-app uni-swiper,
uni-app uni-swiper-item,
uni-app uni-movable-area,
uni-app uni-movable-view,
uni-app uni-cover-view,
uni-app uni-list-item,
uni-app uni-navigator,
uni-app uni-radio-group,
uni-app uni-checkbox-group,
uni-app uni-scroll-view,
uni-app uni-list-view,
uni-app uni-unicloud-db,
uni-app uni-sticky-section,
uni-app uni-sticky-header {
  flex-direction: column;
}

/* 与开发者元素接触的非滚动容器 */
uni-app uni-view,
uni-app uni-label,
uni-app uni-swiper,
uni-app uni-swiper-item,
uni-app uni-movable-area,
uni-app uni-movable-view,
uni-app uni-cover-view,
uni-app uni-list-item,
uni-app uni-navigator,
uni-app uni-radio-group,
uni-app uni-checkbox-group {
  overflow: hidden;
}

uni-app uni-button {
  margin: 0;
}

/* 透传scroll-view的flex-direction样式 */
uni-app uni-scroll-view {
  width: auto;
}

uni-app .uni-scroll-view,
uni-app .uni-scroll-view-content,
uni-app .uni-list-view-visible,
uni-app uni-navigator a {
  flex-direction: inherit;
  align-items: inherit;
  align-content: inherit;
  justify-content: inherit;
}

uni-app uni-navigator a {
  display: flex;
  width: 100%;
  height: 100%;
}

uni-app .uni-scroll-view-content,
uni-app .uni-sticky-header-wrapper {
  display: flex;
  min-height: 0px;
}

uni-app uni-swiper-item {
  position: absolute;
}

/* 设置默认的光标颜色 */
uni-app .uni-input-input,
uni-app .uni-textarea-textarea {
  caret-color: #3393e2;
}

uni-app .uni-input-input:disabled,
uni-app .uni-textarea-textarea:disabled {
  cursor: inherit;
}

/* 覆盖 video 的 poster 背景 */
uni-app .uni-video-cover {
  background-color: unset;
}

/**
 * 界面相关api样式
 */
body .uni-toast {
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background-color: #4c4c4c;
}

body .uni-toast__icon {
  margin: 8px 0px;
}

body .uni-toast__content {
  margin: 0px;
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8)
}

body .uni-modal {
  border-radius: 12px;
}

body .uni-modal__hd {
  padding: 32px 24px 16px;
}

body .uni-modal__title {
  font-weight: 700;
  font-size: 17px;
  line-height: 1.4;
  -webkit-line-clamp: unset;
}

body .uni-modal__bd {
  font-size: 17px;
  padding: 0 24px;
  margin-bottom: 32px;
  min-height: unset;
  max-height: 300px;
  overflow: hidden auto;
  hyphens: auto;
}

body .uni-modal__textarea {
  display: block;
  width: 100%;
  height: 48px;
  font-size: 17px;
  line-height: 24px;
  padding: 0px 8px;
}

body .uni-modal__ft {
  line-height: 56px;
  font-size: 17px;
}

body .uni-modal__btn {
  font-weight: 700;
}

body .uni-modal__btn_primary {
  color: #576b95;
}

@media screen and (max-width: 500px) {
  body .uni-actionsheet {
    left: 0px;
    right: 0px;
    bottom: 0px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    background-color: #f7f7f7;
    overflow: hidden;
  }

  body .uni-actionsheet__menu {
    border-radius: 0px;
    background-color: #ffffff;
  }

  body .uni-actionsheet__action {
    border-radius: 0px;
    margin-top: 8px;
  }

  body .uni-actionsheet__cell,
  body .uni-actionsheet__title {
    padding: 16px;
    line-height: 24px;
  }

  body .uni-actionsheet__cell {
    font-size: 17px;
  }

  body .uni-actionsheet__title {
    font-size: 14px;
    color: #747474;
  }
}