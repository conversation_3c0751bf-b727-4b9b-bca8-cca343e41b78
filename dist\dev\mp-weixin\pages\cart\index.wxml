<view class="cart data-v-3277fd7b"><view wx:if="{{a}}" class="empty-cart data-v-3277fd7b"><view class="empty-icon data-v-3277fd7b">🛒</view><text class="empty-text data-v-3277fd7b">购物车是空的</text><button class="btn-go-shopping data-v-3277fd7b" bindtap="{{b}}">去购物</button></view><view wx:else class="cart-content data-v-3277fd7b"><view class="cart-list data-v-3277fd7b"><view wx:for="{{c}}" wx:for-item="item" wx:key="i" class="cart-item data-v-3277fd7b"><checkbox class="item-checkbox data-v-3277fd7b" checked="{{item.a}}" bindchange="{{item.b}}"/><image class="item-image data-v-3277fd7b" src="{{item.c}}" mode="aspectFill"/><view class="item-info data-v-3277fd7b"><text class="item-name data-v-3277fd7b">{{item.d}}</text><text class="item-price data-v-3277fd7b">¥{{item.e}}</text></view><view class="item-actions data-v-3277fd7b"><view class="quantity-control data-v-3277fd7b"><button class="quantity-btn data-v-3277fd7b" bindtap="{{item.f}}">-</button><text class="quantity data-v-3277fd7b">{{item.g}}</text><button class="quantity-btn data-v-3277fd7b" bindtap="{{item.h}}">+</button></view></view></view></view><view class="cart-footer data-v-3277fd7b"><view class="select-all data-v-3277fd7b"><checkbox class="data-v-3277fd7b" checked="{{d}}" bindchange="{{e}}"/><text class="data-v-3277fd7b">全选</text></view><view class="total-info data-v-3277fd7b"><text class="total-text data-v-3277fd7b">合计：¥{{f}}</text><button class="btn-checkout data-v-3277fd7b" bindtap="{{g}}">结算</button></view></view></view></view>