<view class="cart-page data-v-3277fd7b"><view wx:if="{{a}}" class="empty-cart data-v-3277fd7b"><image src="{{b}}" class="empty-icon data-v-3277fd7b" mode="aspectFit"/><text class="empty-text data-v-3277fd7b">购物车还是空的</text><text class="empty-desc data-v-3277fd7b">快去挑选心仪的商品吧</text><mall-button wx:if="{{d}}" class="data-v-3277fd7b" u-s="{{['d']}}" bindclick="{{c}}" u-i="3277fd7b-0" bind:__l="__l" u-p="{{d}}">去逛逛</mall-button></view><view wx:else class="cart-content data-v-3277fd7b"><view class="select-all-bar data-v-3277fd7b"><view class="select-all data-v-3277fd7b" bindtap="{{h}}"><view class="{{['checkbox', 'data-v-3277fd7b', g && 'checkbox--checked']}}"><image wx:if="{{e}}" src="{{f}}" class="check-icon data-v-3277fd7b" mode="aspectFit"/></view><text class="select-text data-v-3277fd7b">全选</text></view><view class="edit-button data-v-3277fd7b" bindtap="{{j}}">{{i}}</view></view><scroll-view scroll-y class="cart-list data-v-3277fd7b"><view wx:for="{{k}}" wx:for-item="item" wx:key="q" class="cart-item data-v-3277fd7b"><view class="item-checkbox data-v-3277fd7b" bindtap="{{item.d}}"><view class="{{['checkbox', 'data-v-3277fd7b', item.c && 'checkbox--checked']}}"><image wx:if="{{item.a}}" src="{{item.b}}" class="check-icon data-v-3277fd7b" mode="aspectFit"/></view></view><image src="{{item.e}}" class="item-image data-v-3277fd7b" mode="aspectFill"/><view class="item-info data-v-3277fd7b"><view class="item-name ellipsis-2 data-v-3277fd7b">{{item.f}}</view><view class="item-specs data-v-3277fd7b"><text wx:for="{{item.g}}" wx:for-item="spec" wx:key="b" class="spec-text data-v-3277fd7b">{{spec.a}}</text></view><view class="item-bottom data-v-3277fd7b"><view class="item-price data-v-3277fd7b">¥{{item.h}}</view><view class="quantity-control data-v-3277fd7b"><view class="{{['quantity-btn', 'data-v-3277fd7b', item.i && 'quantity-btn--disabled']}}" bindtap="{{item.j}}"> - </view><input class="quantity-input data-v-3277fd7b" type="number" bindblur="{{item.k}}" value="{{item.l}}" bindinput="{{item.m}}"/><view class="quantity-btn data-v-3277fd7b" bindtap="{{item.n}}"> + </view></view></view></view><view wx:if="{{l}}" class="item-delete data-v-3277fd7b" bindtap="{{item.p}}"><image src="{{item.o}}" class="delete-icon data-v-3277fd7b" mode="aspectFit"/></view></view></scroll-view><view class="checkout-bar data-v-3277fd7b"><view class="checkout-info data-v-3277fd7b"><view class="selected-count data-v-3277fd7b">已选择{{m}}件商品</view><view class="total-price data-v-3277fd7b"><text class="total-label data-v-3277fd7b">合计：</text><text class="total-amount data-v-3277fd7b">¥{{n}}</text></view></view><mall-button wx:if="{{q}}" class="data-v-3277fd7b" u-s="{{['d']}}" bindclick="{{p}}" u-i="3277fd7b-1" bind:__l="__l" u-p="{{q}}"> 结算({{o}}) </mall-button></view></view></view>