"use strict";var e=(e,t,a)=>new Promise((o,i)=>{var c=e=>{try{d(a.next(e))}catch(t){i(t)}},n=e=>{try{d(a.throw(e))}catch(t){i(t)}},d=e=>e.done?o(e.value):Promise.resolve(e.value).then(c,n);d((a=a.apply(e,t)).next())});const t=require("../../common/vendor.js"),a=require("../../common/assets.js");if(!Array){t.resolveComponent("mall-button")()}Math;const o=t.defineComponent({__name:"index",setup(o){const i=t.ref([]),c=t.ref(!1),n=t.ref(!1),d=t.computed(()=>i.value.filter(e=>e.checked)),s=t.computed(()=>d.value.length),u=t.computed(()=>i.value.length>0&&d.value.length===i.value.length),l=t.computed(()=>d.value.reduce((e,t)=>e+t.price*t.quantity,0));t.onMounted(()=>{r()});const r=()=>e(this,null,function*(){try{n.value=!0,i.value=[{id:"1",productId:"1",product:{id:"1",name:"春季新款连衣裙 优雅气质款 多色可选",description:"优质面料，舒适透气",price:299,originalPrice:399,images:["https://picsum.photos/300/300?random=30"],categoryId:"1",categoryName:"服装",stock:100,sales:1234,rating:4.8,reviewCount:567,tags:["新品","热销"],specs:[],status:"active",createdAt:"2024-01-01",updatedAt:"2024-01-01"},quantity:1,selectedSpecs:[{specId:"1",specName:"颜色",optionId:"1-1",optionName:"红色",optionValue:"red"},{specId:"2",specName:"尺码",optionId:"2-1",optionName:"M",optionValue:"M"}],price:299,checked:!0,createdAt:"2024-01-01"},{id:"2",productId:"2",product:{id:"2",name:"无线蓝牙耳机 降噪版 长续航",description:"高品质音效，舒适佩戴",price:199,images:["https://picsum.photos/300/300?random=31"],categoryId:"2",categoryName:"数码",stock:50,sales:890,rating:4.6,reviewCount:234,tags:["热销"],specs:[],status:"active",createdAt:"2024-01-01",updatedAt:"2024-01-01"},quantity:2,selectedSpecs:[{specId:"3",specName:"颜色",optionId:"3-1",optionName:"黑色",optionValue:"black"}],price:199,checked:!1,createdAt:"2024-01-01"}]}catch(e){t.index.showToast({title:"加载失败，请重试",icon:"none"})}finally{n.value=!1}}),p=()=>{t.index.switchTab({url:"/pages/home/<USER>"})},m=()=>{c.value=!c.value},v=()=>{const e=!u.value;i.value.forEach(t=>{t.checked=e})},h=(a,o)=>e(this,null,function*(){if(!(o<1))try{a.quantity=o}catch(e){t.index.showToast({title:"更新失败",icon:"none"})}}),y=a=>e(this,null,function*(){try{yield t.index.showModal({title:"确认删除",content:"确定要删除这件商品吗？"});const e=i.value.findIndex(e=>e.id===a.id);e>-1&&(i.value.splice(e,1),t.index.showToast({title:"删除成功",icon:"success"}))}catch(e){}}),g=()=>{if(0===s.value)return void t.index.showToast({title:"请选择要结算的商品",icon:"none"});const e=d.value.map(e=>e.id);t.index.navigateTo({url:`/pages/order/confirm/index?items=${e.join(",")}`})};return(e,o)=>t.e({a:0===i.value.length},0===i.value.length?{b:a._imports_0,c:t.o(p),d:t.p({type:"primary"})}:t.e({e:u.value},u.value?{f:a._imports_1}:{},{g:u.value?1:"",h:t.o(v),i:t.t(c.value?"完成":"编辑"),j:t.o(m),k:t.f(i.value,(e,o,i)=>t.e({a:e.checked},e.checked?{b:a._imports_1}:{},{c:e.checked?1:"",d:t.o(t=>(e=>{e.checked=!e.checked})(e),e.id),e:e.product.images[0],f:t.t(e.product.name),g:t.f(e.selectedSpecs,(e,a,o)=>({a:t.t(e.optionName),b:e.specId})),h:t.t(e.price.toFixed(2)),i:e.quantity<=1?1:"",j:t.o(t=>h(e,e.quantity-1),e.id),k:t.o(t=>(e=>{e.quantity<1&&(e.quantity=1),h(e,e.quantity)})(e),e.id),l:e.quantity,m:t.o(t.m(t=>e.quantity=t.detail.value,{number:!0}),e.id),n:t.o(t=>h(e,e.quantity+1),e.id)},c.value?{o:a._imports_2,p:t.o(t=>y(e),e.id)}:{},{q:e.id})),l:c.value,m:t.t(s.value),n:t.t(l.value.toFixed(2)),o:t.t(s.value),p:t.o(g),q:t.p({type:"primary",disabled:0===s.value})}))}}),i=t._export_sfc(o,[["__scopeId","data-v-3277fd7b"]]);wx.createPage(i);
