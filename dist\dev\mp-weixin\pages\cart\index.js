"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const cartItems = common_vendor.ref([
      {
        id: 1,
        name: "示例商品1",
        price: 99.9,
        quantity: 1,
        image: "/static/placeholder.png",
        selected: true
      },
      {
        id: 2,
        name: "示例商品2",
        price: 199.9,
        quantity: 2,
        image: "/static/placeholder.png",
        selected: false
      }
    ]);
    const allSelected = common_vendor.computed(() => {
      return cartItems.value.length > 0 && cartItems.value.every((item) => item.selected);
    });
    const totalPrice = common_vendor.computed(() => {
      return cartItems.value.filter((item) => item.selected).reduce((total, item) => total + item.price * item.quantity, 0).toFixed(2);
    });
    const handleItemSelect = (item) => {
      item.selected = !item.selected;
    };
    const handleSelectAll = () => {
      const selectAll = !allSelected.value;
      cartItems.value.forEach((item) => {
        item.selected = selectAll;
      });
    };
    const increaseQuantity = (item) => {
      item.quantity++;
    };
    const decreaseQuantity = (item) => {
      if (item.quantity > 1) {
        item.quantity--;
      }
    };
    const goShopping = () => {
      common_vendor.index.switchTab({
        url: "/pages/home/<USER>"
      });
    };
    const checkout = () => {
      const selectedItems = cartItems.value.filter((item) => item.selected);
      if (selectedItems.length === 0) {
        common_vendor.index.showToast({
          title: "请选择商品",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showToast({
        title: "跳转到结算页面",
        icon: "success"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: cartItems.value.length === 0
      }, cartItems.value.length === 0 ? {
        b: common_vendor.o(goShopping)
      } : {
        c: common_vendor.f(cartItems.value, (item, k0, i0) => {
          return {
            a: item.selected,
            b: common_vendor.o(($event) => handleItemSelect(item), item.id),
            c: item.image,
            d: common_vendor.t(item.name),
            e: common_vendor.t(item.price),
            f: common_vendor.o(($event) => decreaseQuantity(item), item.id),
            g: common_vendor.t(item.quantity),
            h: common_vendor.o(($event) => increaseQuantity(item), item.id),
            i: item.id
          };
        }),
        d: allSelected.value,
        e: common_vendor.o(handleSelectAll),
        f: common_vendor.t(totalPrice.value),
        g: common_vendor.o(checkout)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3277fd7b"]]);
wx.createPage(MiniProgramPage);
