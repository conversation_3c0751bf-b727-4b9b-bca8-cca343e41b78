"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userInfo = common_vendor.ref({
      id: "123456",
      nickname: "微信用户",
      avatar: "/static/default-avatar.png"
    });
    const userStats = common_vendor.ref({
      orders: 5,
      favorites: 12,
      points: 1280
    });
    const menuList = common_vendor.ref([
      { id: 1, title: "我的订单", icon: "📋", path: "/pages/order/list" },
      { id: 2, title: "收货地址", icon: "📍", path: "/pages/address/list" },
      { id: 3, title: "我的收藏", icon: "❤️", path: "/pages/favorite/list" },
      { id: 4, title: "优惠券", icon: "🎫", path: "/pages/coupon/list" },
      { id: 5, title: "客服中心", icon: "💬", path: "/pages/service/index" },
      { id: 6, title: "设置", icon: "⚙️", path: "/pages/settings/index" }
    ]);
    const handleMenuClick = (menu) => {
      if (menu.path) {
        common_vendor.index.navigateTo({
          url: menu.path
        });
      } else {
        common_vendor.index.showToast({
          title: `点击了${menu.title}`,
          icon: "none"
        });
      }
    };
    const handleLogout = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            userInfo.value = {
              avatar: "/static/default-avatar.png"
            };
            common_vendor.index.showToast({
              title: "已退出登录",
              icon: "success"
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: userInfo.value.avatar,
        b: common_vendor.t(userInfo.value.nickname || "未登录"),
        c: common_vendor.t(userInfo.value.id || "000000"),
        d: common_vendor.t(userStats.value.orders),
        e: common_vendor.t(userStats.value.favorites),
        f: common_vendor.t(userStats.value.points),
        g: common_vendor.f(menuList.value, (menu, k0, i0) => {
          return {
            a: common_vendor.t(menu.icon),
            b: common_vendor.t(menu.title),
            c: menu.id,
            d: common_vendor.o(($event) => handleMenuClick(menu), menu.id)
          };
        }),
        h: common_vendor.o(handleLogout)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f97f9319"]]);
wx.createPage(MiniProgramPage);
