.category.data-v-f0c2a821 {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.search-bar.data-v-f0c2a821 {
  margin-bottom: 30rpx;
}
.search-bar .search-input.data-v-f0c2a821 {
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.category-grid.data-v-f0c2a821 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.category-item.data-v-f0c2a821 {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}
.category-item.data-v-f0c2a821:active {
  transform: scale(0.95);
}
.category-item .category-icon.data-v-f0c2a821 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.category-item .category-name.data-v-f0c2a821 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}