"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveTemplateCompilerOptions = exports.resolveGenTemplateCodeOptions = void 0;
const path_1 = __importDefault(require("path"));
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const script_1 = require("./script");
const utils_1 = require("../../utils");
function resolveGenTemplateCodeOptions(relativeFileName, code, descriptor, options) {
    const block = descriptor.template;
    if (!block) {
        return {
            genDefaultAs: script_1.scriptIdentifier,
            ...options,
            filename: relativeFileName,
        };
    }
    const inputRoot = (0, uni_cli_shared_1.normalizePath)(options.rootDir);
    const templateStartLine = descriptor.template?.loc.start.line ?? 0;
    let preprocessOptions = block.lang && options.preprocessOptions;
    if (block.lang === 'pug') {
        preprocessOptions = {
            doctype: 'html',
            ...preprocessOptions,
        };
    }
    return {
        genDefaultAs: script_1.scriptIdentifier,
        ...options,
        filename: relativeFileName,
        inMap: descriptor.template?.map,
        preprocessLang: block.lang === 'html' ? undefined : block.lang,
        preprocessOptions,
        inline: !!descriptor.scriptSetup,
        matchEasyCom: (tag, uts) => {
            const source = (0, uni_cli_shared_1.matchEasycom)(tag);
            if (uts && source) {
                if (source.startsWith(inputRoot)) {
                    return '@/' + (0, uni_cli_shared_1.normalizePath)(path_1.default.relative(inputRoot, source));
                }
                return (0, utils_1.parseUTSImportFilename)(source);
            }
            return source;
        },
        onWarn(warning) {
            onTemplateLog('warn', warning, code, relativeFileName, templateStartLine);
        },
        onError(error) {
            onTemplateLog('error', error, code, relativeFileName, templateStartLine);
        },
        parseUTSComponent: uni_cli_shared_1.parseUTSComponent,
    };
}
exports.resolveGenTemplateCodeOptions = resolveGenTemplateCodeOptions;
function onTemplateLog(type, error, code, relativeFileName, templateStartLine) {
    console.error(type + ': ' + error.message);
    if (error.loc) {
        const start = error.loc.start;
        console.log('at ' +
            relativeFileName +
            ':' +
            (start.line + templateStartLine - 1) +
            ':' +
            (start.column - 1));
        console.log((0, uni_cli_shared_1.generateCodeFrameColumns)(code, error.loc));
    }
}
function resolveTemplateCompilerOptions(descriptor, options) {
    const block = descriptor.template;
    if (!block) {
        return;
    }
    const resolvedScript = (0, script_1.getResolvedScript)(descriptor);
    const hasScoped = descriptor.styles.some((s) => s.scoped);
    const { id, filename, cssVars } = descriptor;
    let transformAssetUrls = options.template?.transformAssetUrls;
    // compiler-sfc should export `AssetURLOptions`
    let assetUrlOptions; //: AssetURLOptions | undefined
    if (transformAssetUrls !== false) {
        // build: force all asset urls into import requests so that they go through
        // the assets plugin for asset registration
        assetUrlOptions = {
            includeAbsolute: true,
        };
    }
    if (transformAssetUrls && typeof transformAssetUrls === 'object') {
        // presence of array fields means this is raw tags config
        if (Object.values(transformAssetUrls).some((val) => Array.isArray(val))) {
            transformAssetUrls = {
                ...assetUrlOptions,
                tags: transformAssetUrls,
            };
        }
        else {
            transformAssetUrls = { ...assetUrlOptions, ...transformAssetUrls };
        }
    }
    else {
        transformAssetUrls = assetUrlOptions;
    }
    let preprocessOptions = block.lang && options.template?.preprocessOptions;
    if (block.lang === 'pug') {
        preprocessOptions = {
            doctype: 'html',
            ...preprocessOptions,
        };
    }
    // if using TS, support TS syntax in template expressions
    const expressionPlugins = options.template?.compilerOptions?.expressionPlugins || [];
    const lang = descriptor.scriptSetup?.lang || descriptor.script?.lang;
    if (lang && /tsx?$/.test(lang) && !expressionPlugins.includes('typescript')) {
        expressionPlugins.push('typescript');
    }
    return {
        ...options.template,
        id,
        // TODO remove ignore when dep is updated to 3.4
        ast: descriptor.template?.ast,
        filename,
        scoped: hasScoped,
        slotted: descriptor.slotted,
        isProd: options.isProduction,
        inMap: block.src ? undefined : block.map,
        ssrCssVars: cssVars,
        transformAssetUrls,
        preprocessLang: block.lang === 'html' ? undefined : block.lang,
        preprocessOptions,
        compilerOptions: {
            ...options.template?.compilerOptions,
            scopeId: hasScoped ? `data-v-${id}` : undefined,
            bindingMetadata: resolvedScript ? resolvedScript.bindings : undefined,
            expressionPlugins,
            sourceMap: options.sourceMap,
        },
    };
}
exports.resolveTemplateCompilerOptions = resolveTemplateCompilerOptions;
