<template>
  <view class="user-page">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info" @click="handleUserClick">
        <image :src="userInfo?.avatar || '/static/icons/avatar-default.png'" class="user-avatar" mode="aspectFill" />
        <view class="user-details">
          <view v-if="isLoggedIn" class="user-name">{{ userInfo?.nickname || '用户' }}</view>
          <view v-else class="login-text">点击登录</view>
          <view v-if="isLoggedIn" class="user-level">
            <text class="level-text">Lv.{{ userInfo?.level || 1 }}</text>
            <text class="points-text">积分: {{ userInfo?.points || 0 }}</text>
          </view>
        </view>
        <image src="/static/icons/arrow-right.png" class="arrow-icon" mode="aspectFit" />
      </view>
      
      <view v-if="isLoggedIn" class="user-stats">
        <view class="stat-item" @click="goToOrders">
          <text class="stat-number">{{ userStats.orderCount }}</text>
          <text class="stat-label">订单</text>
        </view>
        <view class="stat-item" @click="goToCoupons">
          <text class="stat-number">{{ userStats.couponCount }}</text>
          <text class="stat-label">优惠券</text>
        </view>
        <view class="stat-item" @click="goToFavorites">
          <text class="stat-number">{{ userStats.favoriteCount }}</text>
          <text class="stat-label">收藏</text>
        </view>
        <view class="stat-item" @click="goToBalance">
          <text class="stat-number">¥{{ (userInfo?.balance || 0).toFixed(2) }}</text>
          <text class="stat-label">余额</text>
        </view>
      </view>
    </view>
    
    <!-- 订单状态 -->
    <view v-if="isLoggedIn" class="order-section">
      <mall-card title="我的订单" extra="查看全部" @click="goToOrders">
        <view class="order-status-list">
          <view 
            v-for="status in orderStatusList" 
            :key="status.key"
            class="order-status-item"
            @click="goToOrders(status.key)"
          >
            <view class="status-icon-wrapper">
              <image :src="status.icon" class="status-icon" mode="aspectFit" />
              <view v-if="status.count > 0" class="status-badge">{{ status.count }}</view>
            </view>
            <text class="status-text">{{ status.name }}</text>
          </view>
        </view>
      </mall-card>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <mall-card>
        <view class="menu-list">
          <view 
            v-for="menu in menuList" 
            :key="menu.key"
            class="menu-item"
            @click="handleMenuClick(menu)"
          >
            <image :src="menu.icon" class="menu-icon" mode="aspectFit" />
            <text class="menu-text">{{ menu.name }}</text>
            <image src="/static/icons/arrow-right.png" class="arrow-icon" mode="aspectFit" />
          </view>
        </view>
      </mall-card>
    </view>
    
    <!-- 客服和设置 -->
    <view class="service-section">
      <mall-card>
        <view class="service-list">
          <view class="service-item" @click="contactService">
            <image src="/static/icons/service.png" class="service-icon" mode="aspectFit" />
            <text class="service-text">联系客服</text>
            <image src="/static/icons/arrow-right.png" class="arrow-icon" mode="aspectFit" />
          </view>
          <view class="service-item" @click="goToSettings">
            <image src="/static/icons/setting.png" class="service-icon" mode="aspectFit" />
            <text class="service-text">设置</text>
            <image src="/static/icons/arrow-right.png" class="arrow-icon" mode="aspectFit" />
          </view>
        </view>
      </mall-card>
    </view>
    
    <!-- 退出登录 -->
    <view v-if="isLoggedIn" class="logout-section">
      <mall-button type="text" block @click="handleLogout">退出登录</mall-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import type { User } from '@/types'

// 响应式数据
const userInfo = ref<User | null>(null)
const userStats = ref({
  orderCount: 0,
  couponCount: 0,
  favoriteCount: 0
})

// 计算属性
const isLoggedIn = computed(() => !!userInfo.value)

// 订单状态列表
const orderStatusList = ref([
  { key: 'pending', name: '待付款', icon: '/static/icons/order-pending.png', count: 2 },
  { key: 'paid', name: '待发货', icon: '/static/icons/order-paid.png', count: 1 },
  { key: 'shipped', name: '待收货', icon: '/static/icons/order-shipped.png', count: 0 },
  { key: 'completed', name: '已完成', icon: '/static/icons/order-completed.png', count: 5 }
])

// 功能菜单列表
const menuList = ref([
  { key: 'address', name: '收货地址', icon: '/static/icons/address.png' },
  { key: 'coupon', name: '优惠券', icon: '/static/icons/coupon.png' },
  { key: 'favorite', name: '我的收藏', icon: '/static/icons/heart.png' },
  { key: 'history', name: '浏览历史', icon: '/static/icons/history.png' },
  { key: 'feedback', name: '意见反馈', icon: '/static/icons/feedback.png' },
  { key: 'about', name: '关于我们', icon: '/static/icons/info.png' }
])

// 页面生命周期
onMounted(() => {
  loadUserInfo()
})

onShow(() => {
  if (isLoggedIn.value) {
    loadUserStats()
  }
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    // 检查本地存储的登录状态
    const token = uni.getStorageSync('token')
    if (token) {
      // 模拟用户数据
      userInfo.value = {
        id: '1',
        nickname: '小明',
        avatar: 'https://picsum.photos/100/100?random=40',
        phone: '138****8888',
        gender: 1,
        level: 3,
        points: 1580,
        balance: 299.50,
        status: 'active',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }
      
      await loadUserStats()
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    // 模拟统计数据
    userStats.value = {
      orderCount: 8,
      couponCount: 5,
      favoriteCount: 12
    }
  } catch (error) {
    console.error('加载用户统计失败:', error)
  }
}

// 事件处理
const handleUserClick = () => {
  if (isLoggedIn.value) {
    uni.navigateTo({
      url: '/pages/user/profile/index'
    })
  } else {
    uni.navigateTo({
      url: '/pages/user/login/index'
    })
  }
}

const goToOrders = (status?: string) => {
  const url = status ? `/pages/order/list/index?status=${status}` : '/pages/order/list/index'
  uni.navigateTo({ url })
}

const goToCoupons = () => {
  uni.navigateTo({
    url: '/pages/user/coupon/index'
  })
}

const goToFavorites = () => {
  uni.navigateTo({
    url: '/pages/user/favorite/index'
  })
}

const goToBalance = () => {
  uni.navigateTo({
    url: '/pages/user/balance/index'
  })
}

const handleMenuClick = (menu: any) => {
  const routeMap: Record<string, string> = {
    address: '/pages/user/address/index',
    coupon: '/pages/user/coupon/index',
    favorite: '/pages/user/favorite/index',
    history: '/pages/user/history/index',
    feedback: '/pages/user/feedback/index',
    about: '/pages/user/about/index'
  }
  
  const url = routeMap[menu.key]
  if (url) {
    uni.navigateTo({ url })
  }
}

const contactService = () => {
  // #ifdef MP-WEIXIN
  uni.navigateTo({
    url: 'plugin://wx-service-chat-plugin/chat'
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.showModal({
    title: '联系客服',
    content: '客服电话：400-123-4567',
    showCancel: false
  })
  // #endif
}

const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/user/settings/index'
  })
}

const handleLogout = async () => {
  try {
    const result = await uni.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？'
    })
    
    if (result.confirm) {
      // 清除本地存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 清除用户信息
      userInfo.value = null
      userStats.value = {
        orderCount: 0,
        couponCount: 0,
        favoriteCount: 0
      }
      
      uni.showToast({
        title: '已退出登录',
        icon: 'success'
      })
    }
  } catch (error) {
    // 用户取消退出
  }
}
</script>

<style lang="scss" scoped>
.user-page {
  background-color: $bg-color-page;
  min-height: 100vh;
  
  .user-header {
    background: linear-gradient(135deg, $primary-color, $primary-light);
    padding: $spacing-xl $spacing-lg $spacing-lg;
    color: $text-color-white;
    
    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-lg;
      cursor: pointer;
      
      &:active {
        opacity: 0.8;
      }
      
      .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right: $spacing-lg;
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      .user-details {
        flex: 1;
        
        .user-name {
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          margin-bottom: $spacing-xs;
        }
        
        .login-text {
          font-size: $font-size-lg;
          margin-bottom: $spacing-xs;
        }
        
        .user-level {
          display: flex;
          gap: $spacing-lg;
          
          .level-text,
          .points-text {
            font-size: $font-size-sm;
            opacity: 0.9;
          }
        }
      }
      
      .arrow-icon {
        width: 16px;
        height: 16px;
        opacity: 0.8;
      }
    }
    
    .user-stats {
      display: flex;
      justify-content: space-around;
      
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        
        &:active {
          opacity: 0.8;
        }
        
        .stat-number {
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-xs;
        }
        
        .stat-label {
          font-size: $font-size-sm;
          opacity: 0.9;
        }
      }
    }
  }
  
  .order-section {
    margin: $spacing-lg;
    
    .order-status-list {
      display: flex;
      justify-content: space-around;
      padding: $spacing-lg 0;
      
      .order-status-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        
        &:active {
          opacity: 0.7;
        }
        
        .status-icon-wrapper {
          position: relative;
          margin-bottom: $spacing-xs;
          
          .status-icon {
            width: 32px;
            height: 32px;
          }
          
          .status-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            min-width: 16px;
            height: 16px;
            background-color: $error-color;
            color: $text-color-white;
            font-size: $font-size-xs;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
          }
        }
        
        .status-text {
          font-size: $font-size-sm;
          color: $text-color-primary;
        }
      }
    }
  }
  
  .menu-section,
  .service-section {
    margin: $spacing-lg;
    
    .menu-list,
    .service-list {
      .menu-item,
      .service-item {
        display: flex;
        align-items: center;
        padding: $spacing-lg 0;
        border-bottom: 1px solid $border-color-light;
        cursor: pointer;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:active {
          background-color: $bg-color-gray;
        }
        
        .menu-icon,
        .service-icon {
          width: 24px;
          height: 24px;
          margin-right: $spacing-lg;
        }
        
        .menu-text,
        .service-text {
          flex: 1;
          font-size: $font-size-base;
          color: $text-color-primary;
        }
        
        .arrow-icon {
          width: 16px;
          height: 16px;
          opacity: 0.6;
        }
      }
    }
  }
  
  .logout-section {
    margin: $spacing-xl $spacing-lg;
  }
}
</style>
