{"name": "@dcloudio/uni-mp-weixin", "version": "3.0.0-4020920240930001", "description": "uni-app mp-weixin", "main": "dist/index.js", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-mp-weixin"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "uni-app": {"name": "mp-weixin", "title": "微信小程序", "apply": ["mp-weixin"], "main": "dist/uni.compiler.js"}, "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "devDependencies": {"@vue/compiler-core": "3.4.21"}, "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/uni-mp-vite": "3.0.0-4020920240930001", "@dcloudio/uni-mp-vue": "3.0.0-4020920240930001", "@dcloudio/uni-shared": "3.0.0-4020920240930001", "@vue/shared": "3.4.21", "jimp": "^0.10.1", "licia": "^1.29.0", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "ws": "^8.4.2"}}