<template>
  <mall-card 
    class="mall-product-card" 
    :clickable="true"
    @click="handleClick"
  >
    <view class="product-image-wrapper">
      <image 
        :src="product.images[0]" 
        class="product-image" 
        mode="aspectFill"
        @error="handleImageError"
      />
      <view v-if="product.tags.length > 0" class="product-tags">
        <view 
          v-for="tag in product.tags.slice(0, 2)" 
          :key="tag" 
          class="product-tag"
        >
          {{ tag }}
        </view>
      </view>
    </view>
    
    <view class="product-info">
      <view class="product-name ellipsis-2">{{ product.name }}</view>
      
      <view class="product-price-wrapper">
        <view class="product-price">
          <text class="price-symbol">¥</text>
          <text class="price-integer">{{ priceInteger }}</text>
          <text class="price-decimal">.{{ priceDecimal }}</text>
        </view>
        <view v-if="product.originalPrice && product.originalPrice > product.price" class="original-price">
          ¥{{ product.originalPrice.toFixed(2) }}
        </view>
      </view>
      
      <view class="product-meta">
        <view class="product-rating">
          <view class="rating-stars">
            <view 
              v-for="i in 5" 
              :key="i" 
              class="star"
              :class="{ 'star--filled': i <= Math.floor(product.rating) }"
            >
              ★
            </view>
          </view>
          <text class="rating-text">{{ product.rating.toFixed(1) }}</text>
        </view>
        <view class="product-sales">已售{{ formatSales(product.sales) }}</view>
      </view>
    </view>
    
    <view class="product-actions">
      <mall-button 
        type="text" 
        size="small"
        @click.stop="handleAddToCart"
      >
        <image src="/static/icons/cart-add.png" class="action-icon" mode="aspectFit" />
      </mall-button>
    </view>
  </mall-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Product } from '@/types'

interface Props {
  product: Product
}

interface Emits {
  (e: 'click', product: Product): void
  (e: 'add-to-cart', product: Product): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const priceInteger = computed(() => {
  return Math.floor(props.product.price).toString()
})

const priceDecimal = computed(() => {
  return (props.product.price % 1).toFixed(2).slice(2)
})

const formatSales = (sales: number) => {
  if (sales >= 10000) {
    return `${(sales / 10000).toFixed(1)}万`
  }
  return sales.toString()
}

const handleClick = () => {
  emit('click', props.product)
}

const handleAddToCart = () => {
  emit('add-to-cart', props.product)
}

const handleImageError = () => {
  console.warn('商品图片加载失败:', props.product.id)
}
</script>

<style lang="scss" scoped>
.mall-product-card {
  position: relative;
  
  .product-image-wrapper {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
    
    .product-image {
      width: 100%;
      height: 100%;
      background-color: $bg-color-gray;
    }
    
    .product-tags {
      position: absolute;
      top: $spacing-xs;
      left: $spacing-xs;
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
      
      .product-tag {
        padding: 2px $spacing-xs;
        background-color: rgba($primary-color, 0.9);
        color: $text-color-white;
        font-size: $font-size-xs;
        border-radius: $border-radius-small;
        max-width: 60px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  
  .product-info {
    padding: $spacing-sm;
    
    .product-name {
      font-size: $font-size-sm;
      color: $text-color-primary;
      line-height: 1.4;
      height: 2.8em;
      margin-bottom: $spacing-xs;
    }
    
    .product-price-wrapper {
      display: flex;
      align-items: baseline;
      gap: $spacing-xs;
      margin-bottom: $spacing-xs;
      
      .product-price {
        color: $primary-color;
        font-weight: $font-weight-bold;
        
        .price-symbol {
          font-size: $font-size-xs;
        }
        
        .price-integer {
          font-size: $font-size-lg;
        }
        
        .price-decimal {
          font-size: $font-size-sm;
        }
      }
      
      .original-price {
        color: $text-color-placeholder;
        font-size: $font-size-xs;
        text-decoration: line-through;
      }
    }
    
    .product-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: $font-size-xs;
      color: $text-color-secondary;
      
      .product-rating {
        display: flex;
        align-items: center;
        gap: 2px;
        
        .rating-stars {
          display: flex;
          
          .star {
            color: $border-color-base;
            font-size: 10px;
            
            &--filled {
              color: $warning-color;
            }
          }
        }
        
        .rating-text {
          margin-left: 2px;
        }
      }
    }
  }
  
  .product-actions {
    position: absolute;
    bottom: $spacing-sm;
    right: $spacing-sm;
    
    .action-icon {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
