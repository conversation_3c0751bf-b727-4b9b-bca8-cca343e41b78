<template>
  <view class="login-container">
    <!-- Logo区域 -->
    <view class="logo-section">
      <image src="https://via.placeholder.com/200x200/ff6b35/ffffff?text=LOGO" class="app-logo" mode="aspectFit" />
      <text class="app-name">积分商城</text>
      <text class="app-slogan">购物赚积分，积分换好礼</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 手机号登录 -->
      <view class="login-method" v-if="loginType === 'phone'">
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-prefix">+86</text>
            <input 
              type="number" 
              v-model="phoneNumber" 
              placeholder="请输入手机号"
              class="phone-input"
              maxlength="11"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <input 
              type="number" 
              v-model="verifyCode" 
              placeholder="请输入验证码"
              class="code-input"
              maxlength="6"
            />
            <view 
              class="send-code-btn"
              :class="{ disabled: !canSendCode }"
              @click="sendVerifyCode"
            >
              <text class="btn-text">{{ codeButtonText }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 微信登录 -->
      <view class="login-method" v-else>
        <view class="wechat-login-tip">
          <text class="tip-text">使用微信账号快速登录</text>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="login-actions">
        <view 
          class="login-btn"
          :class="{ disabled: !canLogin }"
          @click="handleLogin"
        >
          <text class="btn-text">
            {{ loginType === 'phone' ? '登录' : '微信登录' }}
          </text>
        </view>
      </view>

      <!-- 登录方式切换 -->
      <view class="login-switch">
        <text 
          class="switch-text"
          @click="switchLoginType"
        >
          {{ loginType === 'phone' ? '使用微信登录' : '使用手机号登录' }}
        </text>
      </view>

      <!-- 协议条款 -->
      <view class="agreement-section">
        <view class="agreement-checkbox" @click="toggleAgreement">
          <text class="checkbox-icon">{{ hasAgreed ? '☑️' : '☐' }}</text>
          <text class="agreement-text">
            我已阅读并同意
            <text class="link-text" @click.stop="showAgreement('user')">《用户协议》</text>
            和
            <text class="link-text" @click.stop="showAgreement('privacy')">《隐私政策》</text>
          </text>
        </view>
      </view>
    </view>

    <!-- 其他登录方式 -->
    <view class="other-login">
      <view class="divider">
        <text class="divider-text">其他登录方式</text>
      </view>
      
      <view class="social-login">
        <view class="social-item" @click="loginWithQQ">
          <text class="social-icon">🐧</text>
          <text class="social-text">QQ</text>
        </view>
        <view class="social-item" @click="loginWithWeibo">
          <text class="social-icon">🔴</text>
          <text class="social-text">微博</text>
        </view>
        <view class="social-item" @click="loginWithApple">
          <text class="social-icon">🍎</text>
          <text class="social-text">Apple</text>
        </view>
      </view>
    </view>

    <!-- 游客模式 -->
    <view class="guest-mode">
      <text class="guest-text" @click="enterAsGuest">暂不登录，先逛逛</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const loginType = ref<'phone' | 'wechat'>('phone')
const phoneNumber = ref('')
const verifyCode = ref('')
const hasAgreed = ref(false)
const countdown = ref(0)
const isLoading = ref(false)

// 计算属性
const canSendCode = computed(() => {
  return phoneNumber.value.length === 11 && countdown.value === 0
})

const codeButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s后重发` : '发送验证码'
})

const canLogin = computed(() => {
  if (loginType.value === 'phone') {
    return phoneNumber.value.length === 11 && 
           verifyCode.value.length === 6 && 
           hasAgreed.value
  } else {
    return hasAgreed.value
  }
})

// 方法
const switchLoginType = () => {
  loginType.value = loginType.value === 'phone' ? 'wechat' : 'phone'
  // 清空表单数据
  phoneNumber.value = ''
  verifyCode.value = ''
}

const sendVerifyCode = () => {
  if (!canSendCode.value) return
  
  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phoneNumber.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }
  
  // 开始倒计时
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
  
  // 模拟发送验证码
  uni.showToast({
    title: '验证码已发送',
    icon: 'success'
  })
}

const handleLogin = async () => {
  if (!canLogin.value || isLoading.value) return
  
  isLoading.value = true
  
  try {
    if (loginType.value === 'phone') {
      await loginWithPhone()
    } else {
      await loginWithWechat()
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    isLoading.value = false
  }
}

const loginWithPhone = async () => {
  // 模拟手机号登录
  if (verifyCode.value !== '123456') {
    uni.showToast({
      title: '验证码错误',
      icon: 'none'
    })
    return
  }
  
  uni.showToast({
    title: '登录成功',
    icon: 'success'
  })
  
  // 跳转到首页
  setTimeout(() => {
    uni.switchTab({
      url: '/pages/home/<USER>'
    })
  }, 1500)
}

const loginWithWechat = async () => {
  // 模拟微信登录
  uni.showLoading({
    title: '微信登录中...'
  })
  
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
    }, 1500)
  }, 2000)
}

const loginWithQQ = () => {
  uni.showToast({
    title: 'QQ登录功能开发中',
    icon: 'none'
  })
}

const loginWithWeibo = () => {
  uni.showToast({
    title: '微博登录功能开发中',
    icon: 'none'
  })
}

const loginWithApple = () => {
  uni.showToast({
    title: 'Apple登录功能开发中',
    icon: 'none'
  })
}

const toggleAgreement = () => {
  hasAgreed.value = !hasAgreed.value
}

const showAgreement = (type: 'user' | 'privacy') => {
  const title = type === 'user' ? '用户协议' : '隐私政策'
  uni.navigateTo({
    url: `/pages/agreement/index?type=${type}&title=${title}`
  })
}

const enterAsGuest = () => {
  uni.showModal({
    title: '游客模式',
    content: '游客模式下部分功能受限，确定继续吗？',
    success: (res) => {
      if (res.confirm) {
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  padding: 80rpx 60rpx 40rpx;
  display: flex;
  flex-direction: column;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
  
  .app-logo {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .app-name {
    font-size: 48rpx;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16rpx;
  }
  
  .app-slogan {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  
  .login-method {
    margin-bottom: 40rpx;
    
    .input-group {
      margin-bottom: 30rpx;
      
      .input-wrapper {
        display: flex;
        align-items: center;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        padding: 0 20rpx;
        height: 88rpx;
        
        .input-prefix {
          font-size: 28rpx;
          color: #666666;
          margin-right: 20rpx;
        }
        
        .phone-input, .code-input {
          flex: 1;
          font-size: 28rpx;
          color: #333333;
        }
        
        .send-code-btn {
          padding: 16rpx 24rpx;
          background-color: #ff6b35;
          border-radius: 8rpx;
          
          &.disabled {
            background-color: #d9d9d9;
          }
          
          .btn-text {
            font-size: 24rpx;
            color: #ffffff;
            font-weight: 600;
          }
        }
      }
    }
    
    .wechat-login-tip {
      text-align: center;
      padding: 40rpx 0;
      
      .tip-text {
        font-size: 26rpx;
        color: #666666;
      }
    }
  }
  
  .login-actions {
    margin-bottom: 30rpx;
    
    .login-btn {
      height: 88rpx;
      background-color: #ff6b35;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.disabled {
        background-color: #d9d9d9;
      }
      
      .btn-text {
        font-size: 32rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
  
  .login-switch {
    text-align: center;
    margin-bottom: 40rpx;
    
    .switch-text {
      font-size: 26rpx;
      color: #ff6b35;
    }
  }
  
  .agreement-section {
    .agreement-checkbox {
      display: flex;
      align-items: flex-start;
      
      .checkbox-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
        margin-top: 4rpx;
      }
      
      .agreement-text {
        flex: 1;
        font-size: 24rpx;
        color: #666666;
        line-height: 1.5;
        
        .link-text {
          color: #ff6b35;
        }
      }
    }
  }
}

.other-login {
  margin-bottom: 40rpx;
  
  .divider {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1rpx;
      background-color: rgba(255, 255, 255, 0.3);
    }
    
    .divider-text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      margin: 0 30rpx;
    }
  }
  
  .social-login {
    display: flex;
    justify-content: center;
    gap: 60rpx;
    
    .social-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .social-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40rpx;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        margin-bottom: 12rpx;
      }
      
      .social-text {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.guest-mode {
  text-align: center;
  margin-top: auto;
  
  .guest-text {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: underline;
  }
}
</style>
