<template>
  <view class="order-detail-container">
    <!-- 订单状态 -->
    <view class="order-status-card">
      <view class="status-icon">
        <text class="icon">{{ getStatusIcon(orderInfo.status) }}</text>
      </view>
      <view class="status-info">
        <text class="status-text">{{ getStatusText(orderInfo.status) }}</text>
        <text class="status-desc" v-if="getStatusDesc(orderInfo.status)">
          {{ getStatusDesc(orderInfo.status) }}
        </text>
      </view>
    </view>

    <!-- 物流信息 -->
    <view class="logistics-card" v-if="orderInfo.status === 'shipped' || orderInfo.status === 'completed'">
      <view class="card-header">
        <text class="card-title">物流信息</text>
        <text class="track-btn" @click="viewLogistics">查看物流</text>
      </view>
      <view class="logistics-info">
        <text class="logistics-company">{{ orderInfo.logistics?.company || '顺丰速运' }}</text>
        <text class="logistics-number">{{ orderInfo.logistics?.number || 'SF1234567890' }}</text>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-card">
      <view class="card-header">
        <text class="card-title">收货地址</text>
      </view>
      <view class="address-info">
        <view class="contact-info">
          <text class="contact-name">{{ orderInfo.address.name }}</text>
          <text class="contact-phone">{{ orderInfo.address.phone }}</text>
        </view>
        <text class="address-detail">
          {{ orderInfo.address.province }}{{ orderInfo.address.city }}{{ orderInfo.address.district }}{{ orderInfo.address.detail }}
        </text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="products-card">
      <view class="card-header">
        <text class="card-title">商品信息</text>
      </view>
      <view class="product-list">
        <view 
          class="product-item"
          v-for="product in orderInfo.products"
          :key="product.id"
          @click="goToProduct(product)"
        >
          <image :src="product.image" class="product-image" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
            <view class="product-price">
              <text class="price">¥{{ product.price }}</text>
              <text class="quantity">x{{ product.quantity }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card">
      <view class="card-header">
        <text class="card-title">订单信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">订单编号</text>
          <text class="info-value">{{ orderInfo.orderNumber }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">下单时间</text>
          <text class="info-value">{{ formatTime(orderInfo.createTime) }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.payTime">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{ formatTime(orderInfo.payTime) }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.shipTime">
          <text class="info-label">发货时间</text>
          <text class="info-value">{{ formatTime(orderInfo.shipTime) }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.completeTime">
          <text class="info-label">完成时间</text>
          <text class="info-value">{{ formatTime(orderInfo.completeTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="cost-card">
      <view class="card-header">
        <text class="card-title">费用明细</text>
      </view>
      <view class="cost-list">
        <view class="cost-item">
          <text class="cost-label">商品总价</text>
          <text class="cost-value">¥{{ orderInfo.productAmount }}</text>
        </view>
        <view class="cost-item" v-if="orderInfo.discountAmount > 0">
          <text class="cost-label">优惠金额</text>
          <text class="cost-value discount">-¥{{ orderInfo.discountAmount }}</text>
        </view>
        <view class="cost-item">
          <text class="cost-label">运费</text>
          <text class="cost-value">{{ orderInfo.shippingFee > 0 ? `¥${orderInfo.shippingFee}` : '免运费' }}</text>
        </view>
        <view class="cost-item total">
          <text class="cost-label">实付金额</text>
          <text class="cost-value">¥{{ orderInfo.totalAmount }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" v-if="getAvailableActions(orderInfo.status).length > 0">
      <view 
        class="action-btn"
        :class="action.type"
        v-for="action in getAvailableActions(orderInfo.status)"
        :key="action.key"
        @click="handleAction(action.key)"
      >
        <text class="btn-text">{{ action.text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onLoad } from 'vue'

interface Product {
  id: number
  name: string
  image: string
  price: number
  quantity: number
  spec?: string
}

interface Address {
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
}

interface OrderInfo {
  id: number
  orderNumber: string
  status: 'pending' | 'paid' | 'shipped' | 'completed' | 'cancelled'
  products: Product[]
  address: Address
  productAmount: number
  discountAmount: number
  shippingFee: number
  totalAmount: number
  createTime: string
  payTime?: string
  shipTime?: string
  completeTime?: string
  logistics?: {
    company: string
    number: string
  }
}

// 响应式数据
const orderInfo = ref<OrderInfo>({
  id: 1,
  orderNumber: 'EC202401010001',
  status: 'shipped',
  products: [
    {
      id: 1,
      name: '蓝牙无线耳机 降噪版',
      image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=耳机',
      price: 299,
      quantity: 1,
      spec: '黑色 标准版'
    },
    {
      id: 2,
      name: '手机保护壳',
      image: 'https://via.placeholder.com/200x200/1890ff/ffffff?text=保护壳',
      price: 39,
      quantity: 2,
      spec: '透明款'
    }
  ],
  address: {
    name: '张三',
    phone: '138****8888',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    detail: '三里屯街道工体北路8号院1号楼101室'
  },
  productAmount: 377,
  discountAmount: 20,
  shippingFee: 0,
  totalAmount: 357,
  createTime: '2024-01-01 10:30:00',
  payTime: '2024-01-01 10:35:00',
  shipTime: '2024-01-01 15:20:00',
  logistics: {
    company: '顺丰速运',
    number: 'SF1234567890'
  }
})

// 方法
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, string> = {
    pending: '⏰',
    paid: '💰',
    shipped: '🚚',
    completed: '✅',
    cancelled: '❌'
  }
  return iconMap[status] || '📦'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待付款',
    paid: '待发货',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || '未知状态'
}

const getStatusDesc = (status: string) => {
  const descMap: Record<string, string> = {
    pending: '请在24小时内完成支付',
    paid: '商家正在准备发货',
    shipped: '商品正在配送中，请耐心等待',
    completed: '订单已完成，感谢您的购买',
    cancelled: '订单已取消'
  }
  return descMap[status] || ''
}

const getAvailableActions = (status: string) => {
  const actionMap: Record<string, Array<{key: string, text: string, type: string}>> = {
    pending: [
      { key: 'cancel', text: '取消订单', type: 'secondary' },
      { key: 'pay', text: '立即支付', type: 'primary' }
    ],
    paid: [
      { key: 'logistics', text: '查看物流', type: 'secondary' }
    ],
    shipped: [
      { key: 'logistics', text: '查看物流', type: 'secondary' },
      { key: 'confirm', text: '确认收货', type: 'primary' }
    ],
    completed: [
      { key: 'evaluate', text: '评价商品', type: 'secondary' },
      { key: 'rebuy', text: '再次购买', type: 'primary' }
    ],
    cancelled: []
  }
  return actionMap[status] || []
}

const formatTime = (timeStr: string) => {
  return timeStr.replace(/:\d{2}$/, '')
}

const viewLogistics = () => {
  uni.navigateTo({
    url: `/pages/logistics/index?orderNumber=${orderInfo.value.orderNumber}`
  })
}

const goToProduct = (product: Product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}`
  })
}

const handleAction = (actionKey: string) => {
  switch (actionKey) {
    case 'cancel':
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            orderInfo.value.status = 'cancelled'
            uni.showToast({
              title: '订单已取消',
              icon: 'success'
            })
          }
        }
      })
      break
    case 'pay':
      uni.navigateTo({
        url: `/pages/payment/index?orderNumber=${orderInfo.value.orderNumber}`
      })
      break
    case 'logistics':
      viewLogistics()
      break
    case 'confirm':
      uni.showModal({
        title: '确认收货',
        content: '确认已收到商品吗？',
        success: (res) => {
          if (res.confirm) {
            orderInfo.value.status = 'completed'
            orderInfo.value.completeTime = new Date().toLocaleString()
            uni.showToast({
              title: '确认收货成功',
              icon: 'success'
            })
          }
        }
      })
      break
    case 'evaluate':
      uni.navigateTo({
        url: `/pages/evaluate/index?orderNumber=${orderInfo.value.orderNumber}`
      })
      break
    case 'rebuy':
      // 重新购买逻辑
      uni.showToast({
        title: '已加入购物车',
        icon: 'success'
      })
      break
  }
}

onLoad((options) => {
  if (options?.orderNumber) {
    // 根据订单号加载订单详情
    console.log('加载订单:', options.orderNumber)
  }
})
</script>

<style lang="scss" scoped>
.order-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.order-status-card {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  
  .status-icon {
    margin-right: 30rpx;
    
    .icon {
      font-size: 60rpx;
    }
  }
  
  .status-info {
    flex: 1;
    
    .status-text {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 8rpx;
    }
    
    .status-desc {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.logistics-card, .address-card, .products-card, .order-info-card, .cost-card {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .track-btn {
      font-size: 24rpx;
      color: #ff6b35;
    }
  }
}

.logistics-info {
  .logistics-company {
    display: block;
    font-size: 26rpx;
    color: #333333;
    margin-bottom: 8rpx;
  }
  
  .logistics-number {
    font-size: 24rpx;
    color: #666666;
  }
}

.address-info {
  .contact-info {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    
    .contact-name {
      font-size: 26rpx;
      color: #333333;
      margin-right: 20rpx;
    }
    
    .contact-phone {
      font-size: 24rpx;
      color: #666666;
    }
  }
  
  .address-detail {
    font-size: 24rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f8f8f8;
    
    &:last-child {
      border-bottom: none;
    }
    
    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }
    
    .product-info {
      flex: 1;
      
      .product-name {
        display: block;
        font-size: 26rpx;
        color: #333333;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .product-spec {
        display: block;
        font-size: 22rpx;
        color: #999999;
        margin-bottom: 12rpx;
      }
      
      .product-price {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .price {
          font-size: 26rpx;
          font-weight: 600;
          color: #ff6b35;
        }
        
        .quantity {
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }
}

.info-list, .cost-list {
  .info-item, .cost-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f8f8f8;
    
    &:last-child {
      border-bottom: none;
    }
    
    &.total {
      border-top: 1rpx solid #f0f0f0;
      margin-top: 16rpx;
      padding-top: 20rpx;
      
      .cost-label, .cost-value {
        font-weight: 600;
        font-size: 28rpx;
      }
    }
    
    .info-label, .cost-label {
      font-size: 24rpx;
      color: #666666;
    }
    
    .info-value, .cost-value {
      font-size: 24rpx;
      color: #333333;
      
      &.discount {
        color: #ff6b35;
      }
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  
  .action-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    
    &.primary {
      background-color: #ff6b35;
      
      .btn-text {
        color: #ffffff;
      }
    }
    
    &.secondary {
      background-color: #f8f8f8;
      border: 1rpx solid #d9d9d9;
      
      .btn-text {
        color: #666666;
      }
    }
    
    .btn-text {
      font-size: 26rpx;
      font-weight: 600;
    }
  }
}
</style>
